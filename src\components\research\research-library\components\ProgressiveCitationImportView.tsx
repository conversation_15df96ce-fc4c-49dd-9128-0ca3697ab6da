import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  BookOpen, 
  Calendar, 
  Users, 
  Search,
  ChevronDown,
  ChevronUp,
  Filter,
  SortAsc,
  SortDesc,
  Check,
  X,
  CheckCircle2,
  Download,
  Upload,
  AlertCircle,
  Loader2,
  FileText,
  ExternalLink,
  Tag,
  Hash,
  Clock,
  BarChart3,
  Trash2,
  Zap,
  ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';

import { ProcessedCitationData } from '../types/citation-import.types';
import { researchLibraryService } from '../services/research-library.service';
import { aiCitationEnhancerService } from '../services/ai-citation-enhancer.service';

interface ProgressiveCitationImportViewProps {
  citations: ProcessedCitationData[];
  libraries: Array<{ id: string; name: string; description: string; article_count: number }>;
  selectedLibrary: { id: string; name: string } | null;
  onSelectLibrary: (library: any) => void;
  onClose?: () => void;
  onImportComplete: () => void;
  user: any;
}

// Processing phases for better UX and performance
enum ProcessingPhase {
  INITIAL_SEPARATION = 'initial_separation',
  AI_ENHANCEMENT = 'ai_enhancement', 
  PROGRESSIVE_FILTERING = 'progressive_filtering',
  FINAL_SELECTION = 'final_selection'
}

interface ProcessingStats {
  total: number;
  withAbstract: number;
  withoutAbstract: number;
  processed: number;
  remaining: number;
}

interface BatchProcessingState {
  isProcessing: boolean;
  currentBatch: number;
  totalBatches: number;
  processedCount: number;
  errors: string[];
}

interface FilterOptions {
  searchTerm: string;
  hasAbstract: 'all' | 'with' | 'without';
  hasDOI: 'all' | 'with' | 'without';
  hasKeywords: 'all' | 'with' | 'without';
  yearRange: { min: number | null; max: number | null };
  documentTypes: string[];
  selectedKeywords: string[];
  journals: string[];
}

export const ProgressiveCitationImportView: React.FC<ProgressiveCitationImportViewProps> = ({
  citations,
  libraries,
  selectedLibrary,
  onSelectLibrary,
  onClose,
  onImportComplete,
  user
}) => {
  // Progressive workflow state
  const [currentPhase, setCurrentPhase] = useState<ProcessingPhase>(ProcessingPhase.INITIAL_SEPARATION);
  const [processingStats, setProcessingStats] = useState<ProcessingStats>({
    total: 0,
    withAbstract: 0,
    withoutAbstract: 0,
    processed: 0,
    remaining: 0
  });
  const [batchProcessing, setBatchProcessing] = useState<BatchProcessingState>({
    isProcessing: false,
    currentBatch: 0,
    totalBatches: 0,
    processedCount: 0,
    errors: []
  });
  
  // Citation collections
  const [citationsWithAbstract, setCitationsWithAbstract] = useState<ProcessedCitationData[]>([]);
  const [citationsWithoutAbstract, setCitationsWithoutAbstract] = useState<ProcessedCitationData[]>([]);
  const [enhancedCitations, setEnhancedCitations] = useState<ProcessedCitationData[]>([]);
  
  // Filtering and selection
  const [filters, setFilters] = useState<FilterOptions>({
    searchTerm: '',
    hasAbstract: 'all',
    hasDOI: 'all',
    hasKeywords: 'all',
    yearRange: { min: null, max: null },
    documentTypes: [],
    selectedKeywords: [],
    journals: []
  });
  const [selectedCitations, setSelectedCitations] = useState<Set<number>>(new Set());
  const [isImporting, setIsImporting] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Initial separation of citations by abstract availability
  useEffect(() => {
    if (citations.length > 0) {
      separateCitationsByAbstract();
    }
  }, [citations]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(filters.searchTerm);
    }, 300);
    return () => clearTimeout(timer);
  }, [filters.searchTerm]);

  /**
   * Separate citations by abstract availability for faster processing
   */
  const separateCitationsByAbstract = useCallback(() => {
    const withAbstract: ProcessedCitationData[] = [];
    const withoutAbstract: ProcessedCitationData[] = [];

    citations.forEach(citation => {
      if (citation.abstract && citation.abstract.trim().length > 0) {
        withAbstract.push(citation);
      } else {
        withoutAbstract.push(citation);
      }
    });

    setCitationsWithAbstract(withAbstract);
    setCitationsWithoutAbstract(withoutAbstract);
    
    setProcessingStats({
      total: citations.length,
      withAbstract: withAbstract.length,
      withoutAbstract: withoutAbstract.length,
      processed: 0,
      remaining: withAbstract.length
    });

    setCurrentPhase(ProcessingPhase.INITIAL_SEPARATION);
  }, [citations]);

  /**
   * Remove citations without abstracts (bulk delete)
   */
  const removeArticlesWithoutAbstract = useCallback(() => {
    setCitationsWithoutAbstract([]);
    setProcessingStats(prev => ({
      ...prev,
      withoutAbstract: 0,
      remaining: prev.withAbstract
    }));
    toast.success(`Removed ${processingStats.withoutAbstract} articles without abstracts`);
  }, [processingStats.withoutAbstract]);

  /**
   * Process citations in batches for better performance
   */
  const processCitationsInBatches = useCallback(async (
    citationsToProcess: ProcessedCitationData[],
    batchSize: number = 10
  ) => {
    const batches = [];
    for (let i = 0; i < citationsToProcess.length; i += batchSize) {
      batches.push(citationsToProcess.slice(i, i + batchSize));
    }

    setBatchProcessing({
      isProcessing: true,
      currentBatch: 0,
      totalBatches: batches.length,
      processedCount: 0,
      errors: []
    });

    const enhancedResults: ProcessedCitationData[] = [];
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      setBatchProcessing(prev => ({
        ...prev,
        currentBatch: i + 1,
        processedCount: i * batchSize
      }));

      try {
        // Process batch with AI enhancement
        const enhancedBatch = await Promise.all(
          batch.map(async (citation) => {
            try {
              const enhanced = await aiCitationEnhancerService.enhanceCitation(citation);
              return enhanced;
            } catch (error) {
              console.warn('Failed to enhance citation:', citation.title, error);
              return citation; // Return original if enhancement fails
            }
          })
        );

        enhancedResults.push(...enhancedBatch);
        
        // Small delay to prevent overwhelming the API
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error('Batch processing error:', error);
        setBatchProcessing(prev => ({
          ...prev,
          errors: [...prev.errors, `Batch ${i + 1} failed: ${error}`]
        }));
      }
    }

    setEnhancedCitations(enhancedResults);
    setBatchProcessing(prev => ({
      ...prev,
      isProcessing: false,
      processedCount: enhancedResults.length
    }));

    setCurrentPhase(ProcessingPhase.PROGRESSIVE_FILTERING);
    toast.success(`Enhanced ${enhancedResults.length} citations with AI`);
  }, []);

  /**
   * Start AI enhancement process
   */
  const startAIEnhancement = useCallback(() => {
    setCurrentPhase(ProcessingPhase.AI_ENHANCEMENT);
    processCitationsInBatches(citationsWithAbstract);
  }, [citationsWithAbstract, processCitationsInBatches]);

  /**
   * Skip AI enhancement and go directly to filtering
   */
  const skipAIEnhancement = useCallback(() => {
    setEnhancedCitations(citationsWithAbstract);
    setCurrentPhase(ProcessingPhase.PROGRESSIVE_FILTERING);
  }, [citationsWithAbstract]);

  // Working citations based on current phase
  const workingCitations = useMemo(() => {
    if (currentPhase === ProcessingPhase.PROGRESSIVE_FILTERING || currentPhase === ProcessingPhase.FINAL_SELECTION) {
      return enhancedCitations.length > 0 ? enhancedCitations : citationsWithAbstract;
    }
    return citations;
  }, [currentPhase, enhancedCitations, citationsWithAbstract, citations]);

  // Filtered citations based on current filters
  const filteredCitations = useMemo(() => {
    if (currentPhase !== ProcessingPhase.PROGRESSIVE_FILTERING && currentPhase !== ProcessingPhase.FINAL_SELECTION) {
      return workingCitations;
    }

    return workingCitations.filter(citation => {
      // Search filter
      if (debouncedSearchTerm) {
        const searchLower = debouncedSearchTerm.toLowerCase();
        const matchesSearch =
          citation.title.toLowerCase().includes(searchLower) ||
          citation.authors.some(author => author.toLowerCase().includes(searchLower)) ||
          (citation.journal && citation.journal.toLowerCase().includes(searchLower)) ||
          (citation.abstract && citation.abstract.toLowerCase().includes(searchLower));

        if (!matchesSearch) return false;
      }

      // Abstract filter
      if (filters.hasAbstract === 'with' && (!citation.abstract || citation.abstract.length === 0)) return false;
      if (filters.hasAbstract === 'without' && citation.abstract && citation.abstract.length > 0) return false;

      // DOI filter
      if (filters.hasDOI === 'with' && (!citation.doi || citation.doi.length === 0)) return false;
      if (filters.hasDOI === 'without' && citation.doi && citation.doi.length > 0) return false;

      // Year range filter
      if (filters.yearRange.min && citation.publicationYear && citation.publicationYear < filters.yearRange.min) return false;
      if (filters.yearRange.max && citation.publicationYear && citation.publicationYear > filters.yearRange.max) return false;

      return true;
    });
  }, [workingCitations, debouncedSearchTerm, filters, currentPhase]);

  /**
   * Import selected citations to the library
   */
  const importSelectedCitations = useCallback(async () => {
    if (!selectedLibrary || selectedCitations.size === 0) {
      toast.error('Please select a library and at least one citation');
      return;
    }

    setIsImporting(true);

    try {
      const selectedCitationData = Array.from(selectedCitations).map(index => filteredCitations[index]);

      const result = await researchLibraryService.addArticlesToLibrary(
        selectedLibrary.id,
        selectedCitationData.map(citation => ({
          title: citation.title,
          authors: citation.authors,
          abstract: citation.abstract || '',
          publication_year: citation.publicationYear,
          journal: citation.journal || '',
          doi: citation.doi || '',
          keywords: citation.keywords,
          document_type: citation.documentType,
          url: citation.url || '',
          pdf_url: citation.pdfUrl || ''
        })),
        user.id
      );

      if (result.success) {
        toast.success(`Successfully imported ${selectedCitations.size} citations`);
        onImportComplete();
      } else {
        toast.error(result.error || 'Failed to import citations');
      }
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import citations');
    } finally {
      setIsImporting(false);
    }
  }, [selectedLibrary, selectedCitations, filteredCitations, user, onImportComplete]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Progressive Citation Import</h2>
          <p className="text-gray-600">
            {currentPhase === ProcessingPhase.INITIAL_SEPARATION && "Analyze and prepare citations for import"}
            {currentPhase === ProcessingPhase.AI_ENHANCEMENT && "Enhancing citations with AI"}
            {(currentPhase === ProcessingPhase.PROGRESSIVE_FILTERING || currentPhase === ProcessingPhase.FINAL_SELECTION) && "Filter and select citations to import"}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
        </div>
      </div>

      {/* Phase Progress Indicator */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-2 ${currentPhase === ProcessingPhase.INITIAL_SEPARATION ? 'text-blue-600' : 'text-gray-400'}`}>
            <BarChart3 className="h-5 w-5" />
            <span className="font-medium">Analyze</span>
          </div>
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <div className={`flex items-center space-x-2 ${currentPhase === ProcessingPhase.AI_ENHANCEMENT ? 'text-purple-600' : 'text-gray-400'}`}>
            <Zap className="h-5 w-5" />
            <span className="font-medium">Enhance</span>
          </div>
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <div className={`flex items-center space-x-2 ${currentPhase === ProcessingPhase.PROGRESSIVE_FILTERING ? 'text-green-600' : 'text-gray-400'}`}>
            <Filter className="h-5 w-5" />
            <span className="font-medium">Filter</span>
          </div>
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <div className={`flex items-center space-x-2 ${currentPhase === ProcessingPhase.FINAL_SELECTION ? 'text-orange-600' : 'text-gray-400'}`}>
            <CheckCircle2 className="h-5 w-5" />
            <span className="font-medium">Select</span>
          </div>
        </div>
      </div>

      {/* Phase Content */}
      {currentPhase === ProcessingPhase.INITIAL_SEPARATION && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <BarChart3 className="h-6 w-6 text-blue-600 mr-3" />
            <h3 className="text-lg font-semibold text-blue-900">Citation Analysis</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 border border-blue-200">
              <div className="text-2xl font-bold text-gray-900">{processingStats.total}</div>
              <div className="text-sm text-gray-600">Total Citations</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <div className="text-2xl font-bold text-green-700">{processingStats.withAbstract}</div>
              <div className="text-sm text-green-600">With Abstract</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <div className="text-2xl font-bold text-red-700">{processingStats.withoutAbstract}</div>
              <div className="text-sm text-red-600">Without Abstract</div>
            </div>
          </div>

          <div className="space-y-4">
            <p className="text-gray-700">
              We found <strong>{processingStats.withAbstract}</strong> citations with abstracts and{' '}
              <strong>{processingStats.withoutAbstract}</strong> without abstracts.
            </p>

            {processingStats.withoutAbstract > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                  <span className="font-medium text-yellow-800">Recommendation</span>
                </div>
                <p className="text-yellow-700 mb-3">
                  Citations without abstracts are less useful for research. Consider removing them to focus on high-quality articles.
                </p>
                <Button
                  variant="outline"
                  onClick={removeArticlesWithoutAbstract}
                  className="border-red-300 text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove {processingStats.withoutAbstract} Articles Without Abstracts
                </Button>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                onClick={startAIEnhancement}
                disabled={processingStats.withAbstract === 0}
                className="flex-1"
              >
                <Zap className="h-4 w-4 mr-2" />
                Enhance {processingStats.withAbstract} Citations with AI
              </Button>
              <Button
                variant="outline"
                onClick={skipAIEnhancement}
              >
                Skip Enhancement
              </Button>
            </div>
          </div>
        </div>
      )}

      {currentPhase === ProcessingPhase.AI_ENHANCEMENT && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Loader2 className="h-6 w-6 text-purple-600 mr-3 animate-spin" />
            <h3 className="text-lg font-semibold text-purple-900">AI Enhancement in Progress</h3>
          </div>

          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border border-purple-200">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Processing Batch {batchProcessing.currentBatch} of {batchProcessing.totalBatches}
                </span>
                <span className="text-sm text-gray-500">
                  {batchProcessing.processedCount} / {processingStats.withAbstract} completed
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(batchProcessing.processedCount / processingStats.withAbstract) * 100}%`
                  }}
                />
              </div>
            </div>

            <p className="text-gray-700">
              Enhancing citations with AI to improve keyword extraction and metadata quality...
            </p>

            {batchProcessing.errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">Processing Errors:</h4>
                <ul className="text-sm text-red-700 space-y-1">
                  {batchProcessing.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {(currentPhase === ProcessingPhase.PROGRESSIVE_FILTERING || currentPhase === ProcessingPhase.FINAL_SELECTION) && (
        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircle2 className="h-5 w-5 text-green-600 mr-2" />
                <span className="font-medium text-green-800">
                  Ready to filter {workingCitations.length} citations
                </span>
              </div>
              <Badge variant="secondary">
                {enhancedCitations.length > 0 ? 'AI Enhanced' : 'Basic Processing'}
              </Badge>
            </div>
          </div>

          {/* Quick Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Quick Filters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search titles, authors..."
                      value={filters.searchTerm}
                      onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Year Range</label>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      placeholder="From"
                      value={filters.yearRange.min || ''}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        yearRange: { ...prev.yearRange, min: e.target.value ? parseInt(e.target.value) : null }
                      }))}
                      className="w-20"
                    />
                    <Input
                      type="number"
                      placeholder="To"
                      value={filters.yearRange.max || ''}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        yearRange: { ...prev.yearRange, max: e.target.value ? parseInt(e.target.value) : null }
                      }))}
                      className="w-20"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Abstract</label>
                  <Select
                    value={filters.hasAbstract}
                    onValueChange={(value: 'all' | 'with' | 'without') =>
                      setFilters(prev => ({ ...prev, hasAbstract: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Articles</SelectItem>
                      <SelectItem value="with">With Abstract</SelectItem>
                      <SelectItem value="without">Without Abstract</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">DOI</label>
                  <Select
                    value={filters.hasDOI}
                    onValueChange={(value: 'all' | 'with' | 'without') =>
                      setFilters(prev => ({ ...prev, hasDOI: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Articles</SelectItem>
                      <SelectItem value="with">With DOI</SelectItem>
                      <SelectItem value="without">Without DOI</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results and Import */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {filteredCitations.length} citations • {selectedCitations.size} selected
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  const allIndices = new Set(filteredCitations.map((_, index) => index));
                  setSelectedCitations(allIndices);
                }}
              >
                Select All
              </Button>
              <Button
                onClick={() => setSelectedCitations(new Set())}
                variant="outline"
              >
                Clear Selection
              </Button>
              <Button
                onClick={importSelectedCitations}
                disabled={selectedCitations.size === 0 || !selectedLibrary || isImporting}
              >
                {isImporting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                Import {selectedCitations.size} Citations
              </Button>
            </div>
          </div>

          {/* Citations List */}
          <div className="space-y-3">
            {filteredCitations.map((citation, index) => {
              const isSelected = selectedCitations.has(index);

              return (
                <Card
                  key={index}
                  className={`transition-all cursor-pointer ${
                    isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'
                  }`}
                  onClick={() => {
                    const newSelected = new Set(selectedCitations);
                    if (newSelected.has(index)) {
                      newSelected.delete(index);
                    } else {
                      newSelected.add(index);
                    }
                    setSelectedCitations(newSelected);
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        checked={isSelected}
                        onChange={() => {}} // Handled by card click
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 mb-1 line-clamp-2">
                          {citation.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {citation.authors.join(', ')}
                        </p>
                        {citation.journal && (
                          <p className="text-sm text-gray-500 mb-2">
                            {citation.journal} {citation.publicationYear && `(${citation.publicationYear})`}
                          </p>
                        )}
                        {citation.abstract && (
                          <p className="text-sm text-gray-700 line-clamp-2 mb-2">
                            {citation.abstract}
                          </p>
                        )}
                        <div className="flex items-center space-x-2">
                          {citation.doi && (
                            <Badge variant="secondary" className="text-xs">
                              DOI
                            </Badge>
                          )}
                          {citation.keywords.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {citation.keywords.length} keywords
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {citation.documentType}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredCitations.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No citations found</h3>
              <p className="text-gray-600">Try adjusting your filters to see more results.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
