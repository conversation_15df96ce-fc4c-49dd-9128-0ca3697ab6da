// Enhanced Generation Components
export { EnhancedInputPanel } from './EnhancedInputPanel';
export { MultiImageUpload } from './MultiImageUpload';

// Types for enhanced generation
export interface EnhancedInputs {
  prompt: string;
  requirements: string;
  keyPoints: string[];
  writingStyle: string;
  targetWordCount: number;
  specificInstructions: string;
}

export interface UploadedImage {
  id: string;
  file: File;
  url: string;
  base64: string;
  title: string;
  caption: string;
  analysis?: string;
  isAnalyzing: boolean;
}
