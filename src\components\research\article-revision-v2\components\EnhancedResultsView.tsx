/**
 * Enhanced Results View - Full Page Beautiful Results Display
 * Dedicated page for showing Response to Reviews results with proper formatting
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Download,
  CheckCircle,
  Eye,
  Edit3,
  MessageSquare,
  ArrowLeft,
  FileDown,
  Sparkles,
  Star,
  Clock,
  Target,
  BookOpen,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { EnhancedResponseResult } from '../services/enhanced-response-ai.service';
import { enhancedExportService } from '../services/enhanced-response-export.service';

interface EnhancedResultsViewProps {
  result: EnhancedResponseResult;
  onBack: () => void;
}

export function EnhancedResultsView({ result, onBack }: EnhancedResultsViewProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['revised-article']));

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Export functions with enhanced formatting
  const exportRevisedArticle = async (format: 'docx' | 'pdf') => {
    const title = 'Revised Article - Response to Reviews';
    const content = result.step3_outputs?.revisedArticle || 'No content available';
    const filename = `revised-article-${new Date().toISOString().split('T')[0]}`;

    try {
      if (format === 'docx') {
        await enhancedExportService.exportEnhancedDocx(title, content, filename);
      } else {
        await enhancedExportService.exportEnhancedPdf(title, content, filename);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const exportChangeSummary = async (format: 'docx' | 'pdf') => {
    const title = 'Change Summary - Response to Reviews';
    const content = result.step3_outputs?.changeSummary || 'No content available';
    const filename = `change-summary-${new Date().toISOString().split('T')[0]}`;

    try {
      if (format === 'docx') {
        await enhancedExportService.exportEnhancedDocx(title, content, filename);
      } else {
        await enhancedExportService.exportEnhancedPdf(title, content, filename);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const exportResponseLetter = async (format: 'docx' | 'pdf') => {
    const title = 'Response Letter to Reviewers';
    const content = result.step3_outputs?.responseLetter || 'No content available';
    const filename = `response-letter-${new Date().toISOString().split('T')[0]}`;

    try {
      if (format === 'docx') {
        await enhancedExportService.exportEnhancedDocx(title, content, filename);
      } else {
        await enhancedExportService.exportEnhancedPdf(title, content, filename);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Format content for display with proper markdown rendering
  const formatContent = (content: string) => {
    const htmlContent = enhancedExportService.markdownToHtml(content);
    return (
      <div 
        className="prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
        style={{
          lineHeight: '1.8',
          fontSize: '16px'
        }}
      />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Input
            </Button>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">
                Enhanced Response Results
              </h1>
              <p className="text-lg text-gray-600 mt-2">
                Comprehensive AI-generated response to peer reviews
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-green-700 border-green-300">
              <CheckCircle className="h-4 w-4 mr-1" />
              Analysis Complete
            </Badge>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {result.step1_commentAnalysis?.totalComments || 0}
                </div>
                <div className="text-sm font-medium text-blue-700">Comments Analyzed</div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-purple-200 bg-purple-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {result.step2_revisedArticle?.overallChangesSummary?.sectionsModified || 0}
                </div>
                <div className="text-sm font-medium text-purple-700">Sections Revised</div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {result.processingMetadata.qualityScore}
                </div>
                <div className="text-sm font-medium text-green-700">Quality Score</div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {Math.round(result.processingMetadata.totalProcessingTime / 1000)}s
                </div>
                <div className="text-sm font-medium text-orange-700">Processing Time</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="overview" className="text-lg py-3">
              <Eye className="h-5 w-5 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="revised-article" className="text-lg py-3">
              <FileText className="h-5 w-5 mr-2" />
              Revised Article
            </TabsTrigger>
            <TabsTrigger value="change-summary" className="text-lg py-3">
              <Edit3 className="h-5 w-5 mr-2" />
              Change Summary
            </TabsTrigger>
            <TabsTrigger value="response-letter" className="text-lg py-3">
              <MessageSquare className="h-5 w-5 mr-2" />
              Response Letter
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="border-gray-200 shadow-lg">
              <CardHeader className="bg-gray-50">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <Sparkles className="h-6 w-6" />
                  Analysis Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Comment Analysis</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Total Comments:</span>
                        <span className="font-medium">{result.step1_commentAnalysis?.totalComments || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Major Issues:</span>
                        <span className="font-medium text-red-600">
                          {Array.isArray(result.step1_commentAnalysis?.categorizedComments)
                            ? result.step1_commentAnalysis.categorizedComments.filter(c => c.severity === 'major').length
                            : 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Minor Issues:</span>
                        <span className="font-medium text-yellow-600">
                          {Array.isArray(result.step1_commentAnalysis?.categorizedComments)
                            ? result.step1_commentAnalysis.categorizedComments.filter(c => c.severity === 'minor').length
                            : 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Suggestions:</span>
                        <span className="font-medium text-blue-600">
                          {Array.isArray(result.step1_commentAnalysis?.categorizedComments)
                            ? result.step1_commentAnalysis.categorizedComments.filter(c => c.severity === 'suggestion').length
                            : 0}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Revision Summary</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Sections Modified:</span>
                        <span className="font-medium">{result.step2_revisedArticle?.overallChangesSummary?.sectionsModified || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Changes:</span>
                        <span className="font-medium">{result.step2_revisedArticle?.overallChangesSummary?.totalChanges || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Quality Score:</span>
                        <span className="font-medium text-green-600">{result.processingMetadata?.qualityScore || 0}/10</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Revised Article Tab */}
          <TabsContent value="revised-article" className="space-y-6">
            <Card className="border-blue-200 shadow-lg">
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-blue-900">
                    <FileText className="h-6 w-6" />
                    Revised Article with Changes Highlighted
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => exportRevisedArticle('docx')}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export DOCX
                    </Button>
                    <Button
                      onClick={() => exportRevisedArticle('pdf')}
                      variant="outline"
                      className="border-blue-300 text-blue-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="bg-white border border-blue-200 rounded-lg p-8 max-h-[800px] overflow-y-auto">
                  {formatContent(result.step3_outputs?.revisedArticle || 'No content available')}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Change Summary Tab */}
          <TabsContent value="change-summary" className="space-y-6">
            <Card className="border-green-200 shadow-lg">
              <CardHeader className="bg-green-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-green-900">
                    <Edit3 className="h-6 w-6" />
                    Detailed Change Summary
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => exportChangeSummary('docx')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export DOCX
                    </Button>
                    <Button
                      onClick={() => exportChangeSummary('pdf')}
                      variant="outline"
                      className="border-green-300 text-green-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="bg-white border border-green-200 rounded-lg p-8 max-h-[800px] overflow-y-auto">
                  {formatContent(result.step3_outputs?.changeSummary || 'No content available')}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Response Letter Tab */}
          <TabsContent value="response-letter" className="space-y-6">
            <Card className="border-purple-200 shadow-lg">
              <CardHeader className="bg-purple-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-purple-900">
                    <MessageSquare className="h-6 w-6" />
                    Professional Response Letter
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => exportResponseLetter('docx')}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export DOCX
                    </Button>
                    <Button
                      onClick={() => exportResponseLetter('pdf')}
                      variant="outline"
                      className="border-purple-300 text-purple-700"
                    >
                      <FileDown className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="bg-white border border-purple-200 rounded-lg p-8 max-h-[800px] overflow-y-auto">
                  {formatContent(result.step3_outputs?.responseLetter || 'No content available')}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
