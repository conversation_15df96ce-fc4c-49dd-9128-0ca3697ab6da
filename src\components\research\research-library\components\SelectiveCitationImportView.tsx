/**
 * Selective Citation Import View Component
 * Enhanced preview with filtering, selection, and targeted library import
 */

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  FileText,
  Users,
  Calendar,
  BookOpen,
  ExternalLink,
  Tag,
  Hash,
  Globe,
  Search,
  ChevronDown,
  ChevronUp,
  Filter,
  SortAsc,
  SortDesc,
  Check,
  X,
  Plus,
  Save,
  Trash2,
  AlertCircle,
  CheckCircle2,
  Sparkles,
  RefreshCw,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

import { ProcessedCitationData } from '../types/citation-import.types';
import { researchLibraryService } from '../services/research-library.service';
import { citationPersistenceService, ImportedCitationSession } from '../services/citation-persistence.service';
import { aiCitationEnhancerService } from '../services/ai-citation-enhancer.service';

interface SelectiveCitationImportViewProps {
  citations: ProcessedCitationData[];
  libraries: Array<{ id: string; name: string; description: string; article_count: number }>;
  selectedLibrary: { id: string; name: string } | null;
  onSelectLibrary: (library: any) => void;
  onClose?: () => void;
  onImportComplete: () => void;
  user: any;
}

// Processing phases for better UX and performance
enum ProcessingPhase {
  INITIAL_SEPARATION = 'initial_separation',
  AI_ENHANCEMENT = 'ai_enhancement',
  PROGRESSIVE_FILTERING = 'progressive_filtering',
  FINAL_SELECTION = 'final_selection'
}

interface ProcessingStats {
  total: number;
  withAbstract: number;
  withoutAbstract: number;
  processed: number;
  remaining: number;
}

interface BatchProcessingState {
  isProcessing: boolean;
  currentBatch: number;
  totalBatches: number;
  processedCount: number;
  errors: string[];
}

interface FilterOptions {
  searchTerm: string;
  hasAbstract: 'all' | 'with' | 'without';
  hasDOI: 'all' | 'with' | 'without';
  hasKeywords: 'all' | 'with' | 'without';
  yearRange: { min: number | null; max: number | null };
  documentTypes: string[];
  selectedKeywords: string[];
}

export function SelectiveCitationImportView({ 
  citations, 
  libraries, 
  selectedLibrary, 
  onSelectLibrary, 
  onClose, 
  onImportComplete,
  user 
}: SelectiveCitationImportViewProps) {
  const [selectedCitations, setSelectedCitations] = useState<Set<number>>(new Set());
  const [expandedCitations, setExpandedCitations] = useState<Set<number>>(new Set());
  const [isImporting, setIsImporting] = useState(false);
  const [sortBy, setSortBy] = useState<'title' | 'author' | 'year' | 'journal'>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [keywordSearchTerm, setKeywordSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isFiltering, setIsFiltering] = useState(false);

  // Progressive workflow state
  const [currentPhase, setCurrentPhase] = useState<ProcessingPhase>(ProcessingPhase.INITIAL_SEPARATION);
  const [processingStats, setProcessingStats] = useState<ProcessingStats>({
    total: 0,
    withAbstract: 0,
    withoutAbstract: 0,
    processed: 0,
    remaining: 0
  });
  const [batchProcessing, setBatchProcessing] = useState<BatchProcessingState>({
    isProcessing: false,
    currentBatch: 0,
    totalBatches: 0,
    processedCount: 0,
    errors: []
  });
  const [citationsWithAbstract, setCitationsWithAbstract] = useState<ProcessedCitationData[]>([]);
  const [citationsWithoutAbstract, setCitationsWithoutAbstract] = useState<ProcessedCitationData[]>([]);
  const [enhancedCitations, setEnhancedCitations] = useState<ProcessedCitationData[]>([]);

  // AI Enhancement state
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementProgress, setEnhancementProgress] = useState({ current: 0, total: 0 });
  const [useAIEnhancement, setUseAIEnhancement] = useState(true);

  // Persistence state
  const [currentSession, setCurrentSession] = useState<ImportedCitationSession | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(false);

  const [filters, setFilters] = useState<FilterOptions>({
    searchTerm: '',
    hasAbstract: 'all',
    hasDOI: 'all',
    hasKeywords: 'all',
    yearRange: { min: null, max: null },
    documentTypes: [],
    selectedKeywords: []
  });

  // Initial separation of citations by abstract availability
  useEffect(() => {
    if (citations.length > 0) {
      separateCitationsByAbstract();
    }
  }, [citations]);

  // Debounce search term to improve performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(filters.searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [filters.searchTerm]);

  // Load existing sessions on component mount
  useEffect(() => {
    if (user && selectedLibrary) {
      loadExistingSessions();
    }
  }, [user, selectedLibrary]);

  /**
   * Separate citations by abstract availability for faster processing
   */
  const separateCitationsByAbstract = useCallback(() => {
    const withAbstract: ProcessedCitationData[] = [];
    const withoutAbstract: ProcessedCitationData[] = [];

    citations.forEach(citation => {
      if (citation.abstract && citation.abstract.trim().length > 0) {
        withAbstract.push(citation);
      } else {
        withoutAbstract.push(citation);
      }
    });

    setCitationsWithAbstract(withAbstract);
    setCitationsWithoutAbstract(withoutAbstract);

    setProcessingStats({
      total: citations.length,
      withAbstract: withAbstract.length,
      withoutAbstract: withoutAbstract.length,
      processed: 0,
      remaining: withAbstract.length
    });

    // Start with the separation phase
    setCurrentPhase(ProcessingPhase.INITIAL_SEPARATION);
  }, [citations]);

  /**
   * Remove citations without abstracts (bulk delete)
   */
  const removeArticlesWithoutAbstract = useCallback(() => {
    setCitationsWithoutAbstract([]);
    setProcessingStats(prev => ({
      ...prev,
      withoutAbstract: 0,
      remaining: prev.withAbstract
    }));
    toast.success(`Removed ${processingStats.withoutAbstract} articles without abstracts`);
  }, [processingStats.withoutAbstract]);

  /**
   * Process citations in batches for better performance
   */
  const processCitationsInBatches = useCallback(async (
    citationsToProcess: ProcessedCitationData[],
    batchSize: number = 10
  ) => {
    const batches = [];
    for (let i = 0; i < citationsToProcess.length; i += batchSize) {
      batches.push(citationsToProcess.slice(i, i + batchSize));
    }

    setBatchProcessing({
      isProcessing: true,
      currentBatch: 0,
      totalBatches: batches.length,
      processedCount: 0,
      errors: []
    });

    const enhancedResults: ProcessedCitationData[] = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];

      setBatchProcessing(prev => ({
        ...prev,
        currentBatch: i + 1,
        processedCount: i * batchSize
      }));

      try {
        // Process batch with AI enhancement
        const enhancedBatch = await Promise.all(
          batch.map(async (citation) => {
            try {
              const enhanced = await aiCitationEnhancerService.enhanceCitation(citation);
              return enhanced;
            } catch (error) {
              console.warn('Failed to enhance citation:', citation.title, error);
              return citation; // Return original if enhancement fails
            }
          })
        );

        enhancedResults.push(...enhancedBatch);

        // Small delay to prevent overwhelming the API
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error('Batch processing error:', error);
        setBatchProcessing(prev => ({
          ...prev,
          errors: [...prev.errors, `Batch ${i + 1} failed: ${error}`]
        }));
      }
    }

    setEnhancedCitations(enhancedResults);
    setBatchProcessing(prev => ({
      ...prev,
      isProcessing: false,
      processedCount: enhancedResults.length
    }));

    setCurrentPhase(ProcessingPhase.PROGRESSIVE_FILTERING);
    toast.success(`Enhanced ${enhancedResults.length} citations with AI`);
  }, []);

  /**
   * Start AI enhancement process
   */
  const startAIEnhancement = useCallback(() => {
    setCurrentPhase(ProcessingPhase.AI_ENHANCEMENT);
    processCitationsInBatches(citationsWithAbstract);
  }, [citationsWithAbstract, processCitationsInBatches]);

  // Use enhanced citations if available, otherwise use original
  const workingCitations = enhancedCitations.length > 0 ? enhancedCitations : citations;

  // Extract all unique keywords and document types for filtering
  const allKeywords = useMemo(() => {
    const keywordSet = new Set<string>();
    workingCitations.forEach(citation => {
      citation.keywords.forEach(keyword => keywordSet.add(keyword));
    });
    return Array.from(keywordSet).sort();
  }, [workingCitations]);

  // Filter keywords based on search term
  const filteredKeywords = useMemo(() => {
    if (!keywordSearchTerm.trim()) return allKeywords;
    return allKeywords.filter(keyword =>
      keyword.toLowerCase().includes(keywordSearchTerm.toLowerCase())
    );
  }, [allKeywords, keywordSearchTerm]);

  const allDocumentTypes = useMemo(() => {
    const typeSet = new Set<string>();
    workingCitations.forEach(citation => {
      typeSet.add(citation.documentType);
    });
    return Array.from(typeSet).sort();
  }, [workingCitations]);

  // Optimized filtering with performance improvements
  const filteredAndSortedCitations = useMemo(() => {
    setIsFiltering(true);

    // Pre-compile search terms for better performance
    const searchLower = debouncedSearchTerm.toLowerCase();
    const hasSearchTerm = searchLower.length > 0;
    const hasKeywordFilter = filters.selectedKeywords.length > 0;
    const hasDocumentTypeFilter = filters.documentTypes.length > 0;
    const hasYearFilter = filters.yearRange.min !== null || filters.yearRange.max !== null;

    let filtered = workingCitations.filter((citation) => {
      // Early returns for performance

      // Search filter (using debounced term)
      if (hasSearchTerm) {
        const matchesSearch =
          citation.title.toLowerCase().includes(searchLower) ||
          citation.authors.some(author => author.toLowerCase().includes(searchLower)) ||
          (citation.journal && citation.journal.toLowerCase().includes(searchLower)) ||
          (citation.abstract && citation.abstract.toLowerCase().includes(searchLower)) ||
          citation.keywords.some(keyword => keyword.toLowerCase().includes(searchLower));

        if (!matchesSearch) return false;
      }

      // Abstract filter
      if (filters.hasAbstract === 'with' && (!citation.abstract || citation.abstract.length === 0)) return false;
      if (filters.hasAbstract === 'without' && citation.abstract && citation.abstract.length > 0) return false;

      // DOI filter
      if (filters.hasDOI === 'with' && (!citation.doi || citation.doi.length === 0)) return false;
      if (filters.hasDOI === 'without' && citation.doi && citation.doi.length > 0) return false;

      // Keywords filter
      if (filters.hasKeywords === 'with' && citation.keywords.length === 0) return false;
      if (filters.hasKeywords === 'without' && citation.keywords.length > 0) return false;

      // Year range filter (optimized)
      if (hasYearFilter && citation.publicationYear) {
        if (filters.yearRange.min && citation.publicationYear < filters.yearRange.min) return false;
        if (filters.yearRange.max && citation.publicationYear > filters.yearRange.max) return false;
      }

      // Document type filter (optimized)
      if (hasDocumentTypeFilter && !filters.documentTypes.includes(citation.documentType)) return false;

      // Selected keywords filter (optimized)
      if (hasKeywordFilter) {
        const hasSelectedKeyword = filters.selectedKeywords.some(keyword =>
          citation.keywords.includes(keyword)
        );
        if (!hasSelectedKeyword) return false;
      }

      return true;
    });

    // Sort citations
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title || '';
          bValue = b.title || '';
          break;
        case 'author':
          aValue = a.authors[0] || '';
          bValue = b.authors[0] || '';
          break;
        case 'year':
          aValue = a.publicationYear || 0;
          bValue = b.publicationYear || 0;
          break;
        case 'journal':
          aValue = a.journal || '';
          bValue = b.journal || '';
          break;
        default:
          return 0;
      }
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    const result = filtered.map((citation) => ({
      citation,
      originalIndex: workingCitations.indexOf(citation)
    }));

    setIsFiltering(false);
    return result;
  }, [workingCitations, filters, debouncedSearchTerm, sortBy, sortOrder]);

  const toggleCitationSelection = (index: number) => {
    const newSelected = new Set(selectedCitations);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedCitations(newSelected);
  };

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedCitations);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedCitations(newExpanded);
  };

  const selectAll = () => {
    const allIndices = new Set(filteredAndSortedCitations.map(item => item.originalIndex));
    setSelectedCitations(allIndices);
  };

  const selectNone = () => {
    setSelectedCitations(new Set());
  };

  // Load existing import sessions
  const loadExistingSessions = async () => {
    if (!user || !selectedLibrary) return;

    setIsLoadingSession(true);
    try {
      const result = await citationPersistenceService.getLibraryImportSessions(user.id, selectedLibrary.id);
      if (result.success && result.sessions && result.sessions.length > 0) {
        // Load the most recent session
        const latestSession = result.sessions[0];
        setCurrentSession(latestSession);

        // If the session has citations, use them
        if (latestSession.citations && latestSession.citations.length > 0) {
          setEnhancedCitations(latestSession.citations);
          toast.success(`Loaded ${latestSession.citations.length} citations from previous session`);
        }
      }
    } catch (error) {
      console.error('Failed to load existing sessions:', error);
    } finally {
      setIsLoadingSession(false);
    }
  };

  // Enhance citations with AI
  const enhanceCitationsWithAI = async () => {
    if (citations.length === 0) {
      toast.error('No citations to enhance');
      return;
    }

    setIsEnhancing(true);
    setEnhancementProgress({ current: 0, total: citations.length });

    try {
      toast.info('Starting AI enhancement of citations...');

      // Create a new session for this enhancement
      const sessionName = `AI Enhanced Import - ${new Date().toLocaleString()}`;
      const sessionResult = await citationPersistenceService.createImportSession(
        user!.id,
        selectedLibrary!.id,
        sessionName,
        citations
      );

      if (!sessionResult.success) {
        throw new Error(sessionResult.error || 'Failed to create import session');
      }

      // Enhance citations using AI
      const enhancedResults = await aiCitationEnhancerService.enhanceCitations(
        citations.map(citation => ({
          title: citation.title,
          authors: citation.authors,
          abstract: citation.abstract,
          keywords: citation.keywords,
          journal: citation.journal,
          publicationYear: citation.publicationYear,
          doi: citation.doi
        })),
        (current, total) => {
          setEnhancementProgress({ current, total });
        }
      );

      // Apply enhancements to citations
      const enhanced = citations.map((citation, index) => {
        const enhancement = enhancedResults[index];
        return {
          ...citation,
          keywords: [...new Set([...enhancement.cleanedKeywords, ...enhancement.extractedKeywords])],
          tags: enhancement.suggestedTags,
          processingConfidence: enhancement.confidence,
          processingWarnings: enhancement.missingFields.map(field => `Missing ${field}`)
        };
      });

      setEnhancedCitations(enhanced);

      // Update the session with enhanced citations
      if (sessionResult.sessionId) {
        await citationPersistenceService.updateImportProgress(sessionResult.sessionId, {
          current: enhanced.length,
          total: enhanced.length,
          status: 'completed'
        });
      }

      toast.success(`Successfully enhanced ${enhanced.length} citations with AI`);
    } catch (error: any) {
      console.error('AI enhancement failed:', error);
      toast.error(`AI enhancement failed: ${error.message}`);
    } finally {
      setIsEnhancing(false);
      setEnhancementProgress({ current: 0, total: 0 });
    }
  };

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const importSelectedCitations = async () => {
    if (!selectedLibrary || selectedCitations.size === 0) {
      toast.error('Please select a library and at least one citation');
      return;
    }

    setIsImporting(true);

    try {
      // Get selected citations
      const selectedCitationData = Array.from(selectedCitations).map(index => workingCitations[index]);

      // Create import session
      const sessionName = `Manual Import - ${new Date().toLocaleString()}`;
      const sessionResult = await citationPersistenceService.createImportSession(
        user!.id,
        selectedLibrary.id,
        sessionName,
        selectedCitationData
      );

      if (!sessionResult.success) {
        throw new Error(sessionResult.error || 'Failed to create import session');
      }

      // Save citations to library
      const importResult = await citationPersistenceService.saveCitationsToLibrary(
        sessionResult.sessionId!,
        selectedCitationData,
        user!.id,
        selectedLibrary.id,
        (current, total) => {
          // Update progress if needed
        }
      );

      if (importResult.success) {
        const successCount = importResult.importedIds?.length || 0;
        const errorCount = (importResult.errors?.length || 0);

        toast.success(`Successfully imported ${successCount} citations${errorCount > 0 ? ` (${errorCount} errors)` : ''}`);

        if (onImportComplete) {
          onImportComplete();
        }
      } else {
        throw new Error('Import failed');
      }

      // Clear selections after successful import
      setSelectedCitations(new Set());

    } catch (error: any) {
      console.error('Import error:', error);
      toast.error('Import failed: ' + error.message);
    } finally {
      setIsImporting(false);
    }
  };

  const getDocumentTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'journal-article': 'bg-blue-100 text-blue-800',
      'conference-paper': 'bg-green-100 text-green-800',
      'book': 'bg-purple-100 text-purple-800',
      'book-chapter': 'bg-purple-100 text-purple-800',
      'thesis': 'bg-orange-100 text-orange-800',
      'report': 'bg-gray-100 text-gray-800',
      'webpage': 'bg-cyan-100 text-cyan-800',
      'preprint': 'bg-yellow-100 text-yellow-800',
      'other': 'bg-gray-100 text-gray-800'
    };
    return colors[type] || colors.other;
  };

  const formatDocumentType = (type: string) => {
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Select Citations to Import</h2>
          <p className="text-gray-600">
            {filteredAndSortedCitations.length} of {citations.length} citations • {selectedCitations.size} selected
          </p>
        </div>
        <div className="flex space-x-2">
          {onClose && (
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Library Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Target Library</CardTitle>
        </CardHeader>
        <CardContent>
          {selectedLibrary ? (
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div>
                <h3 className="font-medium text-blue-900">{selectedLibrary.name}</h3>
                <p className="text-sm text-blue-700">Selected for import</p>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onSelectLibrary(null)}
              >
                Change Library
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {libraries.map((library) => (
                <Card 
                  key={library.id} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onSelectLibrary(library)}
                >
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900">{library.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{library.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{library.article_count} articles</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedLibrary && (
        <>
          {/* AI Enhancement Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="h-5 w-5 mr-2" />
                AI Enhancement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Enhance Citations with AI</h3>
                    <p className="text-sm text-gray-600">
                      Use AI to clean keywords, extract metadata, and improve citation quality
                    </p>
                  </div>
                  <Button
                    onClick={enhanceCitationsWithAI}
                    disabled={isEnhancing || citations.length === 0}
                    className="flex items-center"
                  >
                    {isEnhancing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Enhancing...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Enhance Citations
                      </>
                    )}
                  </Button>
                </div>

                {isEnhancing && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Processing citations...</span>
                      <span>{enhancementProgress.current} / {enhancementProgress.total}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${enhancementProgress.total > 0 ? (enhancementProgress.current / enhancementProgress.total) * 100 : 0}%`
                        }}
                      />
                    </div>
                  </div>
                )}

                {enhancedCitations.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <CheckCircle2 className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-green-800">
                        {enhancedCitations.length} citations enhanced with AI
                      </span>
                    </div>
                  </div>
                )}

                {isLoadingSession && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="text-sm text-blue-800">
                        Loading previous session...
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Advanced Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Advanced Filters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search and Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  {isFiltering && (
                    <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-500 animate-spin" />
                  )}
                  <Input
                    placeholder="Search citations..."
                    value={filters.searchTerm}
                    onChange={(e) => updateFilter('searchTerm', e.target.value)}
                    className={`pl-10 ${isFiltering ? 'pr-10' : ''}`}
                  />
                </div>

                <Select value={filters.hasAbstract} onValueChange={(value: any) => updateFilter('hasAbstract', value)}>
                  <SelectTrigger className="relative z-10">
                    <SelectValue placeholder="Abstract filter" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    <SelectItem value="all">All Citations</SelectItem>
                    <SelectItem value="with">With Abstract</SelectItem>
                    <SelectItem value="without">Without Abstract</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.hasDOI} onValueChange={(value: any) => updateFilter('hasDOI', value)}>
                  <SelectTrigger className="relative z-10">
                    <SelectValue placeholder="DOI filter" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    <SelectItem value="all">All Citations</SelectItem>
                    <SelectItem value="with">With DOI</SelectItem>
                    <SelectItem value="without">Without DOI</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.hasKeywords} onValueChange={(value: any) => updateFilter('hasKeywords', value)}>
                  <SelectTrigger className="relative z-10">
                    <SelectValue placeholder="Keywords filter" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    <SelectItem value="all">All Citations</SelectItem>
                    <SelectItem value="with">With Keywords</SelectItem>
                    <SelectItem value="without">Without Keywords</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Year Range Filter */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Year Range</label>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      placeholder="Min year"
                      value={filters.yearRange.min || ''}
                      onChange={(e) => updateFilter('yearRange', { 
                        ...filters.yearRange, 
                        min: e.target.value ? parseInt(e.target.value) : null 
                      })}
                    />
                    <Input
                      type="number"
                      placeholder="Max year"
                      value={filters.yearRange.max || ''}
                      onChange={(e) => updateFilter('yearRange', { 
                        ...filters.yearRange, 
                        max: e.target.value ? parseInt(e.target.value) : null 
                      })}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sort & Order</label>
                  <div className="flex space-x-2">
                    <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                      <SelectTrigger className="relative z-10">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="z-50">
                        <SelectItem value="title">Title</SelectItem>
                        <SelectItem value="author">Author</SelectItem>
                        <SelectItem value="year">Year</SelectItem>
                        <SelectItem value="journal">Journal</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="px-3"
                    >
                      {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Document Types Filter */}
              {allDocumentTypes.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Document Types</label>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-2 border border-gray-200 rounded-lg bg-gray-50">
                    {allDocumentTypes.map(type => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          checked={filters.documentTypes.includes(type)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilter('documentTypes', [...filters.documentTypes, type]);
                            } else {
                              updateFilter('documentTypes', filters.documentTypes.filter(t => t !== type));
                            }
                          }}
                        />
                        <Badge className={getDocumentTypeColor(type)}>
                          {formatDocumentType(type)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Keywords Filter */}
              {allKeywords.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Keywords</label>

                  {/* Keyword Search */}
                  <div className="mb-3">
                    <Input
                      type="text"
                      placeholder="Search keywords..."
                      value={keywordSearchTerm}
                      onChange={(e) => setKeywordSearchTerm(e.target.value)}
                      className="text-sm"
                    />
                  </div>

                  {/* Keywords List */}
                  <div className="max-h-64 overflow-y-auto p-3 border border-gray-200 rounded-lg bg-gray-50 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    <div className="flex flex-wrap gap-2">
                      {filteredKeywords.slice(0, 100).map(keyword => (
                        <div key={keyword} className="flex items-center space-x-2">
                          <Checkbox
                            checked={filters.selectedKeywords.includes(keyword)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                updateFilter('selectedKeywords', [...filters.selectedKeywords, keyword]);
                              } else {
                                updateFilter('selectedKeywords', filters.selectedKeywords.filter(k => k !== keyword));
                              }
                            }}
                          />
                          <Badge variant="outline" className="text-xs">
                            {keyword}
                          </Badge>
                        </div>
                      ))}
                    </div>

                    {/* Show count information */}
                    <div className="mt-3 pt-2 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        Showing {Math.min(filteredKeywords.length, 100)} of {filteredKeywords.length} keywords
                        {keywordSearchTerm && ` (filtered from ${allKeywords.length} total)`}
                      </p>
                      {filters.selectedKeywords.length > 0 && (
                        <p className="text-xs text-blue-600 mt-1">
                          {filters.selectedKeywords.length} keywords selected
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selection Controls */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button onClick={selectAll} variant="outline" size="sm">
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Select All ({filteredAndSortedCitations.length})
                  </Button>
                  <Button onClick={selectNone} variant="outline" size="sm">
                    <X className="h-4 w-4 mr-2" />
                    Select None
                  </Button>
                </div>

                <Button
                  onClick={importSelectedCitations}
                  disabled={selectedCitations.size === 0 || isImporting}
                  className="flex items-center space-x-2"
                >
                  {isImporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      <span>Import {selectedCitations.size} Citations</span>
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Citations List */}
          <div className="space-y-4">
            {filteredAndSortedCitations.map(({ citation, originalIndex }) => {
              const isSelected = selectedCitations.has(originalIndex);
              const isExpanded = expandedCitations.has(originalIndex);

              return (
                <Card
                  key={originalIndex}
                  className={`transition-all ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'}`}
                >
                  <CardContent className="p-6">
                    {/* Header with Selection */}
                    <div className="flex items-start space-x-4">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleCitationSelection(originalIndex)}
                        className="mt-1"
                      />

                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                              {citation.title || 'Untitled'}
                            </h3>

                            <div className="flex flex-wrap items-center gap-2 mb-3">
                              {/* Authors */}
                              <div className="flex items-center text-sm text-gray-600">
                                <Users className="h-4 w-4 mr-1" />
                                <span>{citation.authors.length > 0 ? citation.authors.join(', ') : 'Unknown Authors'}</span>
                              </div>

                              {/* Year */}
                              {citation.publicationYear && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  <span>{citation.publicationYear}</span>
                                </div>
                              )}

                              {/* Document Type */}
                              <Badge className={getDocumentTypeColor(citation.documentType)}>
                                {formatDocumentType(citation.documentType)}
                              </Badge>
                            </div>

                            {/* Journal/Source */}
                            {citation.journal && (
                              <div className="flex items-center text-sm text-gray-600 mb-2">
                                <BookOpen className="h-4 w-4 mr-1" />
                                <span className="font-medium">{citation.journal}</span>
                                {citation.volume && <span className="ml-2">Vol. {citation.volume}</span>}
                                {citation.issue && <span className="ml-1">({citation.issue})</span>}
                                {citation.pages && <span className="ml-2">pp. {citation.pages}</span>}
                              </div>
                            )}
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleExpanded(originalIndex)}
                            className="ml-4"
                          >
                            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </Button>
                        </div>

                        {/* Quick Info */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {citation.doi && (
                            <Badge variant="outline" className="text-xs">
                              <Hash className="h-3 w-3 mr-1" />
                              DOI
                            </Badge>
                          )}
                          {citation.abstract && (
                            <Badge variant="outline" className="text-xs">
                              <FileText className="h-3 w-3 mr-1" />
                              Abstract
                            </Badge>
                          )}
                          {citation.keywords.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              {citation.keywords.length} Keywords
                            </Badge>
                          )}
                          {citation.url && (
                            <Badge variant="outline" className="text-xs">
                              <Globe className="h-3 w-3 mr-1" />
                              URL
                            </Badge>
                          )}
                        </div>

                        {/* Expanded Content */}
                        {isExpanded && (
                          <div className="border-t pt-4 space-y-4">
                            {/* Abstract */}
                            {citation.abstract && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                                  <FileText className="h-4 w-4 mr-2" />
                                  Abstract
                                </h4>
                                <p className="text-sm text-gray-700 leading-relaxed bg-gray-50 p-3 rounded-lg">
                                  {citation.abstract}
                                </p>
                              </div>
                            )}

                            {/* Keywords */}
                            {citation.keywords.length > 0 && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                                  <Tag className="h-4 w-4 mr-2" />
                                  Keywords
                                </h4>
                                <div className="flex flex-wrap gap-2">
                                  {citation.keywords.map((keyword, keywordIndex) => (
                                    <Badge key={keywordIndex} variant="secondary" className="text-xs">
                                      {keyword}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Additional Details */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {/* DOI */}
                              {citation.doi && (
                                <div>
                                  <h4 className="font-medium text-gray-900 mb-1 text-sm">DOI</h4>
                                  <a
                                    href={`https://doi.org/${citation.doi}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                                  >
                                    {citation.doi}
                                    <ExternalLink className="h-3 w-3 ml-1" />
                                  </a>
                                </div>
                              )}

                              {/* URL */}
                              {citation.url && (
                                <div>
                                  <h4 className="font-medium text-gray-900 mb-1 text-sm">URL</h4>
                                  <a
                                    href={citation.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center truncate"
                                  >
                                    {citation.url}
                                    <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                                  </a>
                                </div>
                              )}
                            </div>

                            {/* Notes */}
                            {citation.notes && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2 text-sm">Notes</h4>
                                <p className="text-sm text-gray-700 bg-yellow-50 p-3 rounded-lg">
                                  {citation.notes}
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {filteredAndSortedCitations.filter(({ citation }) => citation.abstract && citation.abstract.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With Abstracts</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {filteredAndSortedCitations.filter(({ citation }) => citation.doi && citation.doi.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With DOI</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {filteredAndSortedCitations.filter(({ citation }) => citation.keywords.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With Keywords</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {selectedCitations.size}
                  </div>
                  <div className="text-sm text-gray-600">Selected</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

export default SelectiveCitationImportView;
