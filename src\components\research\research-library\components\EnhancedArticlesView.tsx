/**
 * Enhanced Articles View - Advanced filtering, selection, and management
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  FileText,
  Search,
  Filter,
  Star,
  ExternalLink,
  Calendar,
  Users,
  Eye,
  Trash2,
  BookOpen,
  Tag,
  Download,
  CheckCircle2,
  X,
  AlertTriangle,
  Hash,
  Globe,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

import { ArticleDetailView } from './ArticleDetailView';
import { researchLibraryService, LibraryArticle } from '../services/research-library.service';

interface ResearchLibrary {
  id: string;
  name: string;
  description: string;
  article_count: number;
  created_at: string;
  updated_at: string;
}

interface FilterOptions {
  searchTerm: string;
  hasAbstract: 'all' | 'with' | 'without';
  hasDOI: 'all' | 'with' | 'without';
  hasKeywords: 'all' | 'with' | 'without';
  isFavorite: 'all' | 'favorites' | 'non-favorites';
  yearRange: { min: number | null; max: number | null };
  selectedKeywords: string[];
  selectedTags: string[];
}

interface EnhancedArticlesViewProps {
  articles: LibraryArticle[];
  selectedLibrary: ResearchLibrary | null;
  libraries: ResearchLibrary[];
  onSelectLibrary: (library: ResearchLibrary) => void;
  onArticleDeleted?: () => void;
  onArticlesUpdated?: () => void;
}

export function EnhancedArticlesView({
  articles,
  selectedLibrary,
  libraries,
  onSelectLibrary,
  onArticleDeleted,
  onArticlesUpdated
}: EnhancedArticlesViewProps) {
  const [selectedArticles, setSelectedArticles] = useState<Set<string>>(new Set());
  const [selectedArticle, setSelectedArticle] = useState<LibraryArticle | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [sortBy, setSortBy] = useState<'title' | 'author' | 'year' | 'created'>('created');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  const [filters, setFilters] = useState<FilterOptions>({
    searchTerm: '',
    hasAbstract: 'all',
    hasDOI: 'all',
    hasKeywords: 'all',
    isFavorite: 'all',
    yearRange: { min: null, max: null },
    selectedKeywords: [],
    selectedTags: []
  });

  // Extract all unique keywords and tags for filtering
  const allKeywords = useMemo(() => {
    const keywordSet = new Set<string>();
    articles.forEach(article => {
      if (article.keywords) {
        article.keywords.forEach(keyword => keywordSet.add(keyword));
      }
    });
    return Array.from(keywordSet).sort();
  }, [articles]);

  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    articles.forEach(article => {
      if (article.tags) {
        article.tags.forEach(tag => tagSet.add(tag));
      }
    });
    return Array.from(tagSet).sort();
  }, [articles]);

  // Filter and sort articles
  const filteredAndSortedArticles = useMemo(() => {
    let filtered = articles.filter(article => {
      // Search filter
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const matchesSearch = 
          article.title?.toLowerCase().includes(searchLower) ||
          article.authors?.some(author => author.toLowerCase().includes(searchLower)) ||
          article.journal?.toLowerCase().includes(searchLower) ||
          article.abstract?.toLowerCase().includes(searchLower) ||
          article.keywords?.some(keyword => keyword.toLowerCase().includes(searchLower)) ||
          article.tags?.some(tag => tag.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // Abstract filter
      if (filters.hasAbstract === 'with' && (!article.abstract || article.abstract.length === 0)) return false;
      if (filters.hasAbstract === 'without' && article.abstract && article.abstract.length > 0) return false;

      // DOI filter
      if (filters.hasDOI === 'with' && (!article.doi || article.doi.length === 0)) return false;
      if (filters.hasDOI === 'without' && article.doi && article.doi.length > 0) return false;

      // Keywords filter
      if (filters.hasKeywords === 'with' && (!article.keywords || article.keywords.length === 0)) return false;
      if (filters.hasKeywords === 'without' && article.keywords && article.keywords.length > 0) return false;

      // Favorite filter
      if (filters.isFavorite === 'favorites' && !article.favorite) return false;
      if (filters.isFavorite === 'non-favorites' && article.favorite) return false;

      // Year range filter
      if (filters.yearRange.min && article.publication_year && article.publication_year < filters.yearRange.min) return false;
      if (filters.yearRange.max && article.publication_year && article.publication_year > filters.yearRange.max) return false;

      // Selected keywords filter
      if (filters.selectedKeywords.length > 0) {
        const hasSelectedKeyword = filters.selectedKeywords.some(keyword => 
          article.keywords?.includes(keyword)
        );
        if (!hasSelectedKeyword) return false;
      }

      // Selected tags filter
      if (filters.selectedTags.length > 0) {
        const hasSelectedTag = filters.selectedTags.some(tag => 
          article.tags?.includes(tag)
        );
        if (!hasSelectedTag) return false;
      }

      return true;
    });

    // Sort articles
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title || '';
          bValue = b.title || '';
          break;
        case 'author':
          aValue = a.authors?.[0] || '';
          bValue = b.authors?.[0] || '';
          break;
        case 'year':
          aValue = a.publication_year || 0;
          bValue = b.publication_year || 0;
          break;
        case 'created':
          aValue = new Date(a.created_at || 0).getTime();
          bValue = new Date(b.created_at || 0).getTime();
          break;
        default:
          return 0;
      }
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [articles, filters, sortBy, sortOrder]);

  const toggleArticleSelection = (articleId: string) => {
    const newSelected = new Set(selectedArticles);
    if (newSelected.has(articleId)) {
      newSelected.delete(articleId);
    } else {
      newSelected.add(articleId);
    }
    setSelectedArticles(newSelected);
  };

  const selectAll = () => {
    const allIds = new Set(filteredAndSortedArticles.map(article => article.id));
    setSelectedArticles(allIds);
  };

  const selectNone = () => {
    setSelectedArticles(new Set());
  };

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const deleteSelectedArticles = async () => {
    if (selectedArticles.size === 0) {
      toast.error('No articles selected');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedArticles.size} selected articles? This action cannot be undone.`
    );

    if (!confirmed) return;

    setIsDeleting(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const articleId of selectedArticles) {
        try {
          const result = await researchLibraryService.deleteArticle(articleId);
          if (result.success) {
            successCount++;
          } else {
            errorCount++;
            console.error('Failed to delete article:', result.error);
          }
        } catch (error) {
          errorCount++;
          console.error('Error deleting article:', error);
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} articles`);
        setSelectedArticles(new Set());
        onArticleDeleted?.();
        onArticlesUpdated?.();
      }

      if (errorCount > 0) {
        toast.error(`Failed to delete ${errorCount} articles`);
      }

    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error('Delete failed: ' + error.message);
    } finally {
      setIsDeleting(false);
    }
  };

  const toggleFavorite = async (article: LibraryArticle) => {
    try {
      const result = await researchLibraryService.updateArticle(article.id, {
        favorite: !article.favorite
      });

      if (result.success) {
        toast.success(article.favorite ? 'Removed from favorites' : 'Added to favorites');
        onArticlesUpdated?.();
      } else {
        toast.error('Failed to update favorite status');
      }
    } catch (error) {
      console.error('Error updating favorite:', error);
      toast.error('Failed to update favorite status');
    }
  };

  // Clear selections when library changes
  useEffect(() => {
    setSelectedArticles(new Set());
  }, [selectedLibrary?.id]);

  if (selectedArticle) {
    return (
      <ArticleDetailView
        article={selectedArticle}
        onBack={() => setSelectedArticle(null)}
        onDelete={() => {
          setSelectedArticle(null);
          onArticleDeleted?.();
        }}
        onUpdate={() => {
          onArticlesUpdated?.();
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {selectedLibrary ? selectedLibrary.name : 'All Articles'}
          </h2>
          <p className="text-gray-600">
            {filteredAndSortedArticles.length} of {articles.length} articles
            {selectedArticles.size > 0 && ` • ${selectedArticles.size} selected`}
          </p>
        </div>
      </div>

      {/* Library Selection */}
      {!selectedLibrary && (
        <Card>
          <CardHeader>
            <CardTitle>Select Library</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {libraries.map((library) => (
                <Card 
                  key={library.id} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onSelectLibrary(library)}
                >
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900">{library.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{library.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{library.article_count} articles</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {selectedLibrary && (
        <>
          {/* Advanced Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Filters & Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search and Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search articles..."
                    value={filters.searchTerm}
                    onChange={(e) => updateFilter('searchTerm', e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={filters.hasAbstract} onValueChange={(value: any) => updateFilter('hasAbstract', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Abstract filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Articles</SelectItem>
                    <SelectItem value="with">With Abstract</SelectItem>
                    <SelectItem value="without">Without Abstract</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.hasDOI} onValueChange={(value: any) => updateFilter('hasDOI', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="DOI filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Articles</SelectItem>
                    <SelectItem value="with">With DOI</SelectItem>
                    <SelectItem value="without">Without DOI</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.isFavorite} onValueChange={(value: any) => updateFilter('isFavorite', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Favorites filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Articles</SelectItem>
                    <SelectItem value="favorites">Favorites Only</SelectItem>
                    <SelectItem value="non-favorites">Non-Favorites</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Year Range and Sort */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Year Range</label>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      placeholder="Min year"
                      value={filters.yearRange.min || ''}
                      onChange={(e) => updateFilter('yearRange', { 
                        ...filters.yearRange, 
                        min: e.target.value ? parseInt(e.target.value) : null 
                      })}
                    />
                    <Input
                      type="number"
                      placeholder="Max year"
                      value={filters.yearRange.max || ''}
                      onChange={(e) => updateFilter('yearRange', { 
                        ...filters.yearRange, 
                        max: e.target.value ? parseInt(e.target.value) : null 
                      })}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sort & Order</label>
                  <div className="flex space-x-2">
                    <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="title">Title</SelectItem>
                        <SelectItem value="author">Author</SelectItem>
                        <SelectItem value="year">Year</SelectItem>
                        <SelectItem value="created">Date Added</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="px-3"
                    >
                      {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Keywords Filter */}
              {allKeywords.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Keywords</label>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {allKeywords.slice(0, 30).map(keyword => (
                      <div key={keyword} className="flex items-center space-x-2">
                        <Checkbox
                          checked={filters.selectedKeywords.includes(keyword)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilter('selectedKeywords', [...filters.selectedKeywords, keyword]);
                            } else {
                              updateFilter('selectedKeywords', filters.selectedKeywords.filter(k => k !== keyword));
                            }
                          }}
                        />
                        <Badge variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      </div>
                    ))}
                    {allKeywords.length > 30 && (
                      <p className="text-xs text-gray-500">... and {allKeywords.length - 30} more</p>
                    )}
                  </div>
                </div>
              )}

              {/* Tags Filter */}
              {allTags.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Tags</label>
                  <div className="flex flex-wrap gap-2">
                    {allTags.map(tag => (
                      <div key={tag} className="flex items-center space-x-2">
                        <Checkbox
                          checked={filters.selectedTags.includes(tag)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilter('selectedTags', [...filters.selectedTags, tag]);
                            } else {
                              updateFilter('selectedTags', filters.selectedTags.filter(t => t !== tag));
                            }
                          }}
                        />
                        <Badge variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selection Controls */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button onClick={selectAll} variant="outline" size="sm">
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Select All ({filteredAndSortedArticles.length})
                  </Button>
                  <Button onClick={selectNone} variant="outline" size="sm">
                    <X className="h-4 w-4 mr-2" />
                    Select None
                  </Button>
                </div>

                {selectedArticles.size > 0 && (
                  <Button
                    onClick={deleteSelectedArticles}
                    disabled={isDeleting}
                    variant="destructive"
                    className="flex items-center space-x-2"
                  >
                    {isDeleting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Deleting...</span>
                      </>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4" />
                        <span>Delete {selectedArticles.size} Articles</span>
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Articles List */}
          <div className="space-y-4">
            {filteredAndSortedArticles.map((article) => {
              const isSelected = selectedArticles.has(article.id);

              return (
                <Card
                  key={article.id}
                  className={`transition-all ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'}`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleArticleSelection(article.id)}
                        className="mt-1"
                      />

                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                              {article.title || 'Untitled'}
                            </h3>

                            <div className="flex flex-wrap items-center gap-2 mb-3">
                              {/* Authors */}
                              {article.authors && article.authors.length > 0 && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <Users className="h-4 w-4 mr-1" />
                                  <span>{article.authors.join(', ')}</span>
                                </div>
                              )}

                              {/* Year */}
                              {article.publication_year && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  <span>{article.publication_year}</span>
                                </div>
                              )}

                              {/* Favorite */}
                              {article.favorite && (
                                <Badge className="bg-yellow-100 text-yellow-800">
                                  <Star className="h-3 w-3 mr-1" />
                                  Favorite
                                </Badge>
                              )}
                            </div>

                            {/* Journal/Source */}
                            {article.journal && (
                              <div className="flex items-center text-sm text-gray-600 mb-2">
                                <BookOpen className="h-4 w-4 mr-1" />
                                <span className="font-medium">{article.journal}</span>
                                {article.volume && <span className="ml-2">Vol. {article.volume}</span>}
                                {article.issue && <span className="ml-1">({article.issue})</span>}
                                {article.pages && <span className="ml-2">pp. {article.pages}</span>}
                              </div>
                            )}

                            {/* Abstract Preview */}
                            {article.abstract && (
                              <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                                {article.abstract}
                              </p>
                            )}

                            {/* Quick Info */}
                            <div className="flex flex-wrap gap-2 mb-3">
                              {article.doi && (
                                <Badge variant="outline" className="text-xs">
                                  <Hash className="h-3 w-3 mr-1" />
                                  DOI
                                </Badge>
                              )}
                              {article.keywords && article.keywords.length > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <Tag className="h-3 w-3 mr-1" />
                                  {article.keywords.length} Keywords
                                </Badge>
                              )}
                              {article.source_url && (
                                <Badge variant="outline" className="text-xs">
                                  <Globe className="h-3 w-3 mr-1" />
                                  URL
                                </Badge>
                              )}
                            </div>

                            {/* Keywords */}
                            {article.keywords && article.keywords.length > 0 && (
                              <div className="flex flex-wrap gap-1 mb-3">
                                {article.keywords.slice(0, 5).map((keyword, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {keyword}
                                  </Badge>
                                ))}
                                {article.keywords.length > 5 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{article.keywords.length - 5} more
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleFavorite(article)}
                              className={article.favorite ? 'text-yellow-600' : 'text-gray-400'}
                            >
                              <Star className={`h-4 w-4 ${article.favorite ? 'fill-current' : ''}`} />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedArticle(article)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>

                            {article.doi && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`https://doi.org/${article.doi}`, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {filteredAndSortedArticles.filter(a => a.abstract && a.abstract.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With Abstracts</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {filteredAndSortedArticles.filter(a => a.doi && a.doi.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With DOI</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {filteredAndSortedArticles.filter(a => a.keywords && a.keywords.length > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">With Keywords</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {filteredAndSortedArticles.filter(a => a.favorite).length}
                  </div>
                  <div className="text-sm text-gray-600">Favorites</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

export default EnhancedArticlesView;
