/**
 * Enhanced Methodology Generator Component
 * Two-step methodology generation: outline first, then detailed content with research
 */

import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Sparkles, 
  RefreshCw, 
  Check, 
  X, 
  Lightbulb,
  ChevronDown,
  ChevronUp,
  FileText,
  Search,
  ArrowRight,
  Clock,
  Microscope,
  Layers,
  Copy,
  Plus,
  FlaskConical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

import { paperAIServiceV2 } from '../services/paper-ai-v2.service';
import { Paper, GenerationOptions } from '../types';
import { ReferencesManager } from './ReferencesManager';

interface EnhancedMethodologyGeneratorProps {
  paper: Paper;
  selectedModel: string;
  onContentSelect: (content: string) => void;
  onClose: () => void;
  initialDeepMode?: boolean; // Allow parent to control deep mode
  onReferencesUpdate?: (references: any[]) => void;
  onSourcesUpdate?: (sources: any[]) => void;
}

interface GenerationStep {
  id: string;
  name: string;
  description: string;
  completed: boolean;
  inProgress: boolean;
}

export const EnhancedMethodologyGenerator: React.FC<EnhancedMethodologyGeneratorProps> = ({
  paper,
  selectedModel,
  onContentSelect,
  onClose,
  initialDeepMode = false,
  onReferencesUpdate,
  onSourcesUpdate
}) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [outline, setOutline] = useState('');
  const [content, setContent] = useState('');
  const [citations, setCitations] = useState<any[]>([]);
  const [sources, setSources] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [confidence, setConfidence] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [deepMode, setDeepMode] = useState(initialDeepMode);

  const getSteps = (): GenerationStep[] => {
    if (deepMode) {
      return [
        {
          id: 'context',
          name: 'Context Analysis',
          description: 'Analyzing methodology complexity to determine optimal structure',
          completed: false,
          inProgress: false
        },
        {
          id: 'research',
          name: 'Dynamic Research',
          description: 'AI-determined comprehensive search based on methodology requirements',
          completed: false,
          inProgress: false
        },
        {
          id: 'outline',
          name: 'Intelligent Outline',
          description: 'Creating adaptive outline with AI-determined section count',
          completed: false,
          inProgress: false
        },
        {
          id: 'content',
          name: 'Enhanced Content',
          description: 'Writing detailed methodology with comprehensive citations',
          completed: false,
          inProgress: false
        }
      ];
    } else {
      return [
        {
          id: 'research',
          name: 'Research & Analysis',
          description: 'Searching academic sources and analyzing context',
          completed: false,
          inProgress: false
        },
        {
          id: 'outline',
          name: 'Outline Generation',
          description: 'Creating structured methodology outline',
          completed: false,
          inProgress: false
        },
        {
          id: 'content',
          name: 'Content Generation',
          description: 'Writing detailed methodology with citations',
          completed: false,
          inProgress: false
        }
      ];
    }
  };

  const [generationSteps, setGenerationSteps] = useState(getSteps());

  // Get context from paper sections
  const titleSection = paper.sections.find(s => s.type === 'title');
  const abstractSection = paper.sections.find(s => s.type === 'abstract');
  const introductionSection = paper.sections.find(s => s.type === 'introduction');
  const resultsSection = paper.sections.find(s => s.type === 'results');
  
  const hasContext = Boolean(titleSection?.content || abstractSection?.content || introductionSection?.content);

  // Update step status
  const updateStepStatus = (stepIndex: number, inProgress: boolean, completed: boolean = false) => {
    setGenerationSteps(prev => prev.map((step, index) => ({
      ...step,
      inProgress: index === stepIndex ? inProgress : false,
      completed: index === stepIndex ? completed : step.completed
    })));
  };

  // Generate enhanced methodology
  const generateMethodology = async () => {
    if (!paperAIServiceV2.isConfigured() && !paperAIServiceV2.isDemoMode()) {
      toast.error('AI service not configured. Please set up your API keys.');
      return;
    }

    setIsGenerating(true);
    setCurrentStep(0);
    
    // Reset steps based on current mode
    setGenerationSteps(getSteps());

    try {
      const options: GenerationOptions = {
        model: selectedModel,
        temperature: 0.7,
        maxTokens: deepMode ? 6144 : 4096,
        includeContext: true,
        includeCitations: true
      };

      if (deepMode) {
        // Deep Mode: 4-step process
        // Step 1: Context Analysis
        updateStepStatus(0, true);
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Step 2: Deep Research
        setCurrentStep(1);
        updateStepStatus(0, false, true);
        updateStepStatus(1, true);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Step 3: Enhanced Outline
        setCurrentStep(2);
        updateStepStatus(1, false, true);
        updateStepStatus(2, true);
        
        // Step 4: Content Generation
        setCurrentStep(3);
        updateStepStatus(2, false, true);
        updateStepStatus(3, true);
      } else {
        // Standard Mode: 3-step process
        // Step 1: Research & Analysis
        updateStepStatus(0, true);
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Step 2: Outline Generation
        setCurrentStep(1);
        updateStepStatus(0, false, true);
        updateStepStatus(1, true);

        // Step 3: Content Generation
        setCurrentStep(2);
        updateStepStatus(1, false, true);
        updateStepStatus(2, true);
      }

      const result = await paperAIServiceV2.generateEnhancedMethodology({
        paper,
        userPrompt: userPrompt.trim() || undefined,
        options,
        deepMode
      });

      // Update final step
      const finalStepIndex = deepMode ? 3 : 2;
      if (currentStep !== finalStepIndex) {
        setCurrentStep(finalStepIndex);
        updateStepStatus(finalStepIndex - 1, false, true);
        updateStepStatus(finalStepIndex, true);
      }

      // Set results
      setOutline(result.outline);
      setContent(result.content);
      setCitations(result.citations);
      setSources(result.sources);
      setConfidence(result.confidence);

      // Update global references and sources
      if (onReferencesUpdate && result.citations && result.citations.length > 0) {
        const references = result.citations.map(citation => ({
          id: citation.id || `ref-${Math.random()}`,
          title: citation.text || 'AI Generated Reference',
          authors: ['AI Knowledge Base'],
          year: new Date().getFullYear(),
          source: 'AI Knowledge Base',
          url: citation.url,
          doi: undefined,
          abstract: undefined,
          formattedCitation: citation.text,
          isVerified: false,
          confidence: result.confidence || 0.6,
          sectionUsed: ['methodology']
        }));
        onReferencesUpdate(references);
      }

      if (onSourcesUpdate && result.sources && result.sources.length > 0) {
        onSourcesUpdate(result.sources);
      }

      updateStepStatus(finalStepIndex, false, true);

      const modeText = deepMode ? 'comprehensive deep mode' : 'standard mode';
      toast.success(`Generated methodology in ${modeText} with ${result.citations.length} citations from ${result.sources.length} sources`);

    } catch (error) {
      console.error('Methodology generation failed:', error);
      toast.error(`Failed to generate methodology: ${error.message}`);
      
      // Reset step status on error
      setGenerationSteps(getSteps());
    } finally {
      setIsGenerating(false);
    }
  };

  // Confirm selection
  const confirmSelection = () => {
    onContentSelect(content);
    toast.success('Methodology content selected');
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <FlaskConical className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <CardTitle className="text-xl">Enhanced Methodology Generator</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Generate comprehensive methodology with academic citations
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Context Information */}
        <div className="bg-gray-50 p-4 rounded-lg space-y-3">
          <h3 className="font-medium">Available Context</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Title</label>
              <div className="text-sm text-gray-600 bg-white p-2 rounded border">
                {titleSection?.content || 'Not available'}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-1 block">Research Field</label>
              <div className="text-sm text-gray-600 bg-white p-2 rounded border">
                {paper.metadata.researchField || 'Not specified'}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-1 block">Keywords</label>
              <div className="text-sm text-gray-600 bg-white p-2 rounded border">
                {paper.metadata.keywords.join(', ') || 'Not specified'}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-1 block">Available Sections</label>
              <div className="flex flex-wrap gap-1">
                {[titleSection, abstractSection, introductionSection, resultsSection]
                  .filter(Boolean)
                  .map(section => (
                    <Badge key={section!.type} variant="outline" className="text-xs">
                      {section!.type}
                    </Badge>
                  ))}
                {!hasContext && (
                  <span className="text-xs text-gray-500">No sections available</span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* User Input */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Methodology Requirements (optional)
            </label>
            <Textarea
              value={userPrompt}
              onChange={(e) => setUserPrompt(e.target.value)}
              placeholder="Describe specific methodological approaches, data collection methods, analysis techniques, or any special requirements for your methodology section..."
              className="min-h-[100px]"
            />
          </div>

          {/* Advanced Options */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2"
          >
            {showAdvanced ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            Advanced Options
          </Button>

          {showAdvanced && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              {/* Deep Mode Toggle */}
              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Microscope className="h-4 w-4 text-green-600" />
                    <Label htmlFor="deep-mode-methodology" className="font-medium">Deep Mode</Label>
                  </div>
                  <p className="text-sm text-gray-600">
                    AI determines optimal methodology structure with dynamic section count and comprehensive research
                  </p>
                </div>
                <Switch
                  id="deep-mode-methodology"
                  checked={deepMode}
                  onCheckedChange={setDeepMode}
                />
              </div>

              <div className="text-sm text-gray-600">
                <p><strong>{deepMode ? 'Deep Mode Process:' : 'Standard Process:'}</strong></p>
                {deepMode ? (
                  <ol className="list-decimal list-inside space-y-1 mt-2">
                    <li>Analyze paper context, title, and figures</li>
                    <li>Perform comprehensive research for each methodology section</li>
                    <li>Generate enhanced outline with section-specific sources</li>
                    <li>Write detailed methodology with verified citations</li>
                  </ol>
                ) : (
                  <ol className="list-decimal list-inside space-y-1 mt-2">
                    <li>Research academic sources using Tavily API</li>
                    <li>Generate structured methodology outline</li>
                    <li>Write detailed methodology with citations</li>
                  </ol>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Generation Button */}
        <Button
          onClick={generateMethodology}
          disabled={isGenerating}
          className="w-full h-12 text-base font-medium"
          size="lg"
        >
          {isGenerating ? (
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 animate-spin" />
              Generating Methodology...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Generate Enhanced Methodology
            </div>
          )}
        </Button>

        {/* Generation Progress */}
        {isGenerating && (
          <div className="space-y-4">
            <div className="space-y-3">
              {generationSteps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step.completed
                      ? 'bg-green-100 text-green-600'
                      : step.inProgress
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-400'
                  }`}>
                    {step.completed ? (
                      <Check className="h-4 w-4" />
                    ) : step.inProgress ? (
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{step.name}</div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                </div>
              ))}
            </div>
            <Progress value={(currentStep / (generationSteps.length - 1)) * 100} className="w-full" />
          </div>
        )}

        {/* Results */}
        {(outline || content) && (
          <div className="space-y-4">
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="content" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Content
                </TabsTrigger>
                <TabsTrigger value="outline" className="flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Outline
                </TabsTrigger>
                <TabsTrigger value="sources" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Sources ({sources.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4">
                <div className="bg-white border rounded-lg p-4 max-h-96 overflow-y-auto">
                  <div className="prose prose-sm max-w-none">
                    {content ? (
                      <div className="whitespace-pre-wrap">{content}</div>
                    ) : (
                      <div className="text-gray-500 italic">Content will appear here after generation</div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{content.split(/\s+/).filter(word => word.length > 0).length} words</span>
                  <span>{citations.length} citations</span>
                </div>
              </TabsContent>

              <TabsContent value="outline" className="space-y-4">
                <div className="bg-white border rounded-lg p-4 max-h-96 overflow-y-auto">
                  <div className="prose prose-sm max-w-none">
                    {outline ? (
                      <div className="whitespace-pre-wrap">{outline}</div>
                    ) : (
                      <div className="text-gray-500 italic">Outline will appear here after generation</div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="sources" className="space-y-4">
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {sources.length > 0 ? (
                    sources.map((source, index) => (
                      <Card key={index} className="border-l-4 border-l-green-500">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="flex items-start justify-between">
                              <h4 className="font-medium text-sm">{source.title}</h4>
                              <Badge variant="outline" className="text-xs">
                                {source.type}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600">{source.snippet}</p>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <span>{source.domain}</span>
                              {source.publishedDate && (
                                <>
                                  <span>•</span>
                                  <span>{source.publishedDate}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="text-gray-500 italic text-center py-8">
                      Sources will appear here after generation
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>

            {/* References Manager */}
            {sources.length > 0 && (
              <div className="mt-6">
                <ReferencesManager
                  references={sources.map(source => ({
                    id: source.id || `source-${Math.random()}`,
                    title: source.title || 'Untitled',
                    authors: source.authors || ['Unknown Author'],
                    year: source.year || new Date().getFullYear(),
                    source: source.domain || source.url || 'Unknown Source',
                    url: source.url,
                    doi: source.doi,
                    abstract: source.snippet,
                    formattedCitation: source.formattedCitation
                  }))}
                  onAddToEditor={(referencesText) => {
                    // Add references to the content
                    const updatedContent = content + '\n\n' + referencesText;
                    setContent(updatedContent);
                    toast.success('References added to content');
                  }}
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  Confidence: {Math.round(confidence * 100)}%
                </Badge>
                {deepMode && (
                  <Badge variant="secondary" className="text-xs flex items-center gap-1">
                    <Microscope className="h-3 w-3" />
                    Deep Mode
                  </Badge>
                )}
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>

                <Button
                  onClick={confirmSelection}
                  disabled={!content.trim()}
                  className="flex items-center gap-2"
                >
                  <Check className="h-4 w-4" />
                  Use Generated Methodology
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Help Text */}
        {!hasContext && !outline && !content && (
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-800">
              <strong>Tip:</strong> For better methodology generation, consider adding a title, abstract,
              or introduction section first. The AI will use this context to create more relevant methodological approaches.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
