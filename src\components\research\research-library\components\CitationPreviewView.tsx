/**
 * Citation Preview View Component
 * Displays detailed preview of parsed citations for literature review preparation
 */

import React, { useState } from 'react';
import { 
  FileText, 
  Users, 
  Calendar, 
  BookOpen, 
  ExternalLink, 
  Tag, 
  Hash,
  Globe,
  Search,
  ChevronDown,
  ChevronUp,
  Eye,
  Filter,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { ProcessedCitationData } from '../types/citation-import.types';

interface CitationPreviewViewProps {
  citations: ProcessedCitationData[];
  onClose?: () => void;
}

export function CitationPreviewView({ citations, onClose }: CitationPreviewViewProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'author' | 'year' | 'journal'>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterBy, setFilterBy] = useState<'all' | 'with-abstract' | 'with-doi' | 'recent'>('all');
  const [expandedCitations, setExpandedCitations] = useState<Set<number>>(new Set());

  // Filter and sort citations
  const filteredAndSortedCitations = React.useMemo(() => {
    let filtered = citations.filter(citation => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = 
          citation.title.toLowerCase().includes(searchLower) ||
          citation.authors.some(author => author.toLowerCase().includes(searchLower)) ||
          citation.journal?.toLowerCase().includes(searchLower) ||
          citation.abstract?.toLowerCase().includes(searchLower) ||
          citation.keywords.some(keyword => keyword.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // Content filter
      switch (filterBy) {
        case 'with-abstract':
          return citation.abstract && citation.abstract.length > 0;
        case 'with-doi':
          return citation.doi && citation.doi.length > 0;
        case 'recent':
          return citation.publicationYear && citation.publicationYear >= 2020;
        default:
          return true;
      }
    });

    // Sort citations
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title || '';
          bValue = b.title || '';
          break;
        case 'author':
          aValue = a.authors[0] || '';
          bValue = b.authors[0] || '';
          break;
        case 'year':
          aValue = a.publicationYear || 0;
          bValue = b.publicationYear || 0;
          break;
        case 'journal':
          aValue = a.journal || '';
          bValue = b.journal || '';
          break;
        default:
          return 0;
      }
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [citations, searchTerm, sortBy, sortOrder, filterBy]);

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedCitations);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedCitations(newExpanded);
  };

  const getDocumentTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'journal-article': 'bg-blue-100 text-blue-800',
      'conference-paper': 'bg-green-100 text-green-800',
      'book': 'bg-purple-100 text-purple-800',
      'book-chapter': 'bg-purple-100 text-purple-800',
      'thesis': 'bg-orange-100 text-orange-800',
      'report': 'bg-gray-100 text-gray-800',
      'webpage': 'bg-cyan-100 text-cyan-800',
      'preprint': 'bg-yellow-100 text-yellow-800',
      'other': 'bg-gray-100 text-gray-800'
    };
    return colors[type] || colors.other;
  };

  const formatDocumentType = (type: string) => {
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Citation Preview</h2>
          <p className="text-gray-600">
            {filteredAndSortedCitations.length} of {citations.length} citations
          </p>
        </div>
        {onClose && (
          <Button onClick={onClose} variant="outline">
            Close Preview
          </Button>
        )}
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search citations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Sort By */}
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="author">Author</SelectItem>
                <SelectItem value="year">Year</SelectItem>
                <SelectItem value="journal">Journal</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Order */}
            <Button
              variant="outline"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="flex items-center space-x-2"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              <span>{sortOrder === 'asc' ? 'Ascending' : 'Descending'}</span>
            </Button>

            {/* Filter */}
            <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Citations</SelectItem>
                <SelectItem value="with-abstract">With Abstract</SelectItem>
                <SelectItem value="with-doi">With DOI</SelectItem>
                <SelectItem value="recent">Recent (2020+)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Citations List */}
      <div className="space-y-4">
        {filteredAndSortedCitations.map((citation, index) => {
          const isExpanded = expandedCitations.has(index);
          
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                      {citation.title || 'Untitled'}
                    </h3>
                    
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      {/* Authors */}
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="h-4 w-4 mr-1" />
                        <span>{citation.authors.length > 0 ? citation.authors.join(', ') : 'Unknown Authors'}</span>
                      </div>
                      
                      {/* Year */}
                      {citation.publicationYear && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>{citation.publicationYear}</span>
                        </div>
                      )}
                      
                      {/* Document Type */}
                      <Badge className={getDocumentTypeColor(citation.documentType)}>
                        {formatDocumentType(citation.documentType)}
                      </Badge>
                    </div>

                    {/* Journal/Source */}
                    {citation.journal && (
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <BookOpen className="h-4 w-4 mr-1" />
                        <span className="font-medium">{citation.journal}</span>
                        {citation.volume && <span className="ml-2">Vol. {citation.volume}</span>}
                        {citation.issue && <span className="ml-1">({citation.issue})</span>}
                        {citation.pages && <span className="ml-2">pp. {citation.pages}</span>}
                      </div>
                    )}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpanded(index)}
                    className="ml-4"
                  >
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </div>

                {/* Quick Info */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {citation.doi && (
                    <Badge variant="outline" className="text-xs">
                      <Hash className="h-3 w-3 mr-1" />
                      DOI
                    </Badge>
                  )}
                  {citation.abstract && (
                    <Badge variant="outline" className="text-xs">
                      <FileText className="h-3 w-3 mr-1" />
                      Abstract
                    </Badge>
                  )}
                  {citation.keywords.length > 0 && (
                    <Badge variant="outline" className="text-xs">
                      <Tag className="h-3 w-3 mr-1" />
                      {citation.keywords.length} Keywords
                    </Badge>
                  )}
                  {citation.url && (
                    <Badge variant="outline" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      URL
                    </Badge>
                  )}
                </div>

                {/* Expanded Content */}
                {isExpanded && (
                  <div className="border-t pt-4 space-y-4">
                    {/* Abstract */}
                    {citation.abstract && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          Abstract
                        </h4>
                        <p className="text-sm text-gray-700 leading-relaxed bg-gray-50 p-3 rounded-lg">
                          {citation.abstract}
                        </p>
                      </div>
                    )}

                    {/* Keywords */}
                    {citation.keywords.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                          <Tag className="h-4 w-4 mr-2" />
                          Keywords
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {citation.keywords.map((keyword, keywordIndex) => (
                            <Badge key={keywordIndex} variant="secondary" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Additional Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* DOI */}
                      {citation.doi && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1 text-sm">DOI</h4>
                          <a 
                            href={`https://doi.org/${citation.doi}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                          >
                            {citation.doi}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                        </div>
                      )}

                      {/* URL */}
                      {citation.url && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1 text-sm">URL</h4>
                          <a 
                            href={citation.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 flex items-center truncate"
                          >
                            {citation.url}
                            <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                          </a>
                        </div>
                      )}

                      {/* Source Information */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-1 text-sm">Source</h4>
                        <div className="text-sm text-gray-600">
                          <p>Format: {citation.sourceFormat?.toUpperCase()}</p>
                          <p>Manager: {citation.sourceManager}</p>
                        </div>
                      </div>

                      {/* Processing Info */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-1 text-sm">Quality</h4>
                        <div className="text-sm text-gray-600">
                          <p>Confidence: {(citation.processingConfidence * 100).toFixed(0)}%</p>
                          {citation.processingErrors.length > 0 && (
                            <p className="text-red-600">{citation.processingErrors.length} errors</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Notes */}
                    {citation.notes && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2 text-sm">Notes</h4>
                        <p className="text-sm text-gray-700 bg-yellow-50 p-3 rounded-lg">
                          {citation.notes}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Summary */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {citations.filter(c => c.abstract && c.abstract.length > 0).length}
              </div>
              <div className="text-sm text-gray-600">With Abstracts</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {citations.filter(c => c.doi && c.doi.length > 0).length}
              </div>
              <div className="text-sm text-gray-600">With DOI</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {citations.filter(c => c.keywords.length > 0).length}
              </div>
              <div className="text-sm text-gray-600">With Keywords</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">
                {citations.filter(c => c.publicationYear && c.publicationYear >= 2020).length}
              </div>
              <div className="text-sm text-gray-600">Recent (2020+)</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default CitationPreviewView;
