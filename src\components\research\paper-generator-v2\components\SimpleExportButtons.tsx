/**
 * Simple Export Buttons Component
 * Provides easy export options with toggle buttons for Word and PDF formats
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Download, 
  FileText, 
  File,
  Loader2
} from 'lucide-react';

import { Paper } from '../types';
import { PaperExportService } from '../services/paper-export.service';

interface SimpleExportButtonsProps {
  paper: Paper;
}

export const SimpleExportButtons: React.FC<SimpleExportButtonsProps> = ({
  paper
}) => {
  const [isExporting, setIsExporting] = useState<'word' | 'pdf' | null>(null);

  // Get export statistics
  const stats = PaperExportService.getExportStats(paper);

  // Handle Word export
  const handleWordExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('word');
    try {
      await PaperExportService.exportToWord(paper);
    } catch (error) {
      console.error('Word export failed:', error);
    } finally {
      setIsExporting(null);
    }
  };

  // Handle PDF export
  const handlePdfExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('pdf');
    try {
      await PaperExportService.exportToPDF(paper);
    } catch (error) {
      console.error('PDF export failed:', error);
    } finally {
      setIsExporting(null);
    }
  };

  return (
    <div className="space-y-4">
      {/* Export Stats */}
      <div className="flex items-center gap-4 text-sm text-gray-600">
        <div className="flex items-center gap-1">
          <Download className="h-4 w-4" />
          <span>{stats.sectionsReady} sections ready</span>
        </div>
        <div className="flex items-center gap-1">
          <span>•</span>
          <span>{stats.totalWords.toLocaleString()} words</span>
        </div>
        <div className="flex items-center gap-1">
          <span>•</span>
          <span>{stats.completeness}% complete</span>
        </div>
      </div>

      {/* Export Buttons */}
      <div className="flex gap-3">
        {/* Word Export Button */}
        <Button
          onClick={handleWordExport}
          disabled={isExporting !== null || stats.sectionsReady === 0}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
          size="sm"
        >
          {isExporting === 'word' ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Exporting...</span>
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              <span>Export Word</span>
            </>
          )}
        </Button>

        {/* PDF Export Button */}
        <Button
          onClick={handlePdfExport}
          disabled={isExporting !== null || stats.sectionsReady === 0}
          className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white"
          size="sm"
          variant="outline"
        >
          {isExporting === 'pdf' ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Exporting...</span>
            </>
          ) : (
            <>
              <File className="h-4 w-4" />
              <span>Export PDF</span>
            </>
          )}
        </Button>
      </div>

      {/* Status Message */}
      {stats.sectionsReady === 0 && (
        <p className="text-sm text-amber-600">
          Add content to sections (more than 5 words) to enable export.
        </p>
      )}
      
      {stats.sectionsReady > 0 && stats.completeness < 30 && (
        <p className="text-sm text-blue-600">
          Ready to export! Consider adding more sections for a complete paper.
        </p>
      )}
      
      {stats.sectionsReady > 0 && stats.completeness >= 30 && (
        <p className="text-sm text-green-600">
          Paper ready for export with {stats.sectionsReady} sections and {stats.totalWords.toLocaleString()} words.
        </p>
      )}
    </div>
  );
};
