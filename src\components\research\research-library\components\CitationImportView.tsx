/**
 * Citation Import View Component
 * Comprehensive UI for importing citation libraries from reference managers
 */

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  Settings,
  Brain,
  Loader,
  X,
  Zap,
  Eye
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
// import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

// Import services and types
import { citationImportService } from '../services/citation-import.service';
import { formatDetectorService } from '../services/citation-parsers/format-detector.service';
import { CitationPersistenceService } from '../services/citation-persistence.service';
// import CitationPreviewView from './CitationPreviewView';
import SelectiveCitationImportView from './SelectiveCitationImportView';
import { ProgressiveCitationImportView } from './ProgressiveCitationImportView';
import {
  ImportFileInfo,
  CitationImportOptions,
  ImportProgress,
  ImportResult,
  FileValidationResult,
  ProcessedCitationData
} from '../types/citation-import.types';

interface CitationImportViewProps {
  libraries: Array<{ id: string; name: string; description: string; article_count: number }>;
  selectedLibrary: { id: string; name: string } | null;
  onSelectLibrary: (library: any) => void;
  onImportComplete: () => void;
  user: any;
}

interface ImportState {
  // File handling
  selectedFile: File | null;
  fileValidation: FileValidationResult | null;
  isValidating: boolean;

  // Import options
  importOptions: CitationImportOptions;

  // Progress tracking
  isImporting: boolean;
  importProgress: ImportProgress | null;
  importResult: ImportResult | null;

  // UI state
  showAdvancedOptions: boolean;
  selectedModel: string;
  showPreview: boolean;
  showSelectiveImport: boolean;
  parsedCitations: ProcessedCitationData[];

  // Session persistence
  sessionId: string | null;
  isLoadingSession: boolean;
}

const SUPPORTED_FORMATS = [
  { format: 'bibtex', extension: '.bib', name: 'BibTeX', description: 'LaTeX bibliography format' },
  { format: 'ris', extension: '.ris', name: 'RIS', description: 'Research Information Systems format' },
  { format: 'json', extension: '.json', name: 'JSON', description: 'Zotero CSL JSON, Mendeley JSON' },
  { format: 'endnote', extension: '.xml', name: 'EndNote XML', description: 'EndNote library export' },
  { format: 'refworks', extension: '.txt', name: 'RefWorks', description: 'RefWorks tagged format' },
  { format: 'csv', extension: '.csv', name: 'CSV', description: 'Comma-separated values' }
];

const AI_MODELS = [
  { id: 'google/gemini-2.5-flash', name: 'Gemini 2.5 Flash', provider: 'Google' },
  { id: 'google/gemini-2.5-pro', name: 'Gemini 2.5 Pro', provider: 'Google' },
  { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic' },
  { id: 'openai/gpt-4o', name: 'GPT-4o', provider: 'OpenAI' }
];

export function CitationImportView({ 
  libraries, 
  selectedLibrary, 
  onSelectLibrary, 
  onImportComplete, 
  user 
}: CitationImportViewProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [state, setState] = useState<ImportState>({
    selectedFile: null,
    fileValidation: null,
    isValidating: false,
    importOptions: {
      enableAIEnhancement: true,
      aiModel: 'google/gemini-2.5-flash',
      mergeStrategy: 'skip',
      duplicateDetection: true,
      duplicateThreshold: 0.8,
      abstractFilter: 'all',
      validateDOIs: true,
      validateURLs: false,
      requireMinimumFields: false,
      minimumRequiredFields: ['title', 'authors'],
      extractKeywords: true,
      suggestTags: true,
      detectResearchDomain: true,
      standardizeAuthorNames: true,
      cleanTitles: true,
      batchSize: 10,
      enableProgressCallback: true
    },
    isImporting: false,
    importProgress: null,
    importResult: null,
    showAdvancedOptions: false,
    selectedModel: 'google/gemini-2.5-flash',
    showPreview: false,
    showSelectiveImport: false,
    parsedCitations: [],
    sessionId: null,
    isLoadingSession: false
  });

  const [useProgressiveView, setUseProgressiveView] = useState(true);

  // Services - memoized to prevent recreation on every render
  const persistenceService = useMemo(() => new CitationPersistenceService(), []);

  /**
   * Handle file selection
   */
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setState(prev => ({ ...prev, selectedFile: file, isValidating: true, fileValidation: null }));

    try {
      // Read file content
      const content = await readFileContent(file);
      
      const fileInfo: ImportFileInfo = {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified),
        content
      };

      // Validate and detect format
      const validationResult = await formatDetectorService.detectFormat(fileInfo);
      
      if (validationResult.success && validationResult.data) {
        setState(prev => ({ 
          ...prev, 
          fileValidation: validationResult.data!,
          isValidating: false 
        }));
        
        if (validationResult.data.isValid) {
          toast.success(`Detected ${validationResult.data.format?.toUpperCase()} format with ${validationResult.data.estimatedCitations} citations`);
        } else {
          toast.error('Invalid file format or content');
        }
      } else {
        throw new Error(validationResult.error || 'File validation failed');
      }
    } catch (error: any) {
      console.error('File validation error:', error);
      toast.error(`File validation failed: ${error.message}`);
      setState(prev => ({ ...prev, isValidating: false, fileValidation: null }));
    }
  }, []);

  /**
   * Read file content
   */
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  /**
   * Handle import start
   */
  const handleImport = useCallback(async () => {
    if (!state.selectedFile || !state.fileValidation?.isValid || !selectedLibrary) {
      toast.error('Please select a valid file and library');
      return;
    }

    setState(prev => ({ ...prev, isImporting: true, importProgress: null, importResult: null }));

    try {
      const content = await readFileContent(state.selectedFile);
      
      const fileInfo: ImportFileInfo = {
        name: state.selectedFile.name,
        size: state.selectedFile.size,
        type: state.selectedFile.type,
        lastModified: new Date(state.selectedFile.lastModified),
        content
      };

      const result = await citationImportService.importCitations(
        fileInfo,
        selectedLibrary.id,
        user.id,
        state.importOptions,
        (progress) => {
          setState(prev => ({ ...prev, importProgress: progress }));
        }
      );

      if (result.success && result.data) {
        setState(prev => ({ 
          ...prev, 
          isImporting: false, 
          importResult: result.data!,
          importProgress: null 
        }));
        
        toast.success(`Successfully imported ${result.data.successfulImports} citations`);
        onImportComplete();
      } else {
        throw new Error(result.error || 'Import failed');
      }
    } catch (error: any) {
      console.error('Import error:', error);
      toast.error(`Import failed: ${error.message}`);
      setState(prev => ({ ...prev, isImporting: false, importProgress: null }));
    }
  }, [state.selectedFile, state.fileValidation, selectedLibrary, user, state.importOptions, onImportComplete]);

  /**
   * Save import session for persistence
   */
  const saveImportSession = useCallback(async (citations: ProcessedCitationData[]) => {
    if (!selectedLibrary || !user) return;

    try {
      const sessionName = `Import_${new Date().toISOString().split('T')[0]}_${Date.now()}`;
      const result = await persistenceService.createImportSession(
        user.id,
        selectedLibrary.id,
        sessionName,
        citations
      );

      if (result.success && result.sessionId) {
        setState(prev => ({ ...prev, sessionId: result.sessionId }));
        // Store session ID in localStorage for recovery
        localStorage.setItem('citation_import_session', result.sessionId);
      }
    } catch (error) {
      console.error('Failed to save import session:', error);
    }
  }, [selectedLibrary, user, persistenceService]);

  /**
   * Preview citations with detailed information
   */
  const previewCitations = useCallback(async () => {
    if (!state.selectedFile || !state.fileValidation?.isValid) {
      toast.error('Please select a valid file first');
      return;
    }

    try {
      setState(prev => ({ ...prev, isValidating: true }));

      const content = await readFileContent(state.selectedFile);

      // Parse citations without importing
      const parseResult = await citationImportService.parseCitations(
        { ...state.selectedFile, content } as any,
        state.fileValidation.format!
      );

      if (parseResult.success && parseResult.data) {
        setState(prev => ({
          ...prev,
          parsedCitations: parseResult.data!,
          showSelectiveImport: true,
          isValidating: false
        }));

        // Save session for persistence
        await saveImportSession(parseResult.data);

        toast.success(`Parsed ${parseResult.data.length} citations successfully`);
      } else {
        throw new Error(parseResult.error || 'Failed to parse citations');
      }
    } catch (error: any) {
      console.error('Preview error:', error);
      toast.error(`Preview failed: ${error.message}`);
      setState(prev => ({ ...prev, isValidating: false }));
    }
  }, [state.selectedFile, state.fileValidation, saveImportSession]);

  /**
   * Load import session from persistence
   */
  const loadImportSession = useCallback(async (sessionId: string) => {
    setState(prev => ({ ...prev, isLoadingSession: true }));

    try {
      const result = await persistenceService.getImportSession(sessionId);

      if (result.success && result.session) {
        setState(prev => ({
          ...prev,
          parsedCitations: result.session!.citations,
          sessionId: sessionId,
          showSelectiveImport: true,
          isLoadingSession: false
        }));
      } else {
        // Clear invalid session
        localStorage.removeItem('citation_import_session');
        setState(prev => ({ ...prev, isLoadingSession: false }));
      }
    } catch (error) {
      console.error('Failed to load import session:', error);
      localStorage.removeItem('citation_import_session');
      setState(prev => ({ ...prev, isLoadingSession: false }));
    }
  }, [persistenceService]);

  /**
   * Update import options
   */
  const updateImportOptions = useCallback((updates: Partial<CitationImportOptions>) => {
    setState(prev => ({
      ...prev,
      importOptions: { ...prev.importOptions, ...updates }
    }));
  }, []);

  /**
   * Reset import state
   */
  const resetImport = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedFile: null,
      fileValidation: null,
      importProgress: null,
      importResult: null,
      isImporting: false,
      isValidating: false,
      sessionId: null,
      parsedCitations: []
    }));

    // Clear session from localStorage
    localStorage.removeItem('citation_import_session');

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  /**
   * Check for existing session on mount
   */
  useEffect(() => {
    const sessionId = localStorage.getItem('citation_import_session');
    if (sessionId && selectedLibrary && user) {
      loadImportSession(sessionId);
    }
  }, [selectedLibrary, user, loadImportSession]);

  // Show loading state when restoring session
  if (state.isLoadingSession) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Restoring Import Session</h3>
          <p className="text-gray-600">Loading your previously uploaded citations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Import Citation Library</h2>
          <p className="text-gray-600 mt-1">
            Import citations from popular reference managers (Zotero, Mendeley, EndNote, etc.)
          </p>
        </div>
        
        {state.importResult && (
          <Button onClick={resetImport} variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import Another File
          </Button>
        )}
      </div>

      {/* Library Selection */}
      {!selectedLibrary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Select Target Library
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">Choose which library to import citations into:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {libraries.map((library) => (
                <Card 
                  key={library.id} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onSelectLibrary(library)}
                >
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900">{library.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{library.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{library.article_count} articles</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {selectedLibrary && !state.importResult && (
        <>
          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="h-5 w-5 mr-2" />
                Upload Citation File
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Selected Library Info */}
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Target Library:</strong> {selectedLibrary.name}
                  </p>
                </div>

                {/* File Input */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".bib,.ris,.json,.xml,.txt,.csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  
                  {!state.selectedFile ? (
                    <div>
                      <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">
                        Choose citation file to upload
                      </p>
                      <p className="text-gray-600 mb-4">
                        Supports BibTeX, RIS, JSON, EndNote XML, RefWorks, and CSV formats
                      </p>
                      <Button onClick={() => fileInputRef.current?.click()}>
                        Select File
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <FileText className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">
                        {state.selectedFile.name}
                      </p>
                      <p className="text-gray-600 mb-4">
                        {(state.selectedFile.size / 1024).toFixed(1)} KB
                      </p>
                      
                      {state.isValidating && (
                        <div className="flex items-center justify-center">
                          <Loader className="h-4 w-4 animate-spin mr-2" />
                          <span>Validating file...</span>
                        </div>
                      )}
                      
                      {state.fileValidation && (
                        <div className="space-y-2">
                          {state.fileValidation.isValid ? (
                            <div className="flex items-center justify-center text-green-600">
                              <CheckCircle className="h-4 w-4 mr-2" />
                              <span>Valid {state.fileValidation.format?.toUpperCase()} file</span>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center text-red-600">
                              <AlertCircle className="h-4 w-4 mr-2" />
                              <span>Invalid file format</span>
                            </div>
                          )}
                          
                          <div className="flex justify-center space-x-4 text-sm text-gray-600">
                            <span>Format: {state.fileValidation.format || 'Unknown'}</span>
                            <span>Source: {state.fileValidation.source || 'Generic'}</span>
                            <span>Citations: {state.fileValidation.estimatedCitations}</span>
                          </div>
                        </div>
                      )}
                      
                      <div className="flex justify-center space-x-2 mt-4">
                        <Button 
                          variant="outline" 
                          onClick={() => fileInputRef.current?.click()}
                        >
                          Change File
                        </Button>
                        <Button variant="outline" onClick={resetImport}>
                          <X className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Supported Formats */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {SUPPORTED_FORMATS.map((format) => (
                    <div key={format.format} className="text-center p-3 bg-gray-50 rounded-lg">
                      <p className="font-medium text-gray-900">{format.name}</p>
                      <p className="text-xs text-gray-600">{format.extension}</p>
                      <p className="text-xs text-gray-500 mt-1">{format.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Import Options */}
          {state.fileValidation?.isValid && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    Import Options
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setState(prev => ({ ...prev, showAdvancedOptions: !prev.showAdvancedOptions }))}
                  >
                    {state.showAdvancedOptions ? 'Hide' : 'Show'} Advanced
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Abstract Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Abstract Filter</label>
                    <Select
                      value={state.importOptions.abstractFilter || 'all'}
                      onValueChange={(value: 'all' | 'with' | 'without') =>
                        updateImportOptions({ abstractFilter: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Articles</SelectItem>
                        <SelectItem value="with">Only With Abstract</SelectItem>
                        <SelectItem value="without">Only Without Abstract</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* AI Enhancement */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={state.importOptions.enableAIEnhancement}
                      onCheckedChange={(checked) => updateImportOptions({ enableAIEnhancement: !!checked })}
                    />
                    <Brain className="h-4 w-4 text-blue-500" />
                    <label className="text-sm font-medium">Enable AI Enhancement</label>
                  </div>

                  {state.importOptions.enableAIEnhancement && (
                    <div className="ml-6 space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">AI Model</label>
                        <Select
                          value={state.importOptions.aiModel}
                          onValueChange={(value) => updateImportOptions({ aiModel: value })}
                        >
                          <SelectTrigger className="w-full mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {AI_MODELS.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.name} ({model.provider})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.extractKeywords}
                            onCheckedChange={(checked) => updateImportOptions({ extractKeywords: !!checked })}
                          />
                          <label className="text-sm">Extract Keywords</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.suggestTags}
                            onCheckedChange={(checked) => updateImportOptions({ suggestTags: !!checked })}
                          />
                          <label className="text-sm">Suggest Tags</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.detectResearchDomain}
                            onCheckedChange={(checked) => updateImportOptions({ detectResearchDomain: !!checked })}
                          />
                          <label className="text-sm">Detect Research Domain</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.standardizeAuthorNames}
                            onCheckedChange={(checked) => updateImportOptions({ standardizeAuthorNames: !!checked })}
                          />
                          <label className="text-sm">Standardize Authors</label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Duplicate Detection */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={state.importOptions.duplicateDetection}
                      onCheckedChange={(checked) => updateImportOptions({ duplicateDetection: !!checked })}
                    />
                    <label className="text-sm font-medium">Detect Duplicates</label>
                  </div>

                  {state.importOptions.duplicateDetection && (
                    <div className="ml-6">
                      <label className="text-sm font-medium text-gray-700">Merge Strategy</label>
                      <Select
                        value={state.importOptions.mergeStrategy}
                        onValueChange={(value: any) => updateImportOptions({ mergeStrategy: value })}
                      >
                        <SelectTrigger className="w-full mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="skip">Skip Duplicates</SelectItem>
                          <SelectItem value="update">Update Existing</SelectItem>
                          <SelectItem value="replace">Replace Existing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Advanced Options */}
                  {state.showAdvancedOptions && (
                    <div className="border-t pt-4 space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.validateDOIs}
                            onCheckedChange={(checked) => updateImportOptions({ validateDOIs: !!checked })}
                          />
                          <label className="text-sm">Validate DOIs</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.validateURLs}
                            onCheckedChange={(checked) => updateImportOptions({ validateURLs: !!checked })}
                          />
                          <label className="text-sm">Validate URLs</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.requireMinimumFields}
                            onCheckedChange={(checked) => updateImportOptions({ requireMinimumFields: !!checked })}
                          />
                          <label className="text-sm">Require Minimum Fields</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.importOptions.cleanTitles}
                            onCheckedChange={(checked) => updateImportOptions({ cleanTitles: !!checked })}
                          />
                          <label className="text-sm">Clean Titles</label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="pt-4 space-y-3">
                    <Button
                      onClick={previewCitations}
                      disabled={state.isValidating || !state.fileValidation?.isValid}
                      variant="outline"
                      className="w-full"
                    >
                      {state.isValidating ? (
                        <>
                          <Loader className="h-4 w-4 mr-2 animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-2" />
                          Select & Import Citations
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={handleImport}
                      disabled={state.isImporting || !state.fileValidation?.isValid}
                      className="w-full"
                    >
                      {state.isImporting ? (
                        <>
                          <Loader className="h-4 w-4 mr-2 animate-spin" />
                          Importing...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Import {state.fileValidation?.estimatedCitations} Citations
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Import Progress */}
      {state.importProgress && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Loader className="h-5 w-5 mr-2 animate-spin" />
              Import Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>{state.importProgress.currentOperation}</span>
                  <span>{state.importProgress.currentItem} / {state.importProgress.totalItems}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${state.importProgress.percentage}%` }}
                  />
                </div>
              </div>
              
              <div className="flex justify-center">
                <Badge variant="outline">
                  {state.importProgress.stage.charAt(0).toUpperCase() + state.importProgress.stage.slice(1)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Import Results */}
      {state.importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              Import Complete
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">{state.importResult.successfulImports}</p>
                  <p className="text-sm text-green-700">Imported</p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">{state.importResult.skippedDuplicates}</p>
                  <p className="text-sm text-yellow-700">Duplicates</p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <p className="text-2xl font-bold text-red-600">{state.importResult.errors.length}</p>
                  <p className="text-sm text-red-700">Errors</p>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">{(state.importResult.processingTime / 1000).toFixed(1)}s</p>
                  <p className="text-sm text-blue-700">Processing Time</p>
                </div>
              </div>

              {state.importResult.warnings.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-800 mb-2">Warnings</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {state.importResult.warnings.slice(0, 5).map((warning, index) => (
                      <li key={index}>• {typeof warning === 'string' ? warning : warning.message}</li>
                    ))}
                    {state.importResult.warnings.length > 5 && (
                      <li>• ... and {state.importResult.warnings.length - 5} more</li>
                    )}
                  </ul>
                </div>
              )}

              {state.importResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 mb-2">Errors</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {state.importResult.errors.slice(0, 5).map((error, index) => (
                      <li key={index}>• {error.message}</li>
                    ))}
                    {state.importResult.errors.length > 5 && (
                      <li>• ... and {state.importResult.errors.length - 5} more</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selective Citation Import */}
      {state.showSelectiveImport && state.parsedCitations.length > 0 && (
        <div className="space-y-4">
          {/* View Toggle */}
          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Import Mode:</span>
              <Button
                variant={useProgressiveView ? "default" : "outline"}
                size="sm"
                onClick={() => setUseProgressiveView(true)}
              >
                <Zap className="h-4 w-4 mr-1" />
                Progressive (Recommended)
              </Button>
              <Button
                variant={!useProgressiveView ? "default" : "outline"}
                size="sm"
                onClick={() => setUseProgressiveView(false)}
              >
                <Eye className="h-4 w-4 mr-1" />
                Classic
              </Button>
            </div>
            <div className="text-xs text-gray-500">
              Progressive mode is faster for large datasets
            </div>
          </div>

          {/* Render appropriate view */}
          {useProgressiveView ? (
            <ProgressiveCitationImportView
              citations={state.parsedCitations}
              libraries={libraries}
              selectedLibrary={selectedLibrary}
              onSelectLibrary={onSelectLibrary}
              onClose={() => setState(prev => ({ ...prev, showSelectiveImport: false }))}
              onImportComplete={() => {
                onImportComplete();
                setState(prev => ({ ...prev, showSelectiveImport: false }));
              }}
              user={user}
            />
          ) : (
            <SelectiveCitationImportView
              citations={state.parsedCitations}
              libraries={libraries}
              selectedLibrary={selectedLibrary}
              onSelectLibrary={onSelectLibrary}
              onClose={() => setState(prev => ({ ...prev, showSelectiveImport: false }))}
              onImportComplete={() => {
                onImportComplete();
                setState(prev => ({ ...prev, showSelectiveImport: false }));
              }}
              user={user}
            />
          )}
        </div>
      )}
    </div>
  );
}

export default CitationImportView;
