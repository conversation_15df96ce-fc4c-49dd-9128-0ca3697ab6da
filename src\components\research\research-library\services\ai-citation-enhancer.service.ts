import { EnhancedAIService } from '@/components/research/paper-generator/enhanced-ai.service';

export interface CitationEnhancementResult {
  cleanedKeywords: string[];
  extractedKeywords: string[];
  suggestedTags: string[];
  researchDomain: string;
  methodologyType: string;
  qualityScore: number;
  missingFields: string[];
  enhancedAbstract?: string;
  confidence: number;
}

export interface CitationToEnhance {
  title: string;
  authors: string[];
  abstract?: string;
  keywords: string[];
  journal?: string;
  publicationYear?: number;
  doi?: string;
}

export class AICitationEnhancerService {
  private aiService: EnhancedAIService;

  constructor() {
    this.aiService = new EnhancedAIService();
  }

  /**
   * Enhance a single citation using AI
   */
  async enhanceCitation(citation: CitationToEnhance): Promise<CitationEnhancementResult> {
    try {
      // Clean and extract keywords using AI
      const keywordResult = await this.enhanceKeywords(citation);
      
      // Extract research domain and methodology
      const domainResult = await this.extractResearchDomain(citation);
      
      // Calculate quality score
      const qualityScore = this.calculateQualityScore(citation);
      
      // Identify missing fields
      const missingFields = this.identifyMissingFields(citation);

      return {
        cleanedKeywords: keywordResult.cleaned,
        extractedKeywords: keywordResult.extracted,
        suggestedTags: keywordResult.tags,
        researchDomain: domainResult.domain,
        methodologyType: domainResult.methodology,
        qualityScore,
        missingFields,
        confidence: Math.min(keywordResult.confidence, domainResult.confidence)
      };
    } catch (error) {
      console.error('Citation enhancement failed:', error);
      
      // Fallback to basic cleaning
      return {
        cleanedKeywords: this.basicKeywordCleaning(citation.keywords),
        extractedKeywords: [],
        suggestedTags: [],
        researchDomain: 'Unknown',
        methodologyType: 'Unknown',
        qualityScore: 0.5,
        missingFields: this.identifyMissingFields(citation),
        confidence: 0.3
      };
    }
  }

  /**
   * Enhance keywords using AI
   */
  private async enhanceKeywords(citation: CitationToEnhance): Promise<{
    cleaned: string[];
    extracted: string[];
    tags: string[];
    confidence: number;
  }> {
    const prompt = `
Analyze this academic citation and improve its keywords:

Title: ${citation.title}
Abstract: ${citation.abstract || 'Not available'}
Current Keywords: ${citation.keywords.join(', ')}
Journal: ${citation.journal || 'Not available'}

Tasks:
1. Clean and separate the existing keywords (some may be concatenated incorrectly)
2. Extract additional relevant keywords from the title and abstract
3. Suggest 3-5 research tags/categories
4. Remove duplicates and irrelevant terms

Respond in this exact JSON format:
{
  "cleanedKeywords": ["keyword1", "keyword2", ...],
  "extractedKeywords": ["new_keyword1", "new_keyword2", ...],
  "suggestedTags": ["tag1", "tag2", "tag3"],
  "confidence": 0.85
}

Focus on:
- Academic terminology
- Research methods and techniques
- Subject domains
- Technical concepts
- Geographic locations (if relevant)
`;

    try {
      const response = await this.aiService.generateResponse(prompt, {
        temperature: 0.3,
        maxTokens: 1000
      });

      const result = this.parseAIResponse(response);
      return {
        cleaned: result.cleanedKeywords || [],
        extracted: result.extractedKeywords || [],
        tags: result.suggestedTags || [],
        confidence: result.confidence || 0.7
      };
    } catch (error) {
      console.error('AI keyword enhancement failed:', error);
      return {
        cleaned: this.basicKeywordCleaning(citation.keywords),
        extracted: [],
        tags: [],
        confidence: 0.3
      };
    }
  }

  /**
   * Extract research domain and methodology using AI
   */
  private async extractResearchDomain(citation: CitationToEnhance): Promise<{
    domain: string;
    methodology: string;
    confidence: number;
  }> {
    const prompt = `
Analyze this academic citation and classify its research domain and methodology:

Title: ${citation.title}
Abstract: ${citation.abstract || 'Not available'}
Keywords: ${citation.keywords.join(', ')}
Journal: ${citation.journal || 'Not available'}

Classify into:
1. Research Domain (e.g., "Computer Science", "Environmental Science", "Medicine", "Engineering", etc.)
2. Methodology Type (e.g., "Experimental", "Theoretical", "Survey", "Case Study", "Review", etc.)

Respond in this exact JSON format:
{
  "domain": "Primary Research Domain",
  "methodology": "Primary Methodology Type",
  "confidence": 0.85
}
`;

    try {
      const response = await this.aiService.generateResponse(prompt, {
        temperature: 0.2,
        maxTokens: 500
      });

      const result = this.parseAIResponse(response);
      return {
        domain: result.domain || 'Unknown',
        methodology: result.methodology || 'Unknown',
        confidence: result.confidence || 0.7
      };
    } catch (error) {
      console.error('AI domain extraction failed:', error);
      return {
        domain: 'Unknown',
        methodology: 'Unknown',
        confidence: 0.3
      };
    }
  }

  /**
   * Enhanced keyword cleaning with better pattern recognition
   */
  private basicKeywordCleaning(keywords: string[]): string[] {
    const cleaned = new Set<string>();

    for (const keyword of keywords) {
      if (!keyword || typeof keyword !== 'string') continue;

      // First, handle common concatenated academic terms
      let processedKeyword = this.splitCommonAcademicTerms(keyword);

      // Split concatenated keywords by multiple patterns
      const parts = processedKeyword.split(/[,;|&\n\r\t]+/);

      for (let part of parts) {
        // Clean and normalize the part
        part = part.trim();

        // Remove quotes, brackets, and other punctuation
        part = part.replace(/^["'\[\(\{]+|["'\]\)\}]+$/g, '');
        part = part.replace(/[^\w\s-]/g, ' ');

        // Split camelCase and PascalCase
        part = part.replace(/([a-z])([A-Z])/g, '$1 $2');

        // Split on numbers followed by letters or vice versa
        part = part.replace(/(\d)([a-zA-Z])/g, '$1 $2');
        part = part.replace(/([a-zA-Z])(\d)/g, '$1 $2');

        // Replace underscores and multiple spaces
        part = part.replace(/[_]+/g, ' ');
        part = part.replace(/\s+/g, ' ');

        // Split into individual words and phrases
        const subParts = part.split(/\s+/);

        // Add individual meaningful words
        subParts.forEach(subPart => {
          const trimmed = subPart.trim().toLowerCase();
          if (trimmed.length > 2 && trimmed.length < 50 && !this.isStopWord(trimmed)) {
            cleaned.add(trimmed);
          }
        });

        // Also add the full phrase if it contains multiple words and is meaningful
        const fullPhrase = part.trim().toLowerCase();
        if (fullPhrase.includes(' ') && fullPhrase.length > 4 &&
            fullPhrase.length < 100 && !this.isStopWord(fullPhrase)) {
          cleaned.add(fullPhrase);
        }
      }
    }

    return Array.from(cleaned).sort();
  }

  /**
   * Split common concatenated academic terms
   */
  private splitCommonAcademicTerms(text: string): string {
    const patterns = [
      // Machine Learning & AI
      [/machinelearning/gi, 'machine learning'],
      [/deeplearning/gi, 'deep learning'],
      [/artificialintelligence/gi, 'artificial intelligence'],
      [/neuralnetwork/gi, 'neural network'],
      [/computervision/gi, 'computer vision'],
      [/naturallanguage/gi, 'natural language'],
      [/datascience/gi, 'data science'],
      [/bigdata/gi, 'big data'],

      // Remote Sensing & GIS (relevant for InSAR)
      [/remotesensing/gi, 'remote sensing'],
      [/satellitedata/gi, 'satellite data'],
      [/radardata/gi, 'radar data'],
      [/earthobservation/gi, 'earth observation'],
      [/landcover/gi, 'land cover'],
      [/landuse/gi, 'land use'],
      [/geospatial/gi, 'geospatial'],
      [/geoprocessing/gi, 'geoprocessing'],

      // General academic terms
      [/researchmethod/gi, 'research method'],
      [/dataanalysis/gi, 'data analysis'],
      [/casestudy/gi, 'case study'],
      [/fieldwork/gi, 'field work'],
      [/realtime/gi, 'real time'],
      [/opensource/gi, 'open source'],
      [/crosssection/gi, 'cross section'],
      [/timeseries/gi, 'time series'],
      [/multispectral/gi, 'multispectral'],
      [/hyperspectral/gi, 'hyperspectral'],
    ];

    let result = text;
    patterns.forEach(([pattern, replacement]) => {
      result = result.replace(pattern, replacement);
    });

    return result;
  }

  /**
   * Enhanced stop word detection
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      // Basic stop words
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
      'between', 'among', 'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be',
      'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'must', 'can', 'shall', 'should', 'ought',

      // Academic stop words
      'study', 'analysis', 'research', 'paper', 'article', 'journal', 'conference',
      'proceedings', 'abstract', 'introduction', 'conclusion', 'results', 'discussion',
      'method', 'methodology', 'approach', 'technique', 'system', 'model', 'framework',
      'application', 'using', 'based', 'new', 'novel', 'improved', 'enhanced', 'advanced',
      'recent', 'current', 'modern', 'effective', 'efficient', 'optimal', 'proposed',
      'developed', 'presented', 'described', 'investigated', 'evaluated', 'compared',
      'analyzed', 'examined', 'studied', 'observed', 'measured', 'calculated', 'estimated',

      // Common single letters and numbers
      'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '0'
    ]);

    return stopWords.has(word.toLowerCase());
  }

  /**
   * Calculate citation quality score
   */
  private calculateQualityScore(citation: CitationToEnhance): number {
    let score = 0;
    let maxScore = 0;

    // Title (required)
    maxScore += 20;
    if (citation.title && citation.title.length > 10) score += 20;

    // Authors (required)
    maxScore += 20;
    if (citation.authors && citation.authors.length > 0) score += 20;

    // Abstract
    maxScore += 15;
    if (citation.abstract && citation.abstract.length > 50) score += 15;

    // Keywords
    maxScore += 15;
    if (citation.keywords && citation.keywords.length > 0) score += 15;

    // Journal
    maxScore += 10;
    if (citation.journal) score += 10;

    // Publication Year
    maxScore += 10;
    if (citation.publicationYear) score += 10;

    // DOI
    maxScore += 10;
    if (citation.doi) score += 10;

    return score / maxScore;
  }

  /**
   * Identify missing fields
   */
  private identifyMissingFields(citation: CitationToEnhance): string[] {
    const missing: string[] = [];

    if (!citation.abstract) missing.push('abstract');
    if (!citation.doi) missing.push('doi');
    if (!citation.journal) missing.push('journal');
    if (!citation.publicationYear) missing.push('publication_year');
    if (!citation.keywords || citation.keywords.length === 0) missing.push('keywords');

    return missing;
  }

  /**
   * Parse AI response safely
   */
  private parseAIResponse(response: string): any {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return {};
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      return {};
    }
  }

  /**
   * Batch enhance multiple citations
   */
  async enhanceCitations(
    citations: CitationToEnhance[],
    onProgress?: (current: number, total: number) => void
  ): Promise<CitationEnhancementResult[]> {
    const results: CitationEnhancementResult[] = [];
    
    for (let i = 0; i < citations.length; i++) {
      if (onProgress) {
        onProgress(i + 1, citations.length);
      }
      
      const result = await this.enhanceCitation(citations[i]);
      results.push(result);
      
      // Add delay to avoid rate limiting
      if (i < citations.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }
}

export const aiCitationEnhancerService = new AICitationEnhancerService();
