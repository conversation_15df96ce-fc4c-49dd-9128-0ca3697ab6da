/**
 * Quality Assurance Service
 * Implements data validation, quality assessment, and improvement suggestions for citations
 */

import { 
  ProcessedCitationData,
  QualityAssessment,
  QualityIssue,
  QualitySuggestion,
  CitationImportServiceResponse
} from '../types/citation-import.types';

export class QualityAssuranceService {
  /**
   * Assess quality of a single citation
   */
  async assessCitationQuality(citation: ProcessedCitationData): Promise<QualityAssessment> {
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];
    
    // Check for missing required fields
    this.checkRequiredFields(citation, issues, suggestions);
    
    // Validate field formats
    this.validateFieldFormats(citation, issues, suggestions);
    
    // Check for consistency issues
    this.checkConsistency(citation, issues, suggestions);
    
    // Assess data completeness
    const completeness = this.calculateCompleteness(citation);
    
    // Assess data accuracy
    const accuracy = this.calculateAccuracy(citation, issues);
    
    // Assess data consistency
    const consistency = this.calculateConsistency(citation, issues);
    
    // Calculate overall score
    const overall = (completeness + accuracy + consistency) / 3;
    
    return {
      score: overall,
      completeness,
      accuracy,
      consistency,
      issues,
      suggestions
    };
  }

  /**
   * Assess quality of multiple citations
   */
  async assessBatchQuality(
    citations: ProcessedCitationData[]
  ): Promise<CitationImportServiceResponse<QualityAssessment[]>> {
    try {
      const assessments = await Promise.all(
        citations.map(citation => this.assessCitationQuality(citation))
      );
      
      return {
        success: true,
        data: assessments,
        error: null
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `Quality assessment failed: ${error.message}`
      };
    }
  }

  /**
   * Generate quality report for a batch of citations
   */
  async generateQualityReport(citations: ProcessedCitationData[]): Promise<{
    overallScore: number;
    totalIssues: number;
    issuesByType: Record<string, number>;
    issuesBySeverity: Record<string, number>;
    recommendations: string[];
    topIssues: QualityIssue[];
  }> {
    const assessments = await this.assessBatchQuality(citations);
    
    if (!assessments.success || !assessments.data) {
      throw new Error('Failed to generate quality report');
    }
    
    const allIssues = assessments.data.flatMap(assessment => assessment.issues);
    const overallScore = assessments.data.reduce((sum, assessment) => sum + assessment.score, 0) / assessments.data.length;
    
    // Group issues by type
    const issuesByType: Record<string, number> = {};
    allIssues.forEach(issue => {
      issuesByType[issue.type] = (issuesByType[issue.type] || 0) + 1;
    });
    
    // Group issues by severity
    const issuesBySeverity: Record<string, number> = {};
    allIssues.forEach(issue => {
      issuesBySeverity[issue.severity] = (issuesBySeverity[issue.severity] || 0) + 1;
    });
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(issuesByType, issuesBySeverity);
    
    // Get top issues (most frequent)
    const issueFrequency: Record<string, { issue: QualityIssue; count: number }> = {};
    allIssues.forEach(issue => {
      const key = `${issue.type}-${issue.field}-${issue.message}`;
      if (issueFrequency[key]) {
        issueFrequency[key].count++;
      } else {
        issueFrequency[key] = { issue, count: 1 };
      }
    });
    
    const topIssues = Object.values(issueFrequency)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(item => item.issue);
    
    return {
      overallScore,
      totalIssues: allIssues.length,
      issuesByType,
      issuesBySeverity,
      recommendations,
      topIssues
    };
  }

  /**
   * Check for missing required fields
   */
  private checkRequiredFields(
    citation: ProcessedCitationData,
    issues: QualityIssue[],
    suggestions: QualitySuggestion[]
  ): void {
    const requiredFields = [
      { field: 'title', name: 'Title' },
      { field: 'authors', name: 'Authors' },
      { field: 'publicationYear', name: 'Publication Year' }
    ];
    
    requiredFields.forEach(({ field, name }) => {
      const value = citation[field as keyof ProcessedCitationData];
      
      if (!value || (Array.isArray(value) && value.length === 0)) {
        issues.push({
          type: 'missing_required',
          severity: 'high',
          field,
          message: `Missing required field: ${name}`,
          value: undefined
        });
        
        suggestions.push({
          type: 'fix',
          field,
          message: `Add ${name} to improve citation quality`,
          confidence: 0.9
        });
      }
    });
  }

  /**
   * Validate field formats
   */
  private validateFieldFormats(
    citation: ProcessedCitationData,
    issues: QualityIssue[],
    suggestions: QualitySuggestion[]
  ): void {
    // Validate DOI format
    if (citation.doi && !this.isValidDOI(citation.doi)) {
      issues.push({
        type: 'invalid_format',
        severity: 'medium',
        field: 'doi',
        message: 'Invalid DOI format',
        value: citation.doi
      });
      
      suggestions.push({
        type: 'fix',
        field: 'doi',
        message: 'Correct DOI format (should start with "10.")',
        confidence: 0.8
      });
    }
    
    // Validate publication year
    if (citation.publicationYear && !this.isValidYear(citation.publicationYear)) {
      issues.push({
        type: 'invalid_format',
        severity: 'medium',
        field: 'publicationYear',
        message: 'Invalid publication year',
        value: citation.publicationYear.toString()
      });
      
      suggestions.push({
        type: 'verify',
        field: 'publicationYear',
        message: 'Verify publication year is correct',
        confidence: 0.7
      });
    }
    
    // Validate URL format
    if (citation.url && !this.isValidURL(citation.url)) {
      issues.push({
        type: 'invalid_format',
        severity: 'low',
        field: 'url',
        message: 'Invalid URL format',
        value: citation.url
      });
      
      suggestions.push({
        type: 'fix',
        field: 'url',
        message: 'Correct URL format',
        confidence: 0.6
      });
    }
    
    // Check for suspicious characters in title
    if (citation.title && this.hasSuspiciousCharacters(citation.title)) {
      issues.push({
        type: 'suspicious',
        severity: 'low',
        field: 'title',
        message: 'Title contains suspicious characters or formatting',
        value: citation.title
      });
      
      suggestions.push({
        type: 'enhance',
        field: 'title',
        message: 'Clean title formatting',
        confidence: 0.8
      });
    }
  }

  /**
   * Check for consistency issues
   */
  private checkConsistency(
    citation: ProcessedCitationData,
    issues: QualityIssue[],
    suggestions: QualitySuggestion[]
  ): void {
    // Check author name consistency
    if (citation.authors.length > 0) {
      const inconsistentFormats = this.checkAuthorNameConsistency(citation.authors);
      if (inconsistentFormats) {
        issues.push({
          type: 'inconsistent',
          severity: 'low',
          field: 'authors',
          message: 'Inconsistent author name formats',
          value: citation.authors.join(', ')
        });
        
        suggestions.push({
          type: 'enhance',
          field: 'authors',
          message: 'Standardize author name formats',
          confidence: 0.9
        });
      }
    }
    
    // Check for missing complementary fields
    if (citation.volume && !citation.journal) {
      issues.push({
        type: 'inconsistent',
        severity: 'medium',
        field: 'volume',
        message: 'Volume specified without journal',
        value: citation.volume
      });
    }
    
    if (citation.issue && !citation.volume) {
      issues.push({
        type: 'inconsistent',
        severity: 'low',
        field: 'issue',
        message: 'Issue specified without volume',
        value: citation.issue
      });
    }
  }

  /**
   * Calculate completeness score
   */
  private calculateCompleteness(citation: ProcessedCitationData): number {
    const allFields = [
      'title', 'authors', 'publicationYear', 'journal', 'volume', 
      'issue', 'pages', 'doi', 'abstract', 'keywords', 'url'
    ];
    
    let filledFields = 0;
    
    allFields.forEach(field => {
      const value = citation[field as keyof ProcessedCitationData];
      if (value && (Array.isArray(value) ? value.length > 0 : true)) {
        filledFields++;
      }
    });
    
    return filledFields / allFields.length;
  }

  /**
   * Calculate accuracy score
   */
  private calculateAccuracy(citation: ProcessedCitationData, issues: QualityIssue[]): number {
    const formatIssues = issues.filter(issue => 
      issue.type === 'invalid_format' || issue.type === 'suspicious'
    );
    
    // Start with perfect score and deduct for issues
    let accuracy = 1.0;
    
    formatIssues.forEach(issue => {
      switch (issue.severity) {
        case 'high':
          accuracy -= 0.3;
          break;
        case 'medium':
          accuracy -= 0.2;
          break;
        case 'low':
          accuracy -= 0.1;
          break;
      }
    });
    
    return Math.max(0, accuracy);
  }

  /**
   * Calculate consistency score
   */
  private calculateConsistency(citation: ProcessedCitationData, issues: QualityIssue[]): number {
    const consistencyIssues = issues.filter(issue => issue.type === 'inconsistent');
    
    // Start with perfect score and deduct for issues
    let consistency = 1.0;
    
    consistencyIssues.forEach(issue => {
      switch (issue.severity) {
        case 'high':
          consistency -= 0.3;
          break;
        case 'medium':
          consistency -= 0.2;
          break;
        case 'low':
          consistency -= 0.1;
          break;
      }
    });
    
    return Math.max(0, consistency);
  }

  /**
   * Generate recommendations based on issues
   */
  private generateRecommendations(
    issuesByType: Record<string, number>,
    issuesBySeverity: Record<string, number>
  ): string[] {
    const recommendations: string[] = [];
    
    // High-level recommendations based on issue types
    if (issuesByType.missing_required > 0) {
      recommendations.push(`Fill in missing required fields for ${issuesByType.missing_required} citations`);
    }
    
    if (issuesByType.invalid_format > 0) {
      recommendations.push(`Fix format issues in ${issuesByType.invalid_format} citations`);
    }
    
    if (issuesByType.inconsistent > 0) {
      recommendations.push(`Standardize formatting for ${issuesByType.inconsistent} citations`);
    }
    
    if (issuesByType.suspicious > 0) {
      recommendations.push(`Review and clean ${issuesByType.suspicious} citations with suspicious content`);
    }
    
    // Severity-based recommendations
    if (issuesBySeverity.high > 0) {
      recommendations.push(`Address ${issuesBySeverity.high} high-priority issues immediately`);
    }
    
    if (issuesBySeverity.medium > 5) {
      recommendations.push('Consider batch processing to fix medium-priority issues');
    }
    
    return recommendations;
  }

  // Validation helper methods
  private isValidDOI(doi: string): boolean {
    return /^10\.\d+\//.test(doi);
  }

  private isValidYear(year: number): boolean {
    const currentYear = new Date().getFullYear();
    return year >= 1800 && year <= currentYear + 1;
  }

  private isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private hasSuspiciousCharacters(text: string): boolean {
    // Check for common formatting artifacts
    const suspiciousPatterns = [
      /\\[a-zA-Z]+\{/, // LaTeX commands
      /&[a-zA-Z]+;/,   // HTML entities
      /\{\{|\}\}/,     // Double braces
      /\[\[|\]\]/,     // Double brackets
      /[^\x00-\x7F]/   // Non-ASCII characters (might be encoding issues)
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(text));
  }

  private checkAuthorNameConsistency(authors: string[]): boolean {
    if (authors.length <= 1) return false;
    
    const formats = authors.map(author => {
      if (author.includes(',')) return 'last_first';
      if (author.split(' ').length >= 2) return 'first_last';
      return 'single_name';
    });
    
    const uniqueFormats = new Set(formats);
    return uniqueFormats.size > 1;
  }
}

export const qualityAssuranceService = new QualityAssuranceService();
