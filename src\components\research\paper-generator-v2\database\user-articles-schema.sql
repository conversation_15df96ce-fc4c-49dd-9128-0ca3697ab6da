-- Enhanced User Articles Database Schema
-- This schema supports user article/project management with full persistence

-- User Articles Table - Main article metadata
CREATE TABLE IF NOT EXISTS user_articles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    research_field TEXT,
    description TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed', 'archived')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    total_words INTEGER DEFAULT 0,
    total_sections INTEGER DEFAULT 13,
    completed_sections INTEGER DEFAULT 0,
    ai_model TEXT DEFAULT 'gemini-2.5-flash',
    keywords TEXT[], -- Array of keywords
    figures_count INTEGER DEFAULT 0,
    references_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_favorite BOOLEAN DEFAULT FALSE,
    tags TEXT[], -- User-defined tags
    template_type TEXT DEFAULT 'research_paper',
    export_count INTEGER DEFAULT 0,
    collaboration_enabled BOOLEAN DEFAULT FALSE,
    
    -- Metadata for better organization
    folder_path TEXT DEFAULT '/', -- For organizing articles in folders
    thumbnail_url TEXT, -- For visual article previews
    estimated_completion_time INTERVAL, -- AI-estimated time to complete
    
    -- Search and indexing
    search_vector tsvector GENERATED ALWAYS AS (
        to_tsvector('english', title || ' ' || COALESCE(description, '') || ' ' || COALESCE(research_field, ''))
    ) STORED
);

-- Article Sections Table - Individual section content
CREATE TABLE IF NOT EXISTS user_article_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id UUID NOT NULL REFERENCES user_articles(id) ON DELETE CASCADE,
    section_type TEXT NOT NULL, -- 'title', 'abstract', 'introduction', etc.
    section_order INTEGER NOT NULL,
    title TEXT,
    content TEXT,
    word_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'empty' CHECK (status IN ('empty', 'draft', 'completed', 'reviewed')),
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_model_used TEXT,
    generation_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Version control
    version INTEGER DEFAULT 1,
    parent_version_id UUID REFERENCES user_article_sections(id),
    
    -- AI enhancement tracking
    enhancement_count INTEGER DEFAULT 0,
    last_enhanced_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(article_id, section_type)
);

-- Article References Table - Citations and sources
CREATE TABLE IF NOT EXISTS user_article_references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id UUID NOT NULL REFERENCES user_articles(id) ON DELETE CASCADE,
    section_id UUID REFERENCES user_article_sections(id) ON DELETE CASCADE,
    reference_type TEXT DEFAULT 'citation' CHECK (reference_type IN ('citation', 'source', 'figure', 'table')),
    title TEXT NOT NULL,
    authors TEXT[],
    publication_year INTEGER,
    journal TEXT,
    doi TEXT,
    url TEXT,
    abstract TEXT,
    citation_style TEXT DEFAULT 'apa',
    citation_text TEXT, -- Formatted citation
    is_verified BOOLEAN DEFAULT FALSE,
    verification_score INTEGER DEFAULT 0,
    source_type TEXT DEFAULT 'ai_generated' CHECK (source_type IN ('ai_generated', 'tavily', 'manual', 'imported')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE
);

-- Article History Table - Track changes and versions
CREATE TABLE IF NOT EXISTS user_article_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id UUID NOT NULL REFERENCES user_articles(id) ON DELETE CASCADE,
    section_id UUID REFERENCES user_article_sections(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL CHECK (action_type IN ('created', 'updated', 'generated', 'enhanced', 'exported', 'reviewed')),
    description TEXT,
    changes JSONB, -- Store specific changes made
    user_id UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance tracking
    processing_time_ms INTEGER,
    ai_model_used TEXT,
    tokens_used INTEGER
);

-- Article Collaboration Table - For future collaboration features
CREATE TABLE IF NOT EXISTS user_article_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id UUID NOT NULL REFERENCES user_articles(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'viewer' CHECK (role IN ('owner', 'editor', 'reviewer', 'viewer')),
    permissions JSONB DEFAULT '{"read": true, "write": false, "export": false, "share": false}',
    invited_by UUID REFERENCES auth.users(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(article_id, user_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_articles_user_id ON user_articles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_articles_status ON user_articles(status);
CREATE INDEX IF NOT EXISTS idx_user_articles_updated_at ON user_articles(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_articles_search ON user_articles USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_user_articles_tags ON user_articles USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_article_sections_article_id ON user_article_sections(article_id);
CREATE INDEX IF NOT EXISTS idx_article_sections_type ON user_article_sections(section_type);
CREATE INDEX IF NOT EXISTS idx_article_sections_order ON user_article_sections(article_id, section_order);

CREATE INDEX IF NOT EXISTS idx_article_references_article_id ON user_article_references(article_id);
CREATE INDEX IF NOT EXISTS idx_article_references_section_id ON user_article_references(section_id);
CREATE INDEX IF NOT EXISTS idx_article_references_type ON user_article_references(reference_type);

CREATE INDEX IF NOT EXISTS idx_article_history_article_id ON user_article_history(article_id);
CREATE INDEX IF NOT EXISTS idx_article_history_created_at ON user_article_history(created_at DESC);

-- Row Level Security (RLS) Policies
ALTER TABLE user_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_references ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_collaborators ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_articles
CREATE POLICY "Users can view their own articles" ON user_articles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own articles" ON user_articles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own articles" ON user_articles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own articles" ON user_articles
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for user_article_sections
CREATE POLICY "Users can view sections of their articles" ON user_article_sections
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_articles 
            WHERE id = article_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sections for their articles" ON user_article_sections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_articles 
            WHERE id = article_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sections of their articles" ON user_article_sections
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_articles 
            WHERE id = article_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sections of their articles" ON user_article_sections
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_articles 
            WHERE id = article_id AND user_id = auth.uid()
        )
    );

-- Similar RLS policies for other tables...
-- (References, History, Collaborators follow same pattern)

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_article_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update article statistics when sections change
    UPDATE user_articles SET
        total_words = (
            SELECT COALESCE(SUM(word_count), 0) 
            FROM user_article_sections 
            WHERE article_id = NEW.article_id
        ),
        completed_sections = (
            SELECT COUNT(*) 
            FROM user_article_sections 
            WHERE article_id = NEW.article_id AND status = 'completed'
        ),
        progress_percentage = (
            SELECT LEAST(100, ROUND(
                (COUNT(*) FILTER (WHERE status = 'completed') * 100.0) / 
                GREATEST(COUNT(*), 1)
            ))
            FROM user_article_sections 
            WHERE article_id = NEW.article_id
        ),
        updated_at = NOW()
    WHERE id = NEW.article_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update article stats
CREATE TRIGGER update_article_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_article_sections
    FOR EACH ROW EXECUTE FUNCTION update_article_stats();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_user_articles_updated_at
    BEFORE UPDATE ON user_articles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_article_sections_updated_at
    BEFORE UPDATE ON user_article_sections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
