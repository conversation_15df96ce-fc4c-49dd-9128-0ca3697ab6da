/**
 * Complete Article View Component
 * Displays the combined article with all sections in proper academic format
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  Download,
  Eye,
  BarChart3,
  Clock,
  CheckCircle,
  RefreshCw,
  Copy,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import ReactMarkdown from 'react-markdown';
import { Paper } from '../types';
import { PaperExportService, CombinedPaperContent } from '../services/paper-export.service';

interface CompleteArticleViewProps {
  paper: Paper;
  onExport?: (format: 'word' | 'pdf') => void;
}

export const CompleteArticleView: React.FC<CompleteArticleViewProps> = ({
  paper,
  onExport
}) => {
  const [isExporting, setIsExporting] = useState<'word' | 'pdf' | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Generate combined article content
  const combinedArticle: CombinedPaperContent = useMemo(() => {
    const result = PaperExportService.combinePaperSections(paper);
    setLastUpdated(new Date());
    return result;
  }, [paper.sections, paper.metadata]);

  // Get export statistics
  const stats = useMemo(() => {
    return PaperExportService.getExportStats(paper);
  }, [paper.sections]);

  // Handle Word export
  const handleWordExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('word');
    try {
      await PaperExportService.exportToWord(paper);
      onExport?.('word');
    } catch (error) {
      console.error('Word export failed:', error);
      toast.error('Failed to export Word document');
    } finally {
      setIsExporting(null);
    }
  };

  // Handle PDF export
  const handlePdfExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('pdf');
    try {
      await PaperExportService.exportToPDF(paper);
      onExport?.('pdf');
    } catch (error) {
      console.error('PDF export failed:', error);
      toast.error('Failed to export PDF document');
    } finally {
      setIsExporting(null);
    }
  };

  // Handle copy to clipboard
  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(combinedArticle.content);
      toast.success('Article content copied to clipboard');
    } catch (error) {
      console.error('Copy failed:', error);
      toast.error('Failed to copy content');
    }
  };

  // Custom markdown components for better academic formatting
  const markdownComponents = {
    h1: ({ children }: any) => (
      <h1 className="text-3xl font-bold text-gray-900 mb-6 text-center border-b-2 border-gray-200 pb-4">
        {children}
      </h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-2xl font-semibold text-gray-800 mt-8 mb-4 border-l-4 border-blue-500 pl-4">
        {children}
      </h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-xl font-medium text-gray-700 mt-6 mb-3">
        {children}
      </h3>
    ),
    p: ({ children }: any) => (
      <p className="text-gray-700 leading-relaxed mb-4 text-justify">
        {children}
      </p>
    ),
    strong: ({ children }: any) => (
      <strong className="font-semibold text-gray-900">
        {children}
      </strong>
    ),
    em: ({ children }: any) => (
      <em className="italic text-gray-800">
        {children}
      </em>
    ),
    ul: ({ children }: any) => (
      <ul className="list-disc list-inside mb-4 space-y-2 text-gray-700">
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside mb-4 space-y-2 text-gray-700">
        {children}
      </ol>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4">
        {children}
      </blockquote>
    ),
    hr: () => (
      <hr className="my-8 border-gray-300" />
    )
  };

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      {/* Header with Statistics and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-blue-600" />
              <div>
                <CardTitle className="text-xl">Complete Article Preview</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyContent}
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy Content
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.sectionsReady}</div>
              <div className="text-sm text-gray-600">Sections Ready</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.totalWords.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Words</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.completeness}%</div>
              <div className="text-sm text-gray-600">Complete</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {Math.ceil(stats.totalWords / 250)}
              </div>
              <div className="text-sm text-gray-600">Est. Pages</div>
            </div>
          </div>

          {/* Export Buttons */}
          <div className="flex items-center gap-3">
            <Button
              onClick={handleWordExport}
              disabled={isExporting !== null || stats.sectionsReady === 0}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isExporting === 'word' ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>Export Word</span>
                </>
              )}
            </Button>

            <Button
              onClick={handlePdfExport}
              disabled={isExporting !== null || stats.sectionsReady === 0}
              className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white"
              variant="outline"
            >
              {isExporting === 'pdf' ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>Export PDF</span>
                </>
              )}
            </Button>

            {stats.sectionsReady === 0 && (
              <Badge variant="outline" className="text-amber-600 border-amber-600">
                Add content to sections to enable export
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Article Content */}
      {combinedArticle.content ? (
        <Card>
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none">
              <ReactMarkdown components={markdownComponents}>
                {combinedArticle.content}
              </ReactMarkdown>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No Content Available
            </h3>
            <p className="text-gray-600 mb-4">
              Start writing content in the individual sections to see your complete article here.
            </p>
            <Badge variant="outline" className="text-blue-600 border-blue-600">
              Add content to sections (more than 5 words) to see the preview
            </Badge>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
