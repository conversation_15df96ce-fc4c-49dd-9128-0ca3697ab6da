<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deep Mode Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .step.active {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .step.completed {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            background: #4caf50;
        }
        .success:hover {
            background: #45a049;
        }
        .outline {
            background: #fff3e0;
            border: 1px solid #ff9800;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .outline h3 {
            margin-top: 0;
            color: #e65100;
        }
        .outline-item {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #4caf50;
            transition: width 0.3s ease;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.warning {
            background: #fff3e0;
            color: #f57c00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Deep Mode Generation Test</h1>
        <p>This test demonstrates the improved deep mode workflow where accepting an outline automatically generates all steps sequentially.</p>
        
        <div id="status" class="status info">
            Ready to test deep mode generation workflow
        </div>

        <div class="progress">
            <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
        <div id="progressText">0% Complete</div>

        <!-- Step 1: Generate Outline -->
        <div class="step" id="step1">
            <h3>Step 1: Generate Outline</h3>
            <p>Generate a structured outline for the paper section</p>
            <button id="generateOutlineBtn" onclick="generateOutline()">Generate Outline</button>
        </div>

        <!-- Step 2: Review Outline -->
        <div class="step" id="step2">
            <h3>Step 2: Review & Accept Outline</h3>
            <div id="outlineDisplay" class="outline" style="display: none;">
                <h3>Generated Outline</h3>
                <div id="outlineContent"></div>
                <button id="acceptOutlineBtn" onclick="acceptOutline()" disabled>Accept Outline & Auto-Generate</button>
                <button onclick="rejectOutline()">Regenerate Outline</button>
            </div>
        </div>

        <!-- Step 3: Auto-Generation -->
        <div class="step" id="step3">
            <h3>Step 3: Automatic Step-by-Step Generation</h3>
            <p>All outline steps will be generated automatically after accepting the outline</p>
            <div id="generationSteps"></div>
        </div>

        <!-- Step 4: Completion -->
        <div class="step" id="step4">
            <h3>Step 4: Send to Editor</h3>
            <p>Content will be automatically sent to the editor when all steps are complete</p>
            <div id="finalContent" style="display: none;">
                <h4>Generated Content:</h4>
                <div id="contentPreview" style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;"></div>
                <button class="success" onclick="sendToEditor()">Send to Editor</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let outline = [];
        let generatedContent = [];
        let isAutoGenerating = false;

        function updateProgress(step) {
            const progress = (step / 4) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent = Math.round(progress) + '% Complete';
            
            // Update step styling
            for (let i = 1; i <= 4; i++) {
                const stepEl = document.getElementById('step' + i);
                stepEl.classList.remove('active', 'completed');
                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }

        function generateOutline() {
            updateStatus('Generating CONTENT-BASED outline (no templates!)...', 'info');
            document.getElementById('generateOutlineBtn').disabled = true;

            // Simulate content-based outline generation (no templates)
            setTimeout(() => {
                // Example of CONTENT-BASED outline (specific to AI/ML research topic)
                outline = [
                    {
                        id: '1',
                        title: 'Machine Learning Algorithm Performance Analysis',
                        description: 'Comprehensive analysis of deep learning model performance on image classification tasks',
                        estimatedWords: 220,
                        children: [
                            { id: '1.1', title: 'CNN Architecture Comparison', description: 'Compare ResNet, VGG, and EfficientNet architectures for image classification', estimatedWords: 130 },
                            { id: '1.2', title: 'Training Data Impact Analysis', description: 'Analyze how dataset size and quality affect model performance', estimatedWords: 90 }
                        ]
                    },
                    {
                        id: '2',
                        title: 'Experimental Design and Methodology',
                        description: 'Detailed methodology for evaluating ML models on computer vision tasks',
                        estimatedWords: 190,
                        children: [
                            { id: '2.1', title: 'Dataset Preparation and Augmentation', description: 'Describe image preprocessing, augmentation techniques, and train/test splits', estimatedWords: 110 },
                            { id: '2.2', title: 'Hyperparameter Optimization Strategy', description: 'Explain grid search, random search, and Bayesian optimization approaches', estimatedWords: 80 }
                        ]
                    },
                    {
                        id: '3',
                        title: 'Performance Metrics and Evaluation Framework',
                        description: 'Comprehensive evaluation using accuracy, precision, recall, F1-score, and computational efficiency',
                        estimatedWords: 170,
                        children: [
                            { id: '3.1', title: 'Classification Accuracy Analysis', description: 'Detailed analysis of top-1 and top-5 accuracy across different model architectures', estimatedWords: 90 },
                            { id: '3.2', title: 'Computational Efficiency Metrics', description: 'Compare training time, inference speed, and memory usage across models', estimatedWords: 80 }
                        ]
                    },
                    {
                        id: '4',
                        title: 'Transfer Learning and Fine-tuning Results',
                        description: 'Analysis of pre-trained model adaptation for domain-specific image classification',
                        estimatedWords: 180,
                        children: [
                            { id: '4.1', title: 'Pre-trained Model Selection', description: 'Compare ImageNet pre-trained models for medical image classification', estimatedWords: 100 },
                            { id: '4.2', title: 'Layer Freezing Strategies', description: 'Analyze optimal layer freezing approaches for different dataset sizes', estimatedWords: 80 }
                        ]
                    },
                    {
                        id: '5',
                        title: 'Error Analysis and Model Interpretability',
                        description: 'Deep dive into model failures and visualization of learned features',
                        estimatedWords: 140,
                        children: [
                            { id: '5.1', title: 'Confusion Matrix Analysis', description: 'Identify common misclassification patterns and challenging image categories', estimatedWords: 80 },
                            { id: '5.2', title: 'Feature Visualization with Grad-CAM', description: 'Use gradient-based attention maps to understand model decision-making', estimatedWords: 60 }
                        ]
                    },
                    {
                        id: '6',
                        title: 'Practical Implementation and Deployment Considerations',
                        description: 'Real-world deployment challenges and optimization for production environments',
                        estimatedWords: 130,
                        children: [
                            { id: '6.1', title: 'Model Compression and Quantization', description: 'Techniques for reducing model size while maintaining accuracy', estimatedWords: 70 },
                            { id: '6.2', title: 'Edge Device Deployment', description: 'Challenges and solutions for mobile and IoT deployment', estimatedWords: 60 }
                        ]
                    }
                ];

                displayOutline();
                updateStatus('✅ CONTENT-BASED outline generated! Specific to ML/AI research topic with 6 specialized sections (no generic templates).', 'success');
                currentStep = 2;
                updateProgress(currentStep);
            }, 2000);
        }

        function displayOutline() {
            const outlineContent = document.getElementById('outlineContent');

            const renderOutlineItem = (item, level = 0) => {
                const indent = level * 20;
                let html = `<div class="outline-item" style="margin-left: ${indent}px; ${level > 0 ? 'border-left: 2px solid #ddd; padding-left: 10px;' : ''}">
                    <strong>${item.title}</strong> (${item.estimatedWords} words)<br>
                    <small style="color: #666;">${item.description}</small>
                </div>`;

                if (item.children && item.children.length > 0) {
                    html += item.children.map(child => renderOutlineItem(child, level + 1)).join('');
                }

                return html;
            };

            outlineContent.innerHTML = outline.map(item => renderOutlineItem(item)).join('');

            document.getElementById('outlineDisplay').style.display = 'block';
            document.getElementById('acceptOutlineBtn').disabled = false;
        }

        function acceptOutline() {
            updateStatus('Outline accepted! Starting automatic generation of all steps...', 'warning');
            document.getElementById('acceptOutlineBtn').disabled = true;
            currentStep = 3;
            updateProgress(currentStep);

            // Start auto-generation immediately (simulating the fixed workflow)
            setTimeout(() => {
                startAutoGeneration();
            }, 500);
        }

        function rejectOutline() {
            updateStatus('Regenerating outline...', 'info');
            document.getElementById('outlineDisplay').style.display = 'none';
            setTimeout(() => {
                generateOutline();
            }, 1000);
        }

        async function startAutoGeneration() {
            isAutoGenerating = true;
            updateStatus('Auto-generating all steps sequentially (including nested subsections)...', 'warning');

            const stepsContainer = document.getElementById('generationSteps');
            stepsContainer.innerHTML = '<h4>Generation Progress:</h4>';

            // Flatten outline to include all items (main sections + children)
            const allItems = [];
            const flattenOutline = (items, level = 0) => {
                items.forEach(item => {
                    allItems.push({ ...item, level });
                    if (item.children && item.children.length > 0) {
                        flattenOutline(item.children, level + 1);
                    }
                });
            };
            flattenOutline(outline);

            for (let i = 0; i < allItems.length; i++) {
                const item = allItems[i];
                const indent = item.level * 20;

                // Add step to UI
                const stepDiv = document.createElement('div');
                stepDiv.id = 'genStep' + i;
                stepDiv.innerHTML = `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-left: ${indent}px;">
                        <strong>Step ${i + 1}: ${item.title}</strong> ${item.level > 0 ? `(Level ${item.level + 1})` : '(Main Section)'}
                        <div id="stepStatus${i}" style="color: #666;">⏳ Generating...</div>
                    </div>
                `;
                stepsContainer.appendChild(stepDiv);

                // Simulate generation
                await new Promise(resolve => {
                    setTimeout(() => {
                        const content = `Generated content for ${item.title}. This is a ${item.estimatedWords}-word ${item.level > 0 ? 'subsection' : 'section'} about ${item.description.toLowerCase()}. The content includes detailed analysis and research findings relevant to this ${item.level > 0 ? 'subsection' : 'section'}.`;
                        generatedContent.push({ title: item.title, content, level: item.level });

                        document.getElementById('stepStatus' + i).innerHTML = '✅ Completed';
                        document.getElementById('stepStatus' + i).style.color = '#4caf50';

                        resolve();
                    }, 1200); // 1.2 second delay between steps
                });
            }

            // All steps completed
            updateStatus(`All ${allItems.length} steps completed successfully! Content ready to send to editor.`, 'success');
            currentStep = 4;
            updateProgress(currentStep);

            // Auto-send to editor after 2 seconds
            setTimeout(() => {
                showFinalContent();
                setTimeout(() => {
                    sendToEditor();
                }, 2000);
            }, 1000);
        }

        function showFinalContent() {
            const contentPreview = document.getElementById('contentPreview');
            const combinedContent = generatedContent.map(item => {
                const level = item.level || 0;
                const headerLevel = level === 0 ? '##' : level === 1 ? '###' : '####';
                return `${headerLevel} ${item.title}\n\n${item.content}`;
            }).join('\n\n');

            contentPreview.textContent = combinedContent;
            document.getElementById('finalContent').style.display = 'block';
        }

        function sendToEditor() {
            updateStatus('Content sent to editor successfully! Deep mode workflow complete.', 'success');

            // Simulate the FIXED behavior - no additional generation
            const editorContent = generatedContent.map(item =>
                `## ${item.title}\n\n${item.content}`
            ).join('\n\n');

            // Show that content is sent directly without triggering new AI generation
            alert('✅ SUCCESS! Content sent to editor without additional generation!\n\n' +
                  '🔧 FIXES IMPLEMENTED:\n' +
                  '• Send to Editor now uses onSendToEditor() instead of onGenerateContent()\n' +
                  '• No additional AI generation triggered\n' +
                  '• Content flows directly to editor\n' +
                  '• Deep mode and direct mode work independently\n\n' +
                  '📝 WORKFLOW COMPLETED:\n' +
                  '1. ✅ Generated outline\n' +
                  '2. ✅ User accepted outline\n' +
                  '3. ✅ Auto-generated all steps sequentially\n' +
                  '4. ✅ Sent content directly to editor\n\n' +
                  'Content length: ' + editorContent.length + ' characters');
        }

        // Initialize
        updateProgress(currentStep);
    </script>
</body>
</html>
