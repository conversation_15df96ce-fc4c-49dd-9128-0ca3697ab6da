/**
 * CSV Parser Service
 * Parses CSV files with citation data
 */

import { 
  ProcessedCitationData, 
  CitationFileFormat, 
  ReferenceManagerSource,
  CitationDocumentType,
  CitationImportServiceResponse 
} from '../../types/citation-import.types';

export class CSVParserService {
  /**
   * Parse CSV file content
   */
  async parseCSV(content: string): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    try {
      const rows = this.parseCSVContent(content);
      
      if (rows.length === 0) {
        throw new Error('Empty CSV file');
      }
      
      const headers = rows[0];
      const dataRows = rows.slice(1);
      
      const citations = dataRows.map((row, index) => 
        this.convertCSVRow(headers, row, index)
      ).filter(citation => citation !== null) as ProcessedCitationData[];
      
      return {
        success: true,
        data: citations,
        error: null,
        warnings: this.generateWarnings(citations)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `CSV parsing failed: ${error.message}`
      };
    }
  }

  /**
   * Parse CSV content into rows
   */
  private parseCSVContent(content: string): string[][] {
    const rows: string[][] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.trim()) {
        const row = this.parseCSVLine(line);
        rows.push(row);
      }
    }
    
    return rows;
  }

  /**
   * Parse a single CSV line handling quotes and commas
   */
  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;
    
    while (i < line.length) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        result.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }
    
    // Add the last field
    result.push(current.trim());
    
    return result;
  }

  /**
   * Convert CSV row to ProcessedCitationData
   */
  private convertCSVRow(headers: string[], row: string[], rowIndex: number): ProcessedCitationData | null {
    if (row.length === 0) return null;
    
    const data: Record<string, string> = {};
    
    // Map row values to headers
    headers.forEach((header, index) => {
      if (index < row.length) {
        data[header.toLowerCase().trim()] = row[index].trim();
      }
    });
    
    // Extract data using common field mappings
    const title = this.getFieldValue(data, ['title', 'article title', 'publication title']);
    if (!title) return null; // Skip rows without title
    
    return {
      title,
      authors: this.parseCSVAuthors(data),
      publicationYear: this.parseCSVYear(data),
      journal: this.getFieldValue(data, ['journal', 'publication name', 'source', 'periodical']),
      volume: this.getFieldValue(data, ['volume']),
      issue: this.getFieldValue(data, ['issue', 'number']),
      pages: this.getFieldValue(data, ['pages', 'page range', 'start page', 'end page']),
      doi: this.getFieldValue(data, ['doi', 'digital object identifier']),
      abstract: this.getFieldValue(data, ['abstract', 'summary']),
      keywords: this.parseCSVKeywords(data),
      documentType: this.mapCSVType(data),
      publisher: this.getFieldValue(data, ['publisher']),
      isbn: this.getFieldValue(data, ['isbn']),
      issn: this.getFieldValue(data, ['issn']),
      language: this.getFieldValue(data, ['language']),
      url: this.getFieldValue(data, ['url', 'link', 'web address']),
      tags: this.parseCSVTags(data),
      notes: this.getFieldValue(data, ['notes', 'comments']),
      sourceFormat: 'csv' as CitationFileFormat,
      sourceManager: this.detectCSVSource(headers),
      processingConfidence: this.calculateCSVConfidence(data),
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Get field value using multiple possible field names
   */
  private getFieldValue(data: Record<string, string>, fieldNames: string[]): string | undefined {
    for (const fieldName of fieldNames) {
      const value = data[fieldName.toLowerCase()];
      if (value && value.trim()) {
        return value.trim();
      }
    }
    return undefined;
  }

  /**
   * Parse authors from CSV data
   */
  private parseCSVAuthors(data: Record<string, string>): string[] {
    const authorFields = ['authors', 'author', 'author names', 'creators'];
    
    for (const field of authorFields) {
      const authorsStr = data[field.toLowerCase()];
      if (authorsStr) {
        return authorsStr
          .split(/[;|]/)
          .map(author => author.trim())
          .filter(author => author.length > 0);
      }
    }
    
    return [];
  }

  /**
   * Parse publication year from CSV data
   */
  private parseCSVYear(data: Record<string, string>): number | undefined {
    const yearFields = ['year', 'publication year', 'date', 'pub year'];
    
    for (const field of yearFields) {
      const yearStr = data[field.toLowerCase()];
      if (yearStr) {
        const match = yearStr.match(/(\d{4})/);
        if (match) {
          return parseInt(match[1]);
        }
      }
    }
    
    return undefined;
  }

  /**
   * Parse keywords from CSV data
   */
  private parseCSVKeywords(data: Record<string, string>): string[] {
    const keywordFields = ['keywords', 'keyword', 'tags', 'subject'];
    
    for (const field of keywordFields) {
      const keywordsStr = data[field.toLowerCase()];
      if (keywordsStr) {
        return keywordsStr
          .split(/[,;|]/)
          .map(keyword => keyword.trim())
          .filter(keyword => keyword.length > 0);
      }
    }
    
    return [];
  }

  /**
   * Parse tags from CSV data
   */
  private parseCSVTags(data: Record<string, string>): string[] {
    const tagFields = ['tags', 'labels', 'categories'];
    
    for (const field of tagFields) {
      const tagsStr = data[field.toLowerCase()];
      if (tagsStr) {
        return tagsStr
          .split(/[,;|]/)
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);
      }
    }
    
    return [];
  }

  /**
   * Map CSV document type
   */
  private mapCSVType(data: Record<string, string>): CitationDocumentType {
    const typeFields = ['type', 'document type', 'publication type', 'item type'];
    
    for (const field of typeFields) {
      const typeStr = data[field.toLowerCase()];
      if (typeStr) {
        const normalizedType = typeStr.toLowerCase();
        
        if (normalizedType.includes('journal') || normalizedType.includes('article')) {
          return 'journal-article';
        } else if (normalizedType.includes('conference') || normalizedType.includes('proceeding')) {
          return 'conference-paper';
        } else if (normalizedType.includes('book') && !normalizedType.includes('chapter')) {
          return 'book';
        } else if (normalizedType.includes('chapter')) {
          return 'book-chapter';
        } else if (normalizedType.includes('thesis') || normalizedType.includes('dissertation')) {
          return 'thesis';
        } else if (normalizedType.includes('report')) {
          return 'report';
        } else if (normalizedType.includes('web') || normalizedType.includes('online')) {
          return 'webpage';
        } else if (normalizedType.includes('patent')) {
          return 'patent';
        } else if (normalizedType.includes('preprint')) {
          return 'preprint';
        }
      }
    }
    
    return 'other';
  }

  /**
   * Detect CSV source
   */
  private detectCSVSource(headers: string[]): ReferenceManagerSource {
    const headerStr = headers.join(' ').toLowerCase();
    
    if (headerStr.includes('mendeley')) {
      return 'mendeley';
    } else if (headerStr.includes('zotero')) {
      return 'zotero';
    } else if (headerStr.includes('endnote')) {
      return 'endnote';
    } else if (headerStr.includes('refworks')) {
      return 'refworks';
    }
    
    return 'generic';
  }

  /**
   * Calculate CSV confidence
   */
  private calculateCSVConfidence(data: Record<string, string>): number {
    let score = 0;
    const maxScore = 10;
    
    // Required fields
    if (this.getFieldValue(data, ['title', 'article title', 'publication title'])) score += 3;
    if (this.getFieldValue(data, ['authors', 'author', 'author names'])) score += 2;
    if (this.getFieldValue(data, ['year', 'publication year', 'date'])) score += 2;
    
    // Optional but important fields
    if (this.getFieldValue(data, ['journal', 'publication name', 'source'])) score += 1;
    if (this.getFieldValue(data, ['doi', 'digital object identifier'])) score += 1;
    if (this.getFieldValue(data, ['abstract', 'summary'])) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Generate warnings for parsed citations
   */
  private generateWarnings(citations: ProcessedCitationData[]): string[] {
    const warnings: string[] = [];
    
    citations.forEach((citation, index) => {
      if (!citation.title) {
        warnings.push(`Citation ${index + 1}: Missing title`);
      }
      if (citation.authors.length === 0) {
        warnings.push(`Citation ${index + 1}: Missing authors`);
      }
      if (!citation.publicationYear) {
        warnings.push(`Citation ${index + 1}: Missing publication year`);
      }
    });
    
    return warnings;
  }
}

export const csvParserService = new CSVParserService();
