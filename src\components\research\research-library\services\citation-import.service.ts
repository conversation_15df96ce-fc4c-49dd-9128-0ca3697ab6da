/**
 * Main Citation Import Service
 * Orchestrates file parsing, data mapping, AI enhancement, and database storage
 */

import { 
  ImportFileInfo,
  CitationImportOptions,
  ImportProgress,
  ImportResult,
  ProcessedCitationData,
  CitationEnhancementResult,
  DuplicateDetectionResult,
  CitationImportServiceResponse,
  CitationFileFormat
} from '../types/citation-import.types';

// Import parser services
import { bibTeXParserService } from './citation-parsers/bibtex-parser.service';
import { risParserService } from './citation-parsers/ris-parser.service';
import { jsonParserService } from './citation-parsers/json-parser.service';
import { endNoteParserService } from './citation-parsers/endnote-parser.service';
import { csvParserService } from './citation-parsers/csv-parser.service';
import { formatDetectorService } from './citation-parsers/format-detector.service';

// Import enhancement and storage services
import { citationEnhancementService } from './citation-enhancement.service';
import { researchLibraryService } from './research-library.service';

// Import duplicate detection
import { duplicateDetectionService } from './duplicate-detection.service';

// Import citation persistence
import { citationPersistenceService } from './citation-persistence.service';

export class CitationImportService {
  private progressCallback?: (progress: ImportProgress) => void;

  /**
   * Import citations from file
   */
  async importCitations(
    fileInfo: ImportFileInfo,
    libraryId: string,
    userId: string,
    options: CitationImportOptions,
    progressCallback?: (progress: ImportProgress) => void
  ): Promise<CitationImportServiceResponse<ImportResult>> {
    this.progressCallback = progressCallback;
    
    try {
      // Stage 1: File validation and format detection
      this.updateProgress('parsing', 0, 0, 'Detecting file format...');
      
      const validationResult = await formatDetectorService.detectFormat(fileInfo);
      if (!validationResult.success || !validationResult.data?.isValid) {
        throw new Error(validationResult.error || 'Invalid file format');
      }
      
      const { format, estimatedCitations } = validationResult.data;
      
      // Stage 2: Parse citations
      this.updateProgress('parsing', 0, estimatedCitations, 'Parsing citations...');
      
      const parseResult = await this.parseCitations(fileInfo, format);
      if (!parseResult.success || !parseResult.data) {
        throw new Error(parseResult.error || 'Failed to parse citations');
      }
      
      const parsedCitations = parseResult.data;
      
      // Stage 3: Process citations (enhancement, duplicate detection)
      this.updateProgress('processing', 0, parsedCitations.length, 'Processing citations...');
      
      const processedResult = await this.processCitations(parsedCitations, options);
      
      // Stage 4: Create import session and save to database
      this.updateProgress('saving', 0, processedResult.validCitations.length, 'Creating import session...');

      const sessionName = `Import_${new Date().toISOString().replace(/[:.]/g, '')}_${Date.now()}`;
      const sessionResult = await citationPersistenceService.createImportSession(
        userId,
        libraryId,
        sessionName,
        processedResult.validCitations
      );

      if (!sessionResult.success || !sessionResult.sessionId) {
        throw new Error(sessionResult.error || 'Failed to create import session');
      }

      // Stage 5: Process citations (with batch processing for large imports)
      this.updateProgress('saving', 0, processedResult.validCitations.length, 'Saving to library...');

      const saveResult = await citationPersistenceService.processCitationBatches(
        sessionResult.sessionId,
        userId,
        libraryId,
        (progress) => {
          if (this.progressCallback) {
            this.progressCallback({
              stage: 'saving',
              current: progress.current,
              total: progress.total,
              message: `Saving citation ${progress.current}...`,
              details: {
                currentBatch: progress.currentBatch,
                totalBatches: progress.totalBatches,
                batchProgress: progress.batchProgress
              }
            });
          }
        }
      );

      // Stage 6: Complete
      this.updateProgress('complete', saveResult.importedIds?.length || 0, processedResult.validCitations.length, 'Import complete');
      
      const result: ImportResult = {
        success: true,
        totalProcessed: parsedCitations.length,
        successfulImports: saveResult.importedIds?.length || 0,
        skippedDuplicates: processedResult.duplicateCount,
        errors: [...(processedResult.errors || []), ...(saveResult.errors || [])],
        warnings: processedResult.warnings,
        importedCitations: saveResult.importedIds || [],
        processingTime: Date.now() - (processedResult.startTime || Date.now()),
        enhancementResults: processedResult.enhancementResults,
        sessionId: sessionResult.sessionId
      };
      
      return {
        success: true,
        data: result,
        error: null
      };
      
    } catch (error: any) {
      this.updateProgress('error', 0, 0, error.message);
      
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Parse citations based on file format
   */
  async parseCitations(
    fileInfo: ImportFileInfo,
    format: CitationFileFormat
  ): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    const content = typeof fileInfo.content === 'string' 
      ? fileInfo.content 
      : new TextDecoder().decode(fileInfo.content);
    
    switch (format) {
      case 'bibtex':
        return await bibTeXParserService.parseBibTeX(content);
      case 'ris':
        return await risParserService.parseRIS(content);
      case 'json':
        return await jsonParserService.parseJSON(content);
      case 'endnote':
        return await endNoteParserService.parseEndNoteXML(content);
      case 'csv':
        return await csvParserService.parseCSV(content);
      default:
        return {
          success: false,
          data: null,
          error: `Unsupported format: ${format}`
        };
    }
  }

  /**
   * Process citations (enhancement, duplicate detection, validation)
   */
  private async processCitations(
    citations: ProcessedCitationData[],
    options: CitationImportOptions
  ): Promise<{
    validCitations: ProcessedCitationData[];
    duplicateCount: number;
    warnings: any[];
    enhancementResults?: CitationEnhancementResult[];
    startTime: number;
  }> {
    const startTime = Date.now();
    const validCitations: ProcessedCitationData[] = [];
    const warnings: any[] = [];
    let duplicateCount = 0;
    let enhancementResults: CitationEnhancementResult[] | undefined;

    // Apply abstract filter if specified
    let filteredCitations = citations;
    if (options.abstractFilter && options.abstractFilter !== 'all') {
      filteredCitations = citations.filter(citation => {
        const hasAbstract = citation.abstract && citation.abstract.trim().length > 0;
        if (options.abstractFilter === 'with') {
          return hasAbstract;
        } else if (options.abstractFilter === 'without') {
          return !hasAbstract;
        }
        return true;
      });
    }
    
    // AI Enhancement
    if (options.enableAIEnhancement) {
      this.updateProgress('enhancing', 0, filteredCitations.length, 'Enhancing with AI...');

      const enhancementOptions = {
        aiModel: options.aiModel || 'google/gemini-2.5-flash',
        enableDataCleaning: true,
        enableKeywordExtraction: options.extractKeywords,
        enableTagSuggestion: options.suggestTags,
        enableDomainDetection: options.detectResearchDomain,
        enableAuthorStandardization: options.standardizeAuthorNames,
        enableTitleCleaning: options.cleanTitles,
        batchSize: options.batchSize
      };

      const enhancementResult = await citationEnhancementService.enhanceCitations(filteredCitations, enhancementOptions);
      
      if (enhancementResult.success && enhancementResult.data) {
        enhancementResults = enhancementResult.data;
        // Use enhanced data
        for (let i = 0; i < filteredCitations.length; i++) {
          if (enhancementResults[i]) {
            filteredCitations[i] = enhancementResults[i].enhancedData;
          }
        }
      }
    }

    // Process each citation
    for (let i = 0; i < filteredCitations.length; i++) {
      const citation = filteredCitations[i];

      this.updateProgress('processing', i + 1, filteredCitations.length, `Processing citation ${i + 1}...`);
      
      // Validation
      const validationResult = this.validateCitation(citation, options);
      if (!validationResult.isValid) {
        warnings.push({
          type: 'validation',
          message: `Citation ${i + 1}: ${validationResult.errors.join(', ')}`,
          citationIndex: i
        });
        
        if (options.requireMinimumFields) {
          continue; // Skip invalid citations
        }
      }
      
      // Duplicate detection
      if (options.duplicateDetection) {
        const duplicateResult = await this.checkForDuplicates(citation, validCitations, options.duplicateThreshold);
        
        if (duplicateResult.isDuplicate) {
          duplicateCount++;
          
          if (options.mergeStrategy === 'skip') {
            warnings.push({
              type: 'duplicate',
              message: `Citation ${i + 1}: Skipped duplicate of "${duplicateResult.matchingCitation?.title}"`,
              citationIndex: i
            });
            continue;
          } else if (options.mergeStrategy === 'update') {
            // Update existing citation (would need more complex logic)
            warnings.push({
              type: 'duplicate',
              message: `Citation ${i + 1}: Updated existing citation`,
              citationIndex: i
            });
          }
        }
      }
      
      validCitations.push(citation);
    }
    
    return {
      validCitations,
      duplicateCount,
      warnings,
      enhancementResults,
      startTime
    };
  }

  /**
   * Validate citation data
   */
  private validateCitation(
    citation: ProcessedCitationData,
    options: CitationImportOptions
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check required fields
    if (options.requireMinimumFields && options.minimumRequiredFields) {
      for (const field of options.minimumRequiredFields) {
        const value = citation[field as keyof ProcessedCitationData];
        if (!value || (Array.isArray(value) && value.length === 0)) {
          errors.push(`Missing required field: ${field}`);
        }
      }
    }
    
    // Basic validation
    if (!citation.title || citation.title.trim().length === 0) {
      errors.push('Missing or empty title');
    }
    
    if (citation.authors.length === 0) {
      errors.push('No authors specified');
    }
    
    // DOI validation
    if (options.validateDOIs && citation.doi && !citation.doi.match(/^10\.\d+/)) {
      errors.push('Invalid DOI format');
    }
    
    // URL validation
    if (options.validateURLs && citation.url) {
      try {
        new URL(citation.url);
      } catch {
        errors.push('Invalid URL format');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check for duplicate citations
   */
  private async checkForDuplicates(
    citation: ProcessedCitationData,
    existingCitations: ProcessedCitationData[],
    threshold: number
  ): Promise<DuplicateDetectionResult> {
    // Use duplicate detection service
    return await duplicateDetectionService.detectDuplicate(citation, existingCitations, threshold);
  }

  /**
   * Convert ProcessedCitationData to LibraryArticleInsert format (legacy - now handled by persistence service)
   */
  private convertToLibraryArticle(citation: ProcessedCitationData, libraryId: string, userId: string): any {
    return {
      user_id: userId,
      library_id: libraryId,
      title: citation.title,
      authors: citation.authors,
      abstract: citation.abstract,
      doi: citation.doi,
      publication_year: citation.publicationYear,
      journal: citation.journal,
      volume: citation.volume,
      issue: citation.issue,
      pages: citation.pages,
      keywords: citation.keywords,
      tags: citation.tags,
      source_type: 'search' as const, // Imported citations are treated as search results
      source_url: citation.url,
      notes: citation.notes,
      favorite: false,
      extracted_metadata: {
        sourceFormat: citation.sourceFormat,
        sourceManager: citation.sourceManager,
        documentType: citation.documentType,
        processingConfidence: citation.processingConfidence,
        importTimestamp: new Date().toISOString()
      },
      extraction_confidence: citation.processingConfidence,
      ai_model_used: 'citation-import-system'
    };
  }

  /**
   * Update progress
   */
  private updateProgress(
    stage: ImportProgress['stage'],
    current: number,
    total: number,
    operation: string
  ): void {
    if (this.progressCallback) {
      const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
      
      this.progressCallback({
        stage,
        currentItem: current,
        totalItems: total,
        percentage,
        currentOperation: operation,
        errors: [],
        warnings: []
      });
    }
  }
}

export const citationImportService = new CitationImportService();
