/**
 * Simple Export Dialog Component
 * Provides easy export options for Word and PDF formats
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Download, 
  FileText, 
  File,
  CheckCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react';

import { Paper } from '../types';
import { PaperExportService } from '../services/paper-export.service';

interface SimpleExportDialogProps {
  paper: Paper;
  onClose?: () => void;
}

export const SimpleExportDialog: React.FC<SimpleExportDialogProps> = ({
  paper,
  onClose
}) => {
  const [isExporting, setIsExporting] = useState<'word' | 'pdf' | null>(null);

  // Get export statistics
  const stats = PaperExportService.getExportStats(paper);

  // Handle Word export
  const handleWordExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('word');
    try {
      await PaperExportService.exportToWord(paper);
    } catch (error) {
      console.error('Word export failed:', error);
    } finally {
      setIsExporting(null);
    }
  };

  // Handle PDF export
  const handlePdfExport = async () => {
    if (stats.sectionsReady === 0) return;
    
    setIsExporting('pdf');
    try {
      await PaperExportService.exportToPDF(paper);
    } catch (error) {
      console.error('PDF export failed:', error);
    } finally {
      setIsExporting(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Export Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Complete Paper
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.sectionsReady}
              </div>
              <p className="text-sm text-gray-600">Sections Ready</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.totalWords.toLocaleString()}
              </div>
              <p className="text-sm text-gray-600">Total Words</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {stats.completeness}%
              </div>
              <p className="text-sm text-gray-600">Complete</p>
            </div>
          </div>
          
          {stats.completeness < 30 && (
            <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200 mb-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <p className="text-sm text-yellow-800">
                  Your paper has limited content. Consider adding more sections before exporting.
                </p>
              </div>
            </div>
          )}

          {stats.sectionsReady === 0 && (
            <div className="p-3 bg-red-50 rounded-lg border border-red-200 mb-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <p className="text-sm text-red-800">
                  No sections with substantial content (more than 5 words) found. Please add content to your sections first.
                </p>
              </div>
            </div>
          )}

          {stats.sectionsReady > 0 && (
            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <p className="text-sm text-green-800">
                  Ready to export {stats.sectionsReady} sections with {stats.totalWords.toLocaleString()} words total.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Choose Export Format</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Word Export */}
            <Button
              onClick={handleWordExport}
              disabled={isExporting !== null || stats.sectionsReady === 0}
              className="h-20 flex flex-col items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
              size="lg"
            >
              {isExporting === 'word' ? (
                <>
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <FileText className="h-6 w-6" />
                  <span>Export Word</span>
                  <span className="text-xs opacity-80">(.docx format)</span>
                </>
              )}
            </Button>

            {/* PDF Export */}
            <Button
              onClick={handlePdfExport}
              disabled={isExporting !== null || stats.sectionsReady === 0}
              className="h-20 flex flex-col items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white"
              size="lg"
              variant="outline"
            >
              {isExporting === 'pdf' ? (
                <>
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <File className="h-6 w-6" />
                  <span>Export PDF</span>
                  <span className="text-xs opacity-80">(.pdf format)</span>
                </>
              )}
            </Button>
          </div>

          <div className="mt-4 text-sm text-gray-600 text-center">
            <p>
              Export will combine all sections with substantial content into a single, 
              beautifully formatted document with proper headings and spacing.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Export Details */}
      {stats.sectionsReady > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">What will be included:</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">Paper title and metadata</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">{stats.sectionsReady} sections with content</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">Proper academic formatting with headings</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">References section (if citations exist)</span>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> Only sections with more than 5 words will be included. 
                Empty or very short sections will be automatically excluded.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
