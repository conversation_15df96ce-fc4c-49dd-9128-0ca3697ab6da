/**
 * Citation Import Types
 * Comprehensive type definitions for citation library import system
 */

// Supported file formats for import
export type CitationFileFormat = 
  | 'bibtex'     // .bib files - LaTeX bibliography format
  | 'ris'        // .ris files - Research Information Systems format
  | 'json'       // .json files - <PERSON><PERSON><PERSON> CSL JSON, Mendeley JSON
  | 'endnote'    // .xml files - EndNote XML format
  | 'refworks'   // .txt files - RefWorks Tagged format
  | 'csv'        // .csv files - Custom spreadsheet exports
  | 'unknown';   // Unrecognized format

// Reference manager source detection
export type ReferenceManagerSource = 
  | 'zotero'
  | 'mendeley'
  | 'endnote'
  | 'refworks'
  | 'papers'
  | 'generic'
  | 'unknown';

// Document types for citations
export type CitationDocumentType = 
  | 'journal-article'
  | 'conference-paper'
  | 'book'
  | 'book-chapter'
  | 'thesis'
  | 'report'
  | 'webpage'
  | 'patent'
  | 'preprint'
  | 'other';

// Raw citation data structure (before processing)
export interface RawCitationData {
  // Core bibliographic fields
  title?: string;
  authors?: string | string[];
  year?: string | number;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  url?: string;
  abstract?: string;
  keywords?: string | string[];
  
  // Publication details
  publisher?: string;
  isbn?: string;
  issn?: string;
  language?: string;
  
  // Reference manager specific fields
  tags?: string | string[];
  notes?: string;
  file?: string;
  folder?: string;
  
  // Raw metadata from source
  rawData?: Record<string, any>;
  sourceFormat?: CitationFileFormat;
  sourceManager?: ReferenceManagerSource;
}

// Processed citation data (after parsing and standardization)
export interface ProcessedCitationData {
  // Standardized core fields
  title: string;
  authors: string[];
  publicationYear?: number;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  abstract?: string;
  keywords: string[];
  
  // Additional metadata
  documentType: CitationDocumentType;
  publisher?: string;
  isbn?: string;
  issn?: string;
  language?: string;
  url?: string;
  
  // User-defined fields
  tags: string[];
  notes?: string;
  
  // Processing metadata
  sourceFormat: CitationFileFormat;
  sourceManager: ReferenceManagerSource;
  processingConfidence: number; // 0-1 score
  processingErrors: string[];
  processingWarnings: string[];
}

// AI enhancement results
export interface CitationEnhancementResult {
  // Enhanced data
  enhancedData: ProcessedCitationData;
  
  // AI analysis results
  extractedKeywords: string[];
  suggestedTags: string[];
  researchDomain?: string;
  methodologyType?: string;
  
  // Quality assessment
  dataQuality: {
    completeness: number; // 0-1 score
    accuracy: number;     // 0-1 score
    consistency: number;  // 0-1 score
    overall: number;      // 0-1 score
  };
  
  // Enhancement metadata
  aiModel: string;
  enhancementTimestamp: Date;
  enhancementConfidence: number;
  enhancementNotes: string[];
}

// Import file information
export interface ImportFileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: Date;
  content: string | ArrayBuffer;
  detectedFormat?: CitationFileFormat;
  detectedSource?: ReferenceManagerSource;
  estimatedCitationCount?: number;
}

// Import options and configuration
export interface CitationImportOptions {
  // Processing options
  enableAIEnhancement: boolean;
  aiModel?: string;

  // Data handling
  mergeStrategy: 'skip' | 'update' | 'replace' | 'ask';
  duplicateDetection: boolean;
  duplicateThreshold: number; // 0-1 similarity threshold

  // Filtering options
  abstractFilter?: 'all' | 'with' | 'without';

  // Validation options
  validateDOIs: boolean;
  validateURLs: boolean;
  requireMinimumFields: boolean;
  minimumRequiredFields: string[];

  // Enhancement options
  extractKeywords: boolean;
  suggestTags: boolean;
  detectResearchDomain: boolean;
  standardizeAuthorNames: boolean;
  cleanTitles: boolean;

  // Progress tracking
  batchSize: number;
  enableProgressCallback: boolean;
}

// Import progress tracking
export interface ImportProgress {
  stage: 'parsing' | 'processing' | 'enhancing' | 'saving' | 'complete' | 'error';
  currentItem: number;
  totalItems: number;
  percentage: number;
  currentOperation: string;
  estimatedTimeRemaining?: number;
  errors: ImportError[];
  warnings: ImportWarning[];
}

// Import error handling
export interface ImportError {
  type: 'parse' | 'validation' | 'enhancement' | 'database' | 'network';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: string;
  citationIndex?: number;
  field?: string;
  suggestedFix?: string;
}

export interface ImportWarning {
  type: 'missing_field' | 'invalid_format' | 'duplicate' | 'low_confidence';
  message: string;
  citationIndex?: number;
  field?: string;
  suggestion?: string;
}

// Import result summary
export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  successfulImports: number;
  skippedDuplicates: number;
  errors: ImportError[];
  warnings: ImportWarning[];
  importedCitations: string[]; // Array of citation IDs
  processingTime: number; // milliseconds
  enhancementResults?: CitationEnhancementResult[];
  sessionId?: string; // Import session ID for batch processing
}

// Duplicate detection result
export interface DuplicateDetectionResult {
  isDuplicate: boolean;
  similarityScore: number;
  matchingCitation?: {
    id: string;
    title: string;
    authors: string[];
    similarity: number;
  };
  suggestedAction: 'skip' | 'merge' | 'keep_both';
  conflictingFields?: string[];
}

// Export options for citations
export interface CitationExportOptions {
  format: 'bibtex' | 'ris' | 'json' | 'csv' | 'endnote';
  citationStyle?: 'apa' | 'mla' | 'chicago' | 'ieee' | 'harvard';
  includeAbstracts: boolean;
  includeKeywords: boolean;
  includeNotes: boolean;
  includeTags: boolean;
  sortBy: 'title' | 'author' | 'year' | 'journal';
  sortOrder: 'asc' | 'desc';
}

// Bibliography generation options
export interface BibliographyOptions {
  style: 'apa' | 'mla' | 'chicago' | 'ieee' | 'harvard';
  format: 'html' | 'markdown' | 'plain' | 'rtf';
  includeAbstracts: boolean;
  groupBy?: 'author' | 'year' | 'journal' | 'type';
  sortBy: 'title' | 'author' | 'year' | 'journal';
  sortOrder: 'asc' | 'desc';
  annotated: boolean; // Include abstracts/notes as annotations
}

// Service response interfaces
export interface CitationImportServiceResponse<T> {
  success: boolean;
  data: T | null;
  error: string | null;
  warnings?: string[];
}

// File validation result
export interface FileValidationResult {
  isValid: boolean;
  format?: CitationFileFormat;
  source?: ReferenceManagerSource;
  estimatedCitations: number;
  errors: string[];
  warnings: string[];
  supportedFeatures: string[];
}

// Parser-specific interfaces
export interface BibTeXEntry {
  type: string;
  key: string;
  fields: Record<string, string>;
}

export interface RISEntry {
  type: string;
  fields: Record<string, string | string[]>;
}

export interface ZoteroCSLItem {
  type: string;
  title?: string;
  author?: Array<{ family: string; given: string }>;
  issued?: { 'date-parts': number[][] };
  'container-title'?: string;
  volume?: string;
  issue?: string;
  page?: string;
  DOI?: string;
  URL?: string;
  abstract?: string;
  keyword?: string;
  [key: string]: any;
}

// Quality assurance interfaces
export interface QualityAssessment {
  score: number; // 0-1 overall quality score
  completeness: number; // 0-1 how complete the data is
  accuracy: number; // 0-1 estimated accuracy
  consistency: number; // 0-1 internal consistency
  issues: QualityIssue[];
  suggestions: QualitySuggestion[];
}

export interface QualityIssue {
  type: 'missing_required' | 'invalid_format' | 'inconsistent' | 'suspicious';
  severity: 'low' | 'medium' | 'high';
  field: string;
  message: string;
  value?: string;
}

export interface QualitySuggestion {
  type: 'fix' | 'enhance' | 'verify';
  field: string;
  message: string;
  suggestedValue?: string;
  confidence: number;
}
