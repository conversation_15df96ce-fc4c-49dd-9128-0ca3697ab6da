/**
 * JSON Parser Service
 * Parses JSON files from Zotero CSL JSON and Mendeley exports
 */

import { 
  ProcessedCitationData, 
  CitationFileFormat, 
  ReferenceManagerSource,
  CitationDocumentType,
  ZoteroCSLItem,
  CitationImportServiceResponse 
} from '../../types/citation-import.types';

export class JSONParserService {
  /**
   * Parse JSON file content
   */
  async parseJSON(content: string): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    try {
      const data = JSON.parse(content);
      const sourceManager = this.detectJSONSource(data);
      
      let citations: ProcessedCitationData[] = [];
      
      if (sourceManager === 'zotero') {
        citations = this.parseZoteroCSL(data);
      } else if (sourceManager === 'mendeley') {
        citations = this.parseMendeleyJSON(data);
      } else {
        // Try to parse as generic JSON array
        citations = this.parseGenericJSON(data);
      }
      
      return {
        success: true,
        data: citations,
        error: null,
        warnings: this.generateWarnings(citations)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `JSON parsing failed: ${error.message}`
      };
    }
  }

  /**
   * Detect JSON source (Zotero, Mendeley, or generic)
   */
  private detectJSONSource(data: any): ReferenceManagerSource {
    // Check if it's an array
    if (!Array.isArray(data)) {
      // Check for Mendeley-specific structure
      if (data.folders || data.documents) {
        return 'mendeley';
      }
      return 'generic';
    }
    
    if (data.length === 0) return 'generic';
    
    const firstItem = data[0];
    
    // Check for Zotero CSL JSON structure
    if (firstItem.type && firstItem.title && (firstItem.author || firstItem.issued)) {
      return 'zotero';
    }
    
    // Check for Mendeley structure
    if (firstItem.title && firstItem.authors && firstItem.year) {
      return 'mendeley';
    }
    
    return 'generic';
  }

  /**
   * Parse Zotero CSL JSON format
   */
  private parseZoteroCSL(data: ZoteroCSLItem[]): ProcessedCitationData[] {
    return data.map(item => this.convertZoteroItem(item));
  }

  /**
   * Convert Zotero CSL item to ProcessedCitationData
   */
  private convertZoteroItem(item: ZoteroCSLItem): ProcessedCitationData {
    return {
      title: item.title || '',
      authors: this.parseZoteroAuthors(item.author),
      publicationYear: this.parseZoteroDate(item.issued),
      journal: item['container-title'] || undefined,
      volume: item.volume,
      issue: item.issue,
      pages: item.page,
      doi: item.DOI,
      abstract: item.abstract,
      keywords: this.parseZoteroKeywords(item.keyword),
      documentType: this.mapZoteroType(item.type),
      publisher: item.publisher,
      isbn: item.ISBN,
      issn: item.ISSN,
      language: item.language,
      url: item.URL,
      tags: [],
      notes: item.note,
      sourceFormat: 'json' as CitationFileFormat,
      sourceManager: 'zotero' as ReferenceManagerSource,
      processingConfidence: this.calculateZoteroConfidence(item),
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Parse Zotero authors
   */
  private parseZoteroAuthors(authors?: Array<{ family: string; given: string }>): string[] {
    if (!authors) return [];
    
    return authors.map(author => {
      if (author.given && author.family) {
        return `${author.given} ${author.family}`;
      } else if (author.family) {
        return author.family;
      } else if (author.given) {
        return author.given;
      }
      return '';
    }).filter(name => name.length > 0);
  }

  /**
   * Parse Zotero date
   */
  private parseZoteroDate(issued?: { 'date-parts': number[][] }): number | undefined {
    if (!issued || !issued['date-parts'] || issued['date-parts'].length === 0) {
      return undefined;
    }
    
    const dateParts = issued['date-parts'][0];
    return dateParts && dateParts.length > 0 ? dateParts[0] : undefined;
  }

  /**
   * Parse Zotero keywords
   */
  private parseZoteroKeywords(keywords?: string): string[] {
    if (!keywords) return [];
    
    return keywords
      .split(/[,;|\n]/)
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  }

  /**
   * Map Zotero type to document type
   */
  private mapZoteroType(zoteroType: string): CitationDocumentType {
    const typeMap: Record<string, CitationDocumentType> = {
      'article-journal': 'journal-article',
      'paper-conference': 'conference-paper',
      'book': 'book',
      'chapter': 'book-chapter',
      'thesis': 'thesis',
      'report': 'report',
      'webpage': 'webpage',
      'patent': 'patent',
      'manuscript': 'preprint',
      'article': 'journal-article'
    };
    
    return typeMap[zoteroType] || 'other';
  }

  /**
   * Calculate Zotero confidence
   */
  private calculateZoteroConfidence(item: ZoteroCSLItem): number {
    let score = 0;
    const maxScore = 10;
    
    if (item.title) score += 3;
    if (item.author && item.author.length > 0) score += 2;
    if (item.issued) score += 2;
    if (item['container-title']) score += 1;
    if (item.DOI) score += 1;
    if (item.abstract) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Parse Mendeley JSON format
   */
  private parseMendeleyJSON(data: any): ProcessedCitationData[] {
    // Handle Mendeley export structure
    let items = data;
    
    if (data.documents) {
      items = data.documents;
    }
    
    if (!Array.isArray(items)) {
      return [];
    }
    
    return items.map(item => this.convertMendeleyItem(item));
  }

  /**
   * Convert Mendeley item to ProcessedCitationData
   */
  private convertMendeleyItem(item: any): ProcessedCitationData {
    return {
      title: item.title || '',
      authors: this.parseMendeleyAuthors(item.authors),
      publicationYear: item.year ? parseInt(item.year) : undefined,
      journal: item.source || item.journal || undefined,
      volume: item.volume,
      issue: item.issue,
      pages: item.pages,
      doi: item.doi,
      abstract: item.abstract,
      keywords: this.parseMendeleyKeywords(item.keywords),
      documentType: this.mapMendeleyType(item.type),
      publisher: item.publisher,
      isbn: item.isbn,
      issn: item.issn,
      language: item.language,
      url: item.url || item.link,
      tags: this.parseMendeleyTags(item.tags),
      notes: item.notes,
      sourceFormat: 'json' as CitationFileFormat,
      sourceManager: 'mendeley' as ReferenceManagerSource,
      processingConfidence: this.calculateMendeleyConfidence(item),
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Parse Mendeley authors
   */
  private parseMendeleyAuthors(authors?: any[]): string[] {
    if (!authors) return [];
    
    return authors.map(author => {
      if (typeof author === 'string') {
        return author;
      } else if (author.first_name && author.last_name) {
        return `${author.first_name} ${author.last_name}`;
      } else if (author.name) {
        return author.name;
      }
      return '';
    }).filter(name => name.length > 0);
  }

  /**
   * Parse Mendeley keywords
   */
  private parseMendeleyKeywords(keywords?: string[] | string): string[] {
    if (!keywords) return [];
    
    if (Array.isArray(keywords)) {
      return keywords;
    }
    
    return keywords
      .split(/[,;|\n]/)
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  }

  /**
   * Parse Mendeley tags
   */
  private parseMendeleyTags(tags?: string[] | string): string[] {
    if (!tags) return [];
    
    if (Array.isArray(tags)) {
      return tags;
    }
    
    return tags
      .split(/[,;|\n]/)
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  /**
   * Map Mendeley type to document type
   */
  private mapMendeleyType(mendeleyType?: string): CitationDocumentType {
    if (!mendeleyType) return 'other';
    
    const typeMap: Record<string, CitationDocumentType> = {
      'journal': 'journal-article',
      'conference': 'conference-paper',
      'book': 'book',
      'book_section': 'book-chapter',
      'thesis': 'thesis',
      'report': 'report',
      'web_page': 'webpage',
      'patent': 'patent',
      'working_paper': 'preprint'
    };
    
    return typeMap[mendeleyType.toLowerCase()] || 'other';
  }

  /**
   * Calculate Mendeley confidence
   */
  private calculateMendeleyConfidence(item: any): number {
    let score = 0;
    const maxScore = 10;
    
    if (item.title) score += 3;
    if (item.authors && item.authors.length > 0) score += 2;
    if (item.year) score += 2;
    if (item.source || item.journal) score += 1;
    if (item.doi) score += 1;
    if (item.abstract) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Parse generic JSON format
   */
  private parseGenericJSON(data: any): ProcessedCitationData[] {
    if (!Array.isArray(data)) {
      return [];
    }
    
    return data.map(item => this.convertGenericItem(item));
  }

  /**
   * Convert generic JSON item to ProcessedCitationData
   */
  private convertGenericItem(item: any): ProcessedCitationData {
    return {
      title: item.title || item.Title || '',
      authors: this.parseGenericAuthors(item.authors || item.Authors || item.author),
      publicationYear: this.parseGenericYear(item.year || item.Year || item.publicationYear),
      journal: item.journal || item.Journal || item.source || undefined,
      volume: item.volume || item.Volume,
      issue: item.issue || item.Issue,
      pages: item.pages || item.Pages,
      doi: item.doi || item.DOI,
      abstract: item.abstract || item.Abstract,
      keywords: this.parseGenericKeywords(item.keywords || item.Keywords),
      documentType: 'other' as CitationDocumentType,
      publisher: item.publisher || item.Publisher,
      isbn: item.isbn || item.ISBN,
      issn: item.issn || item.ISSN,
      language: item.language || item.Language,
      url: item.url || item.URL || item.link,
      tags: [],
      notes: item.notes || item.Notes,
      sourceFormat: 'json' as CitationFileFormat,
      sourceManager: 'generic' as ReferenceManagerSource,
      processingConfidence: 0.5,
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Parse generic authors
   */
  private parseGenericAuthors(authors: any): string[] {
    if (!authors) return [];
    
    if (Array.isArray(authors)) {
      return authors.map(author => {
        if (typeof author === 'string') {
          return author;
        } else if (typeof author === 'object') {
          return author.name || `${author.first || ''} ${author.last || ''}`.trim();
        }
        return '';
      }).filter(name => name.length > 0);
    }
    
    if (typeof authors === 'string') {
      return authors.split(/[,;]/).map(author => author.trim()).filter(author => author.length > 0);
    }
    
    return [];
  }

  /**
   * Parse generic year
   */
  private parseGenericYear(year: any): number | undefined {
    if (!year) return undefined;
    
    const yearNum = parseInt(year.toString());
    return isNaN(yearNum) ? undefined : yearNum;
  }

  /**
   * Parse generic keywords
   */
  private parseGenericKeywords(keywords: any): string[] {
    if (!keywords) return [];
    
    if (Array.isArray(keywords)) {
      return keywords.map(keyword => keyword.toString()).filter(keyword => keyword.length > 0);
    }
    
    if (typeof keywords === 'string') {
      return keywords
        .split(/[,;|\n]/)
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0);
    }
    
    return [];
  }

  /**
   * Generate warnings for parsed citations
   */
  private generateWarnings(citations: ProcessedCitationData[]): string[] {
    const warnings: string[] = [];
    
    citations.forEach((citation, index) => {
      if (!citation.title) {
        warnings.push(`Citation ${index + 1}: Missing title`);
      }
      if (citation.authors.length === 0) {
        warnings.push(`Citation ${index + 1}: Missing authors`);
      }
      if (!citation.publicationYear) {
        warnings.push(`Citation ${index + 1}: Missing publication year`);
      }
    });
    
    return warnings;
  }
}

export const jsonParserService = new JSONParserService();
