/**
 * BibTeX Parser Service
 * Parses BibTeX (.bib) files and extracts citation data
 */

import {
  RawCitationData,
  ProcessedCitationData,
  CitationFileFormat,
  ReferenceManagerSource,
  CitationDocumentType,
  BibTeXEntry,
  CitationImportServiceResponse
} from '../../types/citation-import.types';
import { aiCitationEnhancerService, CitationToEnhance } from '../ai-citation-enhancer.service';

export class BibTeXParserService {
  /**
   * Parse BibTeX file content
   */
  async parseBibTeX(content: string, useAIEnhancement: boolean = true): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    try {
      const entries = this.extractBibTeXEntries(content);
      let citations = entries.map(entry => this.convertBibTeXEntry(entry));

      // Apply AI enhancement if enabled
      if (useAIEnhancement && citations.length > 0) {
        citations = await this.enhanceCitationsWithAI(citations);
      }

      return {
        success: true,
        data: citations,
        error: null,
        warnings: this.generateWarnings(citations)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `BibTeX parsing failed: ${error.message}`
      };
    }
  }

  /**
   * Extract individual BibTeX entries from file content
   */
  private extractBibTeXEntries(content: string): BibTeXEntry[] {
    const entries: BibTeXEntry[] = [];
    
    // Remove comments (lines starting with %)
    const cleanContent = content
      .split('\n')
      .filter(line => !line.trim().startsWith('%'))
      .join('\n');

    // Match BibTeX entries using regex
    const entryRegex = /@(\w+)\s*\{\s*([^,\s]+)\s*,\s*([\s\S]*?)\n\s*\}/g;
    let match;

    while ((match = entryRegex.exec(cleanContent)) !== null) {
      const [, type, key, fieldsStr] = match;
      const fields = this.parseFields(fieldsStr);
      
      entries.push({
        type: type.toLowerCase(),
        key: key.trim(),
        fields
      });
    }

    return entries;
  }

  /**
   * Parse BibTeX fields from field string
   */
  private parseFields(fieldsStr: string): Record<string, string> {
    const fields: Record<string, string> = {};
    
    // Split by commas, but respect braces and quotes
    const fieldPairs = this.splitFields(fieldsStr);
    
    for (const pair of fieldPairs) {
      const equalIndex = pair.indexOf('=');
      if (equalIndex === -1) continue;
      
      const key = pair.substring(0, equalIndex).trim().toLowerCase();
      let value = pair.substring(equalIndex + 1).trim();
      
      // Remove surrounding braces or quotes
      value = this.cleanFieldValue(value);
      
      fields[key] = value;
    }
    
    return fields;
  }

  /**
   * Split fields respecting braces and quotes
   */
  private splitFields(fieldsStr: string): string[] {
    const fields: string[] = [];
    let current = '';
    let braceLevel = 0;
    let inQuotes = false;
    let escapeNext = false;

    for (let i = 0; i < fieldsStr.length; i++) {
      const char = fieldsStr[i];
      
      if (escapeNext) {
        current += char;
        escapeNext = false;
        continue;
      }
      
      if (char === '\\') {
        escapeNext = true;
        current += char;
        continue;
      }
      
      if (char === '"' && braceLevel === 0) {
        inQuotes = !inQuotes;
        current += char;
      } else if (char === '{' && !inQuotes) {
        braceLevel++;
        current += char;
      } else if (char === '}' && !inQuotes) {
        braceLevel--;
        current += char;
      } else if (char === ',' && braceLevel === 0 && !inQuotes) {
        if (current.trim()) {
          fields.push(current.trim());
          current = '';
        }
      } else {
        current += char;
      }
    }
    
    if (current.trim()) {
      fields.push(current.trim());
    }
    
    return fields;
  }

  /**
   * Clean field value by removing braces, quotes, and LaTeX commands
   */
  private cleanFieldValue(value: string): string {
    // Remove surrounding braces or quotes
    value = value.replace(/^[\s{}"]+|[\s{}"]+$/g, '');
    
    // Remove common LaTeX commands
    value = value.replace(/\\textbf\{([^}]+)\}/g, '$1');
    value = value.replace(/\\textit\{([^}]+)\}/g, '$1');
    value = value.replace(/\\emph\{([^}]+)\}/g, '$1');
    value = value.replace(/\\url\{([^}]+)\}/g, '$1');
    value = value.replace(/\\href\{[^}]+\}\{([^}]+)\}/g, '$1');
    
    // Remove other LaTeX commands
    value = value.replace(/\\[a-zA-Z]+\{([^}]*)\}/g, '$1');
    value = value.replace(/\\[a-zA-Z]+/g, '');
    
    // Clean up whitespace
    value = value.replace(/\s+/g, ' ').trim();
    
    return value;
  }

  /**
   * Convert BibTeX entry to ProcessedCitationData
   */
  private convertBibTeXEntry(entry: BibTeXEntry): ProcessedCitationData {
    const fields = entry.fields;
    
    return {
      title: this.cleanFieldValue(fields.title || ''),
      authors: this.parseAuthors(fields.author || fields.editor || ''),
      publicationYear: this.parseYear(fields.year || fields.date),
      journal: this.cleanFieldValue(fields.journal || fields.booktitle || fields.series || ''),
      volume: fields.volume,
      issue: fields.number || fields.issue,
      pages: this.parsePages(fields.pages),
      doi: fields.doi,
      abstract: this.cleanFieldValue(fields.abstract || ''),
      keywords: this.parseKeywords(fields.keywords || fields.keyword || ''),
      documentType: this.mapBibTeXType(entry.type),
      publisher: this.cleanFieldValue(fields.publisher || ''),
      isbn: fields.isbn,
      issn: fields.issn,
      language: fields.language,
      url: fields.url || fields.link,
      tags: [],
      notes: this.cleanFieldValue(fields.note || fields.annote || ''),
      sourceFormat: 'bibtex' as CitationFileFormat,
      sourceManager: this.detectSourceManager(entry, fields),
      processingConfidence: this.calculateConfidence(fields),
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Parse author names from BibTeX format
   */
  private parseAuthors(authorStr: string): string[] {
    if (!authorStr) return [];
    
    // Split by 'and' keyword
    const authors = authorStr.split(/\s+and\s+/i);
    
    return authors.map(author => {
      author = this.cleanFieldValue(author);
      
      // Handle "Last, First" format
      if (author.includes(',')) {
        const parts = author.split(',').map(p => p.trim());
        if (parts.length >= 2) {
          return `${parts[1]} ${parts[0]}`.trim();
        }
      }
      
      return author;
    }).filter(author => author.length > 0);
  }

  /**
   * Parse publication year
   */
  private parseYear(yearStr?: string): number | undefined {
    if (!yearStr) return undefined;
    
    const match = yearStr.match(/(\d{4})/);
    return match ? parseInt(match[1]) : undefined;
  }

  /**
   * Parse page numbers
   */
  private parsePages(pagesStr?: string): string | undefined {
    if (!pagesStr) return undefined;
    
    // Convert double dashes to single dash
    return pagesStr.replace(/--/g, '-');
  }

  /**
   * Parse keywords with improved cleaning
   */
  private parseKeywords(keywordsStr: string): string[] {
    if (!keywordsStr) return [];

    // First, try to split by common separators
    let keywords = keywordsStr
      .split(/[,;\n]|(?<=[a-z])(?=[A-Z])|(?<=[a-zA-Z])(?=\d)|(?<=\d)(?=[a-zA-Z])/)
      .map(keyword => this.cleanFieldValue(keyword))
      .filter(keyword => keyword.length > 1);

    // If we still have very long keywords, try more aggressive splitting
    const finalKeywords: string[] = [];
    for (const keyword of keywords) {
      if (keyword.length > 50) {
        // Split very long keywords by camelCase and other patterns
        const split = keyword
          .split(/(?=[A-Z][a-z])|(?<=[a-z])(?=[A-Z])|(?<=[a-zA-Z])(?=\d)|(?<=\d)(?=[a-zA-Z])|\s+/)
          .map(k => k.trim())
          .filter(k => k.length > 1);
        finalKeywords.push(...split);
      } else {
        finalKeywords.push(keyword);
      }
    }

    return [...new Set(finalKeywords)]; // Remove duplicates
  }

  /**
   * Map BibTeX entry type to document type
   */
  private mapBibTeXType(bibType: string): CitationDocumentType {
    const typeMap: Record<string, CitationDocumentType> = {
      'article': 'journal-article',
      'inproceedings': 'conference-paper',
      'conference': 'conference-paper',
      'book': 'book',
      'inbook': 'book-chapter',
      'incollection': 'book-chapter',
      'phdthesis': 'thesis',
      'mastersthesis': 'thesis',
      'techreport': 'report',
      'manual': 'report',
      'misc': 'other',
      'unpublished': 'preprint'
    };
    
    return typeMap[bibType.toLowerCase()] || 'other';
  }

  /**
   * Detect source reference manager
   */
  private detectSourceManager(entry: BibTeXEntry, fields: Record<string, string>): ReferenceManagerSource {
    // Check for Zotero-specific fields
    if (fields.mendeley || fields['mendeley-groups']) {
      return 'mendeley';
    }
    
    // Check for Zotero-specific fields
    if (fields.zotero || fields['zotero-key'] || fields.file?.includes('Zotero')) {
      return 'zotero';
    }
    
    // Check for EndNote-specific fields
    if (fields.endnote || fields['endnote-id']) {
      return 'endnote';
    }
    
    return 'generic';
  }

  /**
   * Calculate processing confidence
   */
  private calculateConfidence(fields: Record<string, string>): number {
    let score = 0;
    const maxScore = 10;
    
    // Required fields
    if (fields.title) score += 3;
    if (fields.author || fields.editor) score += 2;
    if (fields.year || fields.date) score += 2;
    
    // Optional but important fields
    if (fields.journal || fields.booktitle) score += 1;
    if (fields.doi) score += 1;
    if (fields.abstract) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Enhance citations using AI
   */
  private async enhanceCitationsWithAI(citations: ProcessedCitationData[]): Promise<ProcessedCitationData[]> {
    try {
      console.log('Enhancing citations with AI...');

      // Convert to format expected by AI service
      const citationsToEnhance: CitationToEnhance[] = citations.map(citation => ({
        title: citation.title,
        authors: citation.authors,
        abstract: citation.abstract,
        keywords: citation.keywords,
        journal: citation.journal,
        publicationYear: citation.publicationYear,
        doi: citation.doi
      }));

      // Enhance in batches to avoid overwhelming the AI service
      const batchSize = 5;
      const enhancedCitations: ProcessedCitationData[] = [];

      for (let i = 0; i < citations.length; i += batchSize) {
        const batch = citationsToEnhance.slice(i, i + batchSize);
        const batchResults = await aiCitationEnhancerService.enhanceCitations(batch);

        // Apply enhancements to original citations
        for (let j = 0; j < batch.length; j++) {
          const originalIndex = i + j;
          const enhanced = batchResults[j];
          const original = citations[originalIndex];

          enhancedCitations.push({
            ...original,
            keywords: [...new Set([...enhanced.cleanedKeywords, ...enhanced.extractedKeywords])],
            tags: enhanced.suggestedTags,
            processingConfidence: enhanced.confidence,
            processingWarnings: enhanced.missingFields.map(field => `Missing ${field}`)
          });
        }

        // Add delay between batches
        if (i + batchSize < citations.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      console.log('AI enhancement completed');
      return enhancedCitations;
    } catch (error) {
      console.error('AI enhancement failed, using original citations:', error);
      return citations;
    }
  }

  /**
   * Generate warnings for parsed citations
   */
  private generateWarnings(citations: ProcessedCitationData[]): string[] {
    const warnings: string[] = [];

    citations.forEach((citation, index) => {
      if (!citation.title) {
        warnings.push(`Citation ${index + 1}: Missing title`);
      }
      if (citation.authors.length === 0) {
        warnings.push(`Citation ${index + 1}: Missing authors`);
      }
      if (!citation.publicationYear) {
        warnings.push(`Citation ${index + 1}: Missing publication year`);
      }
    });

    return warnings;
  }
}

export const bibTeXParserService = new BibTeXParserService();
