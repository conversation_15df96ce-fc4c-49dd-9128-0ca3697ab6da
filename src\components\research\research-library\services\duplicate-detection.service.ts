/**
 * Duplicate Detection Service
 * Implements fuzzy matching and similarity algorithms for citation deduplication
 */

import { 
  ProcessedCitationData,
  DuplicateDetectionResult
} from '../types/citation-import.types';

export class DuplicateDetectionService {
  /**
   * Detect if a citation is a duplicate of existing citations
   */
  async detectDuplicate(
    citation: ProcessedCitationData,
    existingCitations: ProcessedCitationData[],
    threshold: number = 0.8
  ): Promise<DuplicateDetectionResult> {
    let bestMatch: { citation: ProcessedCitationData; similarity: number } | null = null;
    
    for (const existing of existingCitations) {
      const similarity = this.calculateSimilarity(citation, existing);
      
      if (similarity >= threshold && (!bestMatch || similarity > bestMatch.similarity)) {
        bestMatch = { citation: existing, similarity };
      }
    }
    
    if (bestMatch) {
      return {
        isDuplicate: true,
        similarityScore: bestMatch.similarity,
        matchingCitation: {
          id: '', // Would be filled with actual ID
          title: bestMatch.citation.title,
          authors: bestMatch.citation.authors,
          similarity: bestMatch.similarity
        },
        suggestedAction: this.getSuggestedAction(bestMatch.similarity),
        conflictingFields: this.getConflictingFields(citation, bestMatch.citation)
      };
    }
    
    return {
      isDuplicate: false,
      similarityScore: 0,
      suggestedAction: 'keep_both'
    };
  }

  /**
   * Calculate similarity between two citations
   */
  private calculateSimilarity(citation1: ProcessedCitationData, citation2: ProcessedCitationData): number {
    const weights = {
      title: 0.4,
      authors: 0.3,
      year: 0.1,
      doi: 0.2
    };
    
    let totalSimilarity = 0;
    let totalWeight = 0;
    
    // Title similarity
    if (citation1.title && citation2.title) {
      const titleSim = this.calculateStringSimilarity(
        this.normalizeTitle(citation1.title),
        this.normalizeTitle(citation2.title)
      );
      totalSimilarity += titleSim * weights.title;
      totalWeight += weights.title;
    }
    
    // Author similarity
    if (citation1.authors.length > 0 && citation2.authors.length > 0) {
      const authorSim = this.calculateAuthorSimilarity(citation1.authors, citation2.authors);
      totalSimilarity += authorSim * weights.authors;
      totalWeight += weights.authors;
    }
    
    // Year similarity
    if (citation1.publicationYear && citation2.publicationYear) {
      const yearSim = citation1.publicationYear === citation2.publicationYear ? 1 : 0;
      totalSimilarity += yearSim * weights.year;
      totalWeight += weights.year;
    }
    
    // DOI similarity (exact match)
    if (citation1.doi && citation2.doi) {
      const doiSim = citation1.doi.toLowerCase() === citation2.doi.toLowerCase() ? 1 : 0;
      totalSimilarity += doiSim * weights.doi;
      totalWeight += weights.doi;
      
      // If DOIs match exactly, it's definitely the same paper
      if (doiSim === 1) {
        return 1.0;
      }
    }
    
    return totalWeight > 0 ? totalSimilarity / totalWeight : 0;
  }

  /**
   * Normalize title for comparison
   */
  private normalizeTitle(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, ' ')    // Normalize whitespace
      .trim();
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;
    
    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    
    return 1 - (distance / maxLength);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }
    
    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Calculate author similarity
   */
  private calculateAuthorSimilarity(authors1: string[], authors2: string[]): number {
    if (authors1.length === 0 && authors2.length === 0) return 1;
    if (authors1.length === 0 || authors2.length === 0) return 0;
    
    // Normalize author names
    const normalizedAuthors1 = authors1.map(author => this.normalizeAuthorName(author));
    const normalizedAuthors2 = authors2.map(author => this.normalizeAuthorName(author));
    
    // Calculate Jaccard similarity
    const set1 = new Set(normalizedAuthors1);
    const set2 = new Set(normalizedAuthors2);
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  /**
   * Normalize author name for comparison
   */
  private normalizeAuthorName(name: string): string {
    // Convert to "Last, First" format for consistent comparison
    const normalized = name.toLowerCase().trim();
    
    // If already in "Last, First" format
    if (normalized.includes(',')) {
      return normalized;
    }
    
    // Convert "First Last" to "Last, First"
    const parts = normalized.split(/\s+/);
    if (parts.length >= 2) {
      const firstName = parts.slice(0, -1).join(' ');
      const lastName = parts[parts.length - 1];
      return `${lastName}, ${firstName}`;
    }
    
    return normalized;
  }

  /**
   * Get suggested action based on similarity score
   */
  private getSuggestedAction(similarity: number): 'skip' | 'merge' | 'keep_both' {
    if (similarity >= 0.95) {
      return 'skip'; // Very high similarity, likely exact duplicate
    } else if (similarity >= 0.8) {
      return 'merge'; // High similarity, consider merging
    } else {
      return 'keep_both'; // Lower similarity, keep both
    }
  }

  /**
   * Get conflicting fields between two citations
   */
  private getConflictingFields(citation1: ProcessedCitationData, citation2: ProcessedCitationData): string[] {
    const conflicts: string[] = [];
    
    // Check title
    if (citation1.title && citation2.title && citation1.title !== citation2.title) {
      conflicts.push('title');
    }
    
    // Check authors
    if (!this.arraysEqual(citation1.authors, citation2.authors)) {
      conflicts.push('authors');
    }
    
    // Check year
    if (citation1.publicationYear && citation2.publicationYear && 
        citation1.publicationYear !== citation2.publicationYear) {
      conflicts.push('publicationYear');
    }
    
    // Check journal
    if (citation1.journal && citation2.journal && citation1.journal !== citation2.journal) {
      conflicts.push('journal');
    }
    
    // Check DOI
    if (citation1.doi && citation2.doi && citation1.doi !== citation2.doi) {
      conflicts.push('doi');
    }
    
    // Check volume
    if (citation1.volume && citation2.volume && citation1.volume !== citation2.volume) {
      conflicts.push('volume');
    }
    
    // Check issue
    if (citation1.issue && citation2.issue && citation1.issue !== citation2.issue) {
      conflicts.push('issue');
    }
    
    // Check pages
    if (citation1.pages && citation2.pages && citation1.pages !== citation2.pages) {
      conflicts.push('pages');
    }
    
    return conflicts;
  }

  /**
   * Check if two arrays are equal
   */
  private arraysEqual(arr1: string[], arr2: string[]): boolean {
    if (arr1.length !== arr2.length) return false;
    
    const sorted1 = [...arr1].sort();
    const sorted2 = [...arr2].sort();
    
    return sorted1.every((value, index) => value === sorted2[index]);
  }

  /**
   * Batch duplicate detection for multiple citations
   */
  async detectBatchDuplicates(
    citations: ProcessedCitationData[],
    threshold: number = 0.8
  ): Promise<Map<number, DuplicateDetectionResult[]>> {
    const results = new Map<number, DuplicateDetectionResult[]>();
    
    for (let i = 0; i < citations.length; i++) {
      const duplicates: DuplicateDetectionResult[] = [];
      
      // Check against all previous citations
      for (let j = 0; j < i; j++) {
        const similarity = this.calculateSimilarity(citations[i], citations[j]);
        
        if (similarity >= threshold) {
          duplicates.push({
            isDuplicate: true,
            similarityScore: similarity,
            matchingCitation: {
              id: j.toString(),
              title: citations[j].title,
              authors: citations[j].authors,
              similarity
            },
            suggestedAction: this.getSuggestedAction(similarity),
            conflictingFields: this.getConflictingFields(citations[i], citations[j])
          });
        }
      }
      
      if (duplicates.length > 0) {
        results.set(i, duplicates);
      }
    }
    
    return results;
  }

  /**
   * Find potential duplicates in existing library
   */
  async findLibraryDuplicates(
    newCitation: ProcessedCitationData,
    existingCitations: ProcessedCitationData[],
    threshold: number = 0.8
  ): Promise<DuplicateDetectionResult[]> {
    const duplicates: DuplicateDetectionResult[] = [];
    
    for (const existing of existingCitations) {
      const similarity = this.calculateSimilarity(newCitation, existing);
      
      if (similarity >= threshold) {
        duplicates.push({
          isDuplicate: true,
          similarityScore: similarity,
          matchingCitation: {
            id: '', // Would be filled with actual library article ID
            title: existing.title,
            authors: existing.authors,
            similarity
          },
          suggestedAction: this.getSuggestedAction(similarity),
          conflictingFields: this.getConflictingFields(newCitation, existing)
        });
      }
    }
    
    return duplicates.sort((a, b) => b.similarityScore - a.similarityScore);
  }
}

export const duplicateDetectionService = new DuplicateDetectionService();
