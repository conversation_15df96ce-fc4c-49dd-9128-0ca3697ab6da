/**
 * Enhanced Self-Review Export Service
 * Provides beautiful Word and PDF export functionality for enhanced self-review results
 * with proper formatting, bold highlights, and comprehensive content
 */

import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, BorderStyle, Table, TableRow, TableCell, WidthType } from 'docx';
import jsPDF from 'jspdf';
import { EnhancedSelfReviewResult, ExportOptions, ArticleSection } from '../types';

/**
 * Export enhanced self-review results to Word format with beautiful formatting
 */
export async function exportEnhancedSelfReviewToDocx(
  result: EnhancedSelfReviewResult, 
  options: ExportOptions
): Promise<void> {
  try {
    const doc = await createEnhancedSelfReviewDocx(result, options);
    const blob = await Packer.toBlob(doc);
    const fileName = `enhanced-self-review-${new Date().toISOString().split('T')[0]}.docx`;
    saveAs(blob, fileName);
  } catch (error) {
    console.error('Error exporting enhanced self-review to DOCX:', error);
    throw error;
  }
}

/**
 * Export enhanced self-review results to PDF format
 */
export async function exportEnhancedSelfReviewToPdf(
  result: EnhancedSelfReviewResult, 
  options: ExportOptions
): Promise<void> {
  try {
    // Create HTML content for PDF generation
    const htmlContent = generateEnhancedSelfReviewHtml(result, options);
    
    // For now, download as HTML (can be opened in browser and printed to PDF)
    // In production, you might want to use a proper PDF generation library
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const fileName = `enhanced-self-review-${new Date().toISOString().split('T')[0]}.html`;
    saveAs(blob, fileName);
    
    console.log('PDF export completed (HTML format for browser printing)');
  } catch (error) {
    console.error('Error exporting enhanced self-review to PDF:', error);
    throw error;
  }
}

/**
 * Create a beautifully formatted Word document
 */
async function createEnhancedSelfReviewDocx(
  result: EnhancedSelfReviewResult, 
  options: ExportOptions
): Promise<Document> {
  const FONT = {
    name: "Times New Roman",
    ascii: "Times New Roman",
    eastAsia: "Times New Roman",
    hAnsi: "Times New Roman"
  };

  const documentChildren = [];

  // Title Page
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Enhanced Article Self-Review Report",
          bold: true,
          size: 32,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { after: 400 },
      alignment: AlignmentType.CENTER
    })
  );

  // Article Information
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Article Information",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  // Add article details
  const originalArticle = result.originalSelfReview.articleId;
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Article ID: ${originalArticle}`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Generated: ${result.generatedAt.toLocaleDateString()} at ${result.generatedAt.toLocaleTimeString()}`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 200 }
    })
  );

  // Processing Summary
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Processing Summary",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  const metadata = result.processingMetadata;
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Processing Time: ${metadata.totalProcessingTime} seconds`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Quality Score: ${metadata.qualityScore}/100`,
          size: 22,
          font: FONT,
          bold: true
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Steps Completed: ${metadata.stepsCompleted}`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 200 }
    })
  );

  // Change Overview
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Change Overview",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  const changeDoc = result.changeDocumentation;
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Total Changes: ${changeDoc.overview.totalChanges}`,
          size: 22,
          font: FONT,
          bold: true
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Sections Modified: ${changeDoc.overview.sectionsModified.join(', ')}`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Improvement Areas:",
          size: 22,
          font: FONT,
          bold: true
        }),
      ],
      spacing: { after: 100 }
    })
  );

  changeDoc.overview.improvementAreas.forEach(area => {
    documentChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `• ${area}`,
            size: 22,
            font: FONT
          }),
        ],
        spacing: { after: 50 }
      })
    );
  });

  // Quality Improvements
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Quality Improvements",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  const qualityImp = changeDoc.qualityImprovements;
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Before Score: ${qualityImp.beforeScore}/100`,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 100 }
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `After Score: ${qualityImp.afterScore}/100`,
          size: 22,
          font: FONT,
          bold: true,
          color: "008000"
        }),
      ],
      spacing: { after: 100 }
    })
  );

  const improvement = qualityImp.afterScore - qualityImp.beforeScore;
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Improvement: +${improvement} points`,
          size: 22,
          font: FONT,
          bold: true,
          color: improvement > 0 ? "008000" : "FF0000"
        }),
      ],
      spacing: { after: 200 }
    })
  );

  // Enhanced Sections with AI Fixes
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Enhanced Article Sections",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  // Add each enhanced section
  result.enhancedSections.forEach((section, index) => {
    // Section header
    documentChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `${index + 1}. ${section.section.toUpperCase()}`,
            bold: true,
            size: 20,
            color: "2c3e50",
            font: FONT
          }),
        ],
        spacing: { before: 300, after: 150 },
        heading: HeadingLevel.HEADING_2
      })
    );

    // Original vs Enhanced comparison
    if (section.originalContent && section.enhancedContent) {
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "Original Content:",
              bold: true,
              size: 18,
              color: "666666",
              font: FONT
            }),
          ],
          spacing: { before: 200, after: 100 }
        })
      );

      // Add original content
      const originalParagraphs = section.originalContent.split('\n\n');
      originalParagraphs.forEach(para => {
        if (para.trim()) {
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: para.trim(),
                  size: 22,
                  font: FONT,
                  color: "666666"
                }),
              ],
              spacing: { after: 100 }
            })
          );
        }
      });

      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "Enhanced Content (Changes in Bold):",
              bold: true,
              size: 18,
              color: "008000",
              font: FONT
            }),
          ],
          spacing: { before: 200, after: 100 }
        })
      );

      // Add enhanced content with bold formatting for changes
      const enhancedParagraphs = section.enhancedContent.split('\n\n');
      enhancedParagraphs.forEach(para => {
        if (para.trim()) {
          const textRuns = parseEnhancedContent(para.trim(), FONT);
          documentChildren.push(
            new Paragraph({
              children: textRuns,
              spacing: { after: 100 }
            })
          );
        }
      });
    }

    // AI Analysis and Suggestions
    if (section.aiAnalysis) {
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "AI Analysis & Suggestions:",
              bold: true,
              size: 18,
              color: "3498db",
              font: FONT
            }),
          ],
          spacing: { before: 200, after: 100 }
        })
      );

      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: section.aiAnalysis,
              size: 22,
              font: FONT,
              italics: true
            }),
          ],
          spacing: { after: 100 }
        })
      );
    }

    // Changes Made
    if (section.changesMade && section.changesMade.length > 0) {
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "Changes Made:",
              bold: true,
              size: 18,
              color: "e74c3c",
              font: FONT
            }),
          ],
          spacing: { before: 200, after: 100 }
        })
      );

      section.changesMade.forEach(change => {
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: `• ${change.description} (${change.type})`,
                size: 22,
                font: FONT
              }),
            ],
            spacing: { after: 50 }
          })
        );

        if (change.reason) {
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `  Reason: ${change.reason}`,
                  size: 20,
                  font: FONT,
                  italics: true,
                  color: "666666"
                }),
              ],
              spacing: { after: 50 }
            })
          );
        }
      });
    }
  });

  // Section-by-Section Change Documentation
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Detailed Change Documentation",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  Object.entries(changeDoc.sectionBySection).forEach(([sectionName, sectionData]) => {
    if (sectionData) {
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: sectionName.toUpperCase(),
              bold: true,
              size: 20,
              color: "2c3e50",
              font: FONT
            }),
          ],
          spacing: { before: 300, after: 150 },
          heading: HeadingLevel.HEADING_2
        })
      );

      // Overall improvement
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Overall Improvement: ${sectionData.overallImprovement}`,
              size: 22,
              font: FONT,
              bold: true
            }),
          ],
          spacing: { after: 100 }
        })
      );

      // Changes
      if (sectionData.changes && sectionData.changes.length > 0) {
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "Changes Made:",
                bold: true,
                size: 18,
                font: FONT
              }),
            ],
            spacing: { before: 100, after: 50 }
          })
        );

        sectionData.changes.forEach(change => {
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `• ${change.description} (${change.type}, ${change.impact} impact)`,
                  size: 22,
                  font: FONT
                }),
              ],
              spacing: { after: 50 }
            })
          );

          if (change.reason) {
            documentChildren.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: `  Reason: ${change.reason}`,
                    size: 20,
                    font: FONT,
                    italics: true,
                    color: "666666"
                  }),
                ],
                spacing: { after: 50 }
              })
            );
          }
        });
      }

      // Additional recommendations
      if (sectionData.additionalRecommendations && sectionData.additionalRecommendations.length > 0) {
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "Additional Recommendations:",
                bold: true,
                size: 18,
                font: FONT
              }),
            ],
            spacing: { before: 100, after: 50 }
          })
        );

        sectionData.additionalRecommendations.forEach(rec => {
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `• ${rec}`,
                  size: 22,
                  font: FONT
                }),
              ],
              spacing: { after: 50 }
            })
          );
        });
      }
    }
  });

  // Next Steps
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "Next Steps",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  changeDoc.nextSteps.forEach(step => {
    documentChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `• ${step}`,
            size: 22,
            font: FONT
          }),
        ],
        spacing: { after: 100 }
      })
    );
  });

  // User Guidance
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "User Guidance",
          bold: true,
          size: 24,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { before: 400, after: 200 },
      heading: HeadingLevel.HEADING_1
    })
  );

  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: result.userGuidance,
          size: 22,
          font: FONT
        }),
      ],
      spacing: { after: 200 }
    })
  );

  return new Document({
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,  // 1 inch
              bottom: 1440, // 1 inch
              left: 1440,   // 1 inch
            },
          },
        },
        children: documentChildren,
      },
    ],
  });
}

/**
 * Parse enhanced content and create text runs with bold formatting for changes
 */
function parseEnhancedContent(content: string, font: any): TextRun[] {
  const runs: TextRun[] = [];

  // Split content by **bold** markers
  const parts = content.split(/(\*\*.*?\*\*)/g);

  parts.forEach(part => {
    if (part.startsWith('**') && part.endsWith('**')) {
      // This is bold text (AI changes)
      const boldText = part.slice(2, -2);
      runs.push(
        new TextRun({
          text: boldText,
          size: 22,
          font: font,
          bold: true,
          color: "008000" // Green for changes
        })
      );
    } else if (part.trim()) {
      // Regular text
      runs.push(
        new TextRun({
          text: part,
          size: 22,
          font: font
        })
      );
    }
  });

  return runs.length > 0 ? runs : [
    new TextRun({
      text: content,
      size: 22,
      font: font
    })
  ];
}

/**
 * Generate HTML content for PDF export
 */
function generateEnhancedSelfReviewHtml(
  result: EnhancedSelfReviewResult, 
  options: ExportOptions
): string {
  const changeDoc = result.changeDocumentation;
  const metadata = result.processingMetadata;
  const qualityImp = changeDoc.qualityImprovements;
  
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Enhanced Article Self-Review Report</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .subsection-title {
            font-size: 16px;
            font-weight: bold;
            color: #34495e;
            margin-bottom: 10px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .improvement {
            color: #28a745;
            font-weight: bold;
        }
        .score {
            font-size: 18px;
            font-weight: bold;
        }
        .list-item {
            margin-bottom: 5px;
        }
        .metadata {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        @media print {
            body { margin: 20px; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Enhanced Article Self-Review Report</div>
        <div>Generated: ${result.generatedAt.toLocaleDateString()} at ${result.generatedAt.toLocaleTimeString()}</div>
    </div>

    <div class="section">
        <div class="section-title">Processing Summary</div>
        <div class="metadata">
            <div><strong>Processing Time:</strong> ${metadata.totalProcessingTime} seconds</div>
            <div><strong>Quality Score:</strong> <span class="score">${metadata.qualityScore}/100</span></div>
            <div><strong>Steps Completed:</strong> ${metadata.stepsCompleted}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Change Overview</div>
        <div><strong>Total Changes:</strong> <span class="highlight">${changeDoc.overview.totalChanges}</span></div>
        <div><strong>Sections Modified:</strong> ${changeDoc.overview.sectionsModified.join(', ')}</div>
        <div class="subsection-title">Improvement Areas:</div>
        <ul>
            ${changeDoc.overview.improvementAreas.map(area => `<li class="list-item">${area}</li>`).join('')}
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Quality Improvements</div>
        <div><strong>Before Score:</strong> ${qualityImp.beforeScore}/100</div>
        <div><strong>After Score:</strong> <span class="improvement">${qualityImp.afterScore}/100</span></div>
        <div><strong>Improvement:</strong> <span class="improvement">+${qualityImp.afterScore - qualityImp.beforeScore} points</span></div>
        
        <div class="subsection-title">Improvement Areas:</div>
        <ul>
            ${qualityImp.improvementAreas.map(area => `<li class="list-item">${area}</li>`).join('')}
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Next Steps</div>
        <ul>
            ${changeDoc.nextSteps.map(step => `<li class="list-item">${step}</li>`).join('')}
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Enhanced Article Sections</div>
        ${result.enhancedSections.map((section, index) => `
            <div class="section">
                <div class="subsection-title">${index + 1}. ${section.section.toUpperCase()}</div>

                ${section.originalContent ? `
                    <div class="subsection-title">Original Content:</div>
                    <div style="color: #666; margin-bottom: 15px;">${section.originalContent.replace(/\n/g, '<br>')}</div>
                ` : ''}

                ${section.enhancedContent ? `
                    <div class="subsection-title">Enhanced Content (Changes in Bold):</div>
                    <div style="margin-bottom: 15px;">${formatEnhancedContent(section.enhancedContent)}</div>
                ` : ''}

                ${section.aiAnalysis ? `
                    <div class="subsection-title">AI Analysis & Suggestions:</div>
                    <div style="font-style: italic; color: #3498db; margin-bottom: 15px;">${section.aiAnalysis}</div>
                ` : ''}

                ${section.changesMade && section.changesMade.length > 0 ? `
                    <div class="subsection-title">Changes Made:</div>
                    <ul>
                        ${section.changesMade.map(change => `
                            <li class="list-item">
                                <strong>${change.description}</strong> (${change.type})
                                ${change.reason ? `<br><em>Reason: ${change.reason}</em>` : ''}
                            </li>
                        `).join('')}
                    </ul>
                ` : ''}
            </div>
        `).join('')}
    </div>

    <div class="section">
        <div class="section-title">Detailed Change Documentation</div>
        ${Object.entries(changeDoc.sectionBySection).map(([sectionName, sectionData]) => {
            if (!sectionData) return '';
            return `
                <div class="section">
                    <div class="subsection-title">${sectionName.toUpperCase()}</div>
                    <div><strong>Overall Improvement:</strong> ${sectionData.overallImprovement}</div>

                    ${sectionData.changes && sectionData.changes.length > 0 ? `
                        <div class="subsection-title">Changes Made:</div>
                        <ul>
                            ${sectionData.changes.map(change => `
                                <li class="list-item">
                                    <strong>${change.description}</strong> (${change.type}, ${change.impact} impact)
                                    ${change.reason ? `<br><em>Reason: ${change.reason}</em>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    ` : ''}

                    ${sectionData.additionalRecommendations && sectionData.additionalRecommendations.length > 0 ? `
                        <div class="subsection-title">Additional Recommendations:</div>
                        <ul>
                            ${sectionData.additionalRecommendations.map(rec => `<li class="list-item">${rec}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            `;
        }).join('')}
    </div>

    <div class="section">
        <div class="section-title">Next Steps</div>
        <ul>
            ${changeDoc.nextSteps.map(step => `<li class="list-item">${step}</li>`).join('')}
        </ul>
    </div>

    <div class="section">
        <div class="section-title">User Guidance</div>
        <div>${result.userGuidance}</div>
    </div>
</body>
</html>`;
}

/**
 * Format enhanced content with bold highlighting for changes
 */
function formatEnhancedContent(content: string): string {
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong style="color: #28a745; background-color: #d4edda; padding: 2px 4px; border-radius: 3px;">$1</strong>')
    .replace(/\n/g, '<br>');
}
