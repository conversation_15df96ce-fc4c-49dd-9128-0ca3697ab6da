/**
 * Paper Generator V2 - Main Exports
 * Advanced AI-powered academic paper generator with flexible section-based writing
 */

// Main component
export { PaperGeneratorV2 } from './components/PaperGeneratorV2';
export { EnhancedPaperGeneratorV2 } from './components/EnhancedPaperGeneratorV2';

// UI Components
export { SectionSelector } from './components/SectionSelector';
export { SectionEditor } from './components/SectionEditor';
export { EnhancedSectionEditor } from './components/EnhancedSectionEditor';
export { OutlineManager } from './components/OutlineManager';
export { ContentBlockEditor } from './components/ContentBlockEditor';
export { AIModelSelector } from './components/AIModelSelector';
export { ProgressTracker } from './components/ProgressTracker';
export { PaperMetadataForm } from './components/PaperMetadataForm';
export { ReviewPanel } from './components/ReviewPanel';
export { ExportDialog } from './components/ExportDialog';
export { CompleteArticleView } from './components/CompleteArticleView';

// Services
export { paperAIServiceV2 } from './services/paper-ai-v2.service';
export { sectionContextService } from './services/section-context.service';
export { hierarchicalContentService } from './services/hierarchical-content.service';
// Research library services
export { researchLibraryService } from './services/research-library.service';
export { articleExtractionService } from './services/article-extraction.service';
export { paperPersistenceV2Service } from './services/paper-persistence-v2.service';
export { tavilyIntegrationService } from './services/tavily-integration.service';
export { aiResearchContextService } from './services/ai-research-context.service';

// Utilities
export * from './utils/content-block.utils';

// Types
export type {
  Paper,
  PaperMetadata,
  PaperSection,
  SubSection,
  SectionType,
  AIModel,
  GenerationOptions,
  GenerationRequest,
  GenerationResponse,
  SectionContext,
  ReviewResult,
  ReviewCriteria,
  ReviewFeedback,
  UIState,
  ProgressMetrics,
  ExportOptions,
  Citation,
  Figure
} from './types';

// Research library types
export type {
  ResearchLibrary,
  ResearchLibraryInsert,
  ResearchLibraryUpdate,
  LibraryArticle,
  LibraryArticleInsert,
  LibraryArticleUpdate,
  PDFExtractionResult,
  SearchResultForLibrary,
  ResearchContext,
  ArticleSearchOptions,
  ArticleSearchResult,
  LibraryStatistics,
  ResearchLibraryAnalytics,
  FileUploadProgress,
  BatchArticleOperation,
  LibraryExportOptions,
  LibraryImportOptions,
  LibraryCitation,
  ResearchLibraryServiceResponse,
  PaperWithResearchLibrary,
  AIGenerationWithResearchContext,
  ResearchAwareAIResponse
} from './types/research-library.types';

// Constants
export {
  SECTION_TYPES,
  AI_MODELS,
  JOURNAL_FORMATS,
  DEFAULT_GENERATION_OPTIONS,
  REVIEW_WEIGHTS,
  EXPORT_TEMPLATES,
  CITATION_STYLES
} from './constants';
