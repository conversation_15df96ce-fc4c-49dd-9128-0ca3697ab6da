import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Copy,
  Download,
  Plus,
  Trash2,
  ExternalLink,
  BookOpen,
  FileText,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';

interface Reference {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  url?: string;
  doi?: string;
  abstract?: string;
  formattedCitation?: string;
}

interface ReferencesManagerProps {
  references: Reference[];
  onReferencesUpdate?: (references: Reference[]) => void;
  onAddToEditor?: (referencesText: string) => void;
  className?: string;
}

export const ReferencesManager: React.FC<ReferencesManagerProps> = ({
  references,
  onReferencesUpdate,
  onAddToEditor,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [selectedReferences, setSelectedReferences] = useState<Set<string>>(new Set());

  // Format a single reference in APA style
  const formatReferenceAPA = useCallback((ref: Reference): string => {
    if (ref.formattedCitation) {
      return ref.formattedCitation;
    }

    const authors = ref.authors.length > 0 ? ref.authors.join(', ') : 'Unknown Author';
    const year = ref.year || 'n.d.';
    const title = ref.title || 'Untitled';
    const source = ref.source || '';
    
    let formatted = `${authors} (${year}). ${title}`;
    if (source) {
      formatted += `. ${source}`;
    }
    if (ref.doi) {
      formatted += `. https://doi.org/${ref.doi}`;
    } else if (ref.url) {
      formatted += `. Retrieved from ${ref.url}`;
    }
    
    return formatted;
  }, []);

  // Format all references for copying/adding to editor
  const formatAllReferences = useCallback((): string => {
    const sortedRefs = [...references].sort((a, b) => {
      const authorA = a.authors[0]?.split(',')[0] || a.authors[0] || '';
      const authorB = b.authors[0]?.split(',')[0] || b.authors[0] || '';
      return authorA.localeCompare(authorB);
    });

    const formattedRefs = sortedRefs.map(ref => formatReferenceAPA(ref));
    
    return `# References\n\n${formattedRefs.map(ref => `${ref}\n`).join('\n')}`;
  }, [references, formatReferenceAPA]);

  // Copy all references to clipboard
  const copyAllReferences = useCallback(async () => {
    try {
      const referencesText = formatAllReferences();
      await navigator.clipboard.writeText(referencesText);
      toast.success(`Copied ${references.length} references to clipboard`);
    } catch (error) {
      console.error('Failed to copy references:', error);
      toast.error('Failed to copy references to clipboard');
    }
  }, [formatAllReferences, references.length]);

  // Copy selected references to clipboard
  const copySelectedReferences = useCallback(async () => {
    try {
      const selectedRefs = references.filter(ref => selectedReferences.has(ref.id));
      const sortedRefs = [...selectedRefs].sort((a, b) => {
        const authorA = a.authors[0]?.split(',')[0] || a.authors[0] || '';
        const authorB = b.authors[0]?.split(',')[0] || b.authors[0] || '';
        return authorA.localeCompare(authorB);
      });

      const formattedRefs = sortedRefs.map(ref => formatReferenceAPA(ref));
      const referencesText = `# References\n\n${formattedRefs.map(ref => `${ref}\n`).join('\n')}`;
      
      await navigator.clipboard.writeText(referencesText);
      toast.success(`Copied ${selectedRefs.length} selected references to clipboard`);
    } catch (error) {
      console.error('Failed to copy selected references:', error);
      toast.error('Failed to copy selected references to clipboard');
    }
  }, [references, selectedReferences, formatReferenceAPA]);

  // Add references to editor
  const addToEditor = useCallback(() => {
    if (onAddToEditor) {
      const referencesText = formatAllReferences();
      onAddToEditor(referencesText);
      toast.success(`Added ${references.length} references to editor`);
    }
  }, [formatAllReferences, onAddToEditor, references.length]);

  // Toggle reference selection
  const toggleReferenceSelection = useCallback((refId: string) => {
    setSelectedReferences(prev => {
      const newSet = new Set(prev);
      if (newSet.has(refId)) {
        newSet.delete(refId);
      } else {
        newSet.add(refId);
      }
      return newSet;
    });
  }, []);

  // Select all references
  const selectAllReferences = useCallback(() => {
    setSelectedReferences(new Set(references.map(ref => ref.id)));
  }, [references]);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedReferences(new Set());
  }, []);

  if (references.length === 0) {
    return null;
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-blue-600" />
            <CardTitle className="text-lg">References Manager</CardTitle>
            <Badge variant="secondary" className="ml-2">
              {references.length} sources
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1"
          >
            {isExpanded ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </Button>
        </div>
        
        {isExpanded && (
          <div className="flex flex-wrap gap-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={copyAllReferences}
              className="flex items-center gap-1"
            >
              <Copy className="w-3 h-3" />
              Copy All
            </Button>
            
            {selectedReferences.size > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={copySelectedReferences}
                className="flex items-center gap-1"
              >
                <Copy className="w-3 h-3" />
                Copy Selected ({selectedReferences.size})
              </Button>
            )}
            
            {onAddToEditor && (
              <Button
                variant="default"
                size="sm"
                onClick={addToEditor}
                className="flex items-center gap-1"
              >
                <Plus className="w-3 h-3" />
                Add to Editor
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={selectedReferences.size === references.length ? clearSelection : selectAllReferences}
              className="flex items-center gap-1"
            >
              <CheckCircle className="w-3 h-3" />
              {selectedReferences.size === references.length ? 'Clear All' : 'Select All'}
            </Button>
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {references.map((ref, index) => {
                const isSelected = selectedReferences.has(ref.id);
                const formattedRef = formatReferenceAPA(ref);
                
                return (
                  <div
                    key={ref.id}
                    className={`border rounded-lg p-3 transition-colors cursor-pointer ${
                      isSelected ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                    onClick={() => toggleReferenceSelection(ref.id)}
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm text-gray-600">
                            {index + 1}.
                          </span>
                          <div className="flex items-center gap-1">
                            {isSelected && <CheckCircle className="w-4 h-4 text-blue-600" />}
                            <span className="font-medium text-sm">
                              {ref.title}
                            </span>
                          </div>
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Authors: <AUTHORS>
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Year:</span> {ref.year}
                        </div>
                        
                        {ref.source && (
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Source:</span> {ref.source}
                          </div>
                        )}
                        
                        <div className="text-sm text-gray-700 bg-white p-2 rounded border">
                          <span className="font-medium">Formatted Citation:</span>
                          <div className="mt-1 italic">{formattedRef}</div>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-1">
                        {ref.url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(ref.url, '_blank');
                            }}
                            className="p-1 h-auto"
                            title="Open source"
                          >
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={async (e) => {
                            e.stopPropagation();
                            try {
                              await navigator.clipboard.writeText(formattedRef);
                              toast.success('Reference copied to clipboard');
                            } catch (error) {
                              toast.error('Failed to copy reference');
                            }
                          }}
                          className="p-1 h-auto"
                          title="Copy reference"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      )}
    </Card>
  );
};
