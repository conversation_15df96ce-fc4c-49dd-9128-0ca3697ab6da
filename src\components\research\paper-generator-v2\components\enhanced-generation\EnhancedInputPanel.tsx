import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  Plus,
  X,
  Target,
  Lightbulb,
  Type,
  Hash,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface EnhancedInputs {
  prompt: string;
  requirements: string;
  keyPoints: string[];
  writingStyle: string;
  targetWordCount: number;
  specificInstructions: string;
}

interface EnhancedInputPanelProps {
  inputs: EnhancedInputs;
  onInputsChange: (inputs: EnhancedInputs) => void;
  sectionType: string;
  className?: string;
}

const WRITING_STYLES = [
  'Academic & Formal',
  'Technical & Precise',
  'Clear & Accessible',
  'Detailed & Comprehensive',
  'Concise & Direct'
];

const WORD_COUNT_PRESETS = [
  { label: 'Brief', value: 150 },
  { label: 'Standard', value: 300 },
  { label: 'Detailed', value: 500 },
  { label: 'Comprehensive', value: 800 },
  { label: 'Custom', value: 0 }
];

export function EnhancedInputPanel({
  inputs,
  onInputsChange,
  sectionType,
  className = ""
}: EnhancedInputPanelProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [newKeyPoint, setNewKeyPoint] = useState('');

  const updateInputs = (updates: Partial<EnhancedInputs>) => {
    onInputsChange({ ...inputs, ...updates });
  };

  const addKeyPoint = () => {
    if (newKeyPoint.trim()) {
      updateInputs({
        keyPoints: [...inputs.keyPoints, newKeyPoint.trim()]
      });
      setNewKeyPoint('');
    }
  };

  const removeKeyPoint = (index: number) => {
    updateInputs({
      keyPoints: inputs.keyPoints.filter((_, i) => i !== index)
    });
  };

  const handleWordCountPreset = (value: number) => {
    if (value === 0) {
      // Custom - don't change the current value
      return;
    }
    updateInputs({ targetWordCount: value });
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base">
            <FileText className="h-4 w-4" />
            Inputs
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          </Button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Main Prompt */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Main Prompt *
            </label>
            <Textarea
              value={inputs.prompt}
              onChange={(e) => updateInputs({ prompt: e.target.value })}
              placeholder={`Describe what you want to generate for this ${sectionType}...`}
              className="min-h-[80px]"
            />
          </div>

          {/* Requirements */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Specific Requirements
            </label>
            <Textarea
              value={inputs.requirements}
              onChange={(e) => updateInputs({ requirements: e.target.value })}
              placeholder="Any specific requirements, constraints, or guidelines..."
              className="min-h-[60px]"
            />
          </div>

          {/* Key Points */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Key Points to Include
            </label>
            
            {/* Existing Key Points */}
            {inputs.keyPoints.length > 0 && (
              <div className="space-y-2 mb-3">
                {inputs.keyPoints.map((point, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-blue-50 rounded border border-blue-200">
                    <Lightbulb className="h-3 w-3 text-blue-600 flex-shrink-0" />
                    <span className="text-sm text-blue-900 flex-1">{point}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeKeyPoint(index)}
                      className="h-6 w-6 p-0 text-blue-600 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Key Point */}
            <div className="flex gap-2">
              <Input
                value={newKeyPoint}
                onChange={(e) => setNewKeyPoint(e.target.value)}
                placeholder="Add a key point..."
                className="flex-1"
                onKeyPress={(e) => e.key === 'Enter' && addKeyPoint()}
              />
              <Button
                onClick={addKeyPoint}
                disabled={!newKeyPoint.trim()}
                size="sm"
                className="flex items-center gap-1"
              >
                <Plus className="h-3 w-3" />
                Add
              </Button>
            </div>
          </div>

          <Separator />

          {/* Writing Style */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Writing Style
            </label>
            <div className="grid grid-cols-1 gap-2">
              {WRITING_STYLES.map((style) => (
                <Button
                  key={style}
                  variant={inputs.writingStyle === style ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateInputs({ writingStyle: style })}
                  className="justify-start"
                >
                  <Type className="h-3 w-3 mr-2" />
                  {style}
                </Button>
              ))}
            </div>
          </div>

          {/* Target Word Count */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Target Word Count
            </label>
            
            {/* Presets */}
            <div className="grid grid-cols-3 gap-2 mb-2">
              {WORD_COUNT_PRESETS.map((preset) => (
                <Button
                  key={preset.label}
                  variant={
                    preset.value === 0 
                      ? 'outline' 
                      : inputs.targetWordCount === preset.value 
                        ? 'default' 
                        : 'outline'
                  }
                  size="sm"
                  onClick={() => handleWordCountPreset(preset.value)}
                  className="text-xs"
                >
                  {preset.label}
                  {preset.value > 0 && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {preset.value}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>

            {/* Custom Input */}
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-gray-500" />
              <Input
                type="number"
                value={inputs.targetWordCount}
                onChange={(e) => updateInputs({ targetWordCount: parseInt(e.target.value) || 0 })}
                placeholder="Custom word count..."
                className="flex-1"
                min="0"
                max="2000"
              />
              <span className="text-xs text-gray-500">words</span>
            </div>
          </div>

          {/* Specific Instructions */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Additional Instructions
            </label>
            <Textarea
              value={inputs.specificInstructions}
              onChange={(e) => updateInputs({ specificInstructions: e.target.value })}
              placeholder="Any other specific instructions for the AI..."
              className="min-h-[60px]"
            />
          </div>

          {/* Input Summary */}
          <div className="pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-600 space-y-1">
              <div className="flex items-center justify-between">
                <span>Key points:</span>
                <Badge variant="outline" className="text-xs">
                  {inputs.keyPoints.length}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Target length:</span>
                <Badge variant="outline" className="text-xs">
                  {inputs.targetWordCount || 'Not set'} words
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Style:</span>
                <Badge variant="outline" className="text-xs">
                  {inputs.writingStyle || 'Not selected'}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
