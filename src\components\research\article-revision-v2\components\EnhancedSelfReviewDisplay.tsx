/**
 * Enhanced Self-Review Display Component
 * Displays enhanced self-review results with bold changes, change documentation, and side-by-side comparison
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  CheckCircle, 
  Download,
  FileText,
  Eye,
  EyeOff,
  ArrowLeft,
  Zap,
  Star,
  TrendingUp,
  FileDown,
  BookOpen,
  Target
} from 'lucide-react';

import { CompleteArticle, ArticleSection } from '../types';
import { EnhancedSelfReviewResult } from '../services/enhanced-self-review-ai.service';

interface EnhancedSelfReviewDisplayProps {
  article: CompleteArticle;
  enhancedResult: EnhancedSelfReviewResult;
  onBack: () => void;
  onExportPDF?: () => void;
  onExportWord?: () => void;
}

export function EnhancedSelfReviewDisplay({
  article,
  enhancedResult,
  onBack,
  onExportPDF,
  onExportWord
}: EnhancedSelfReviewDisplayProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSection, setSelectedSection] = useState<ArticleSection | null>(null);
  const [showOriginalContent, setShowOriginalContent] = useState<{ [key: string]: boolean }>({});

  // Toggle original content visibility for a section
  const toggleOriginalContent = (section: ArticleSection) => {
    setShowOriginalContent(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Format section title
  const formatSectionTitle = (section: ArticleSection) => {
    return section.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Get improvement color based on score
  const getImprovementColor = (beforeScore: number, afterScore: number) => {
    const improvement = afterScore - beforeScore;
    if (improvement >= 15) return 'text-green-600';
    if (improvement >= 5) return 'text-blue-600';
    return 'text-gray-600';
  };

  // Render enhanced content with bold highlighting
  const renderEnhancedContent = (content: string) => {
    return (
      <div 
        className="prose prose-sm max-w-none leading-relaxed text-gray-800"
        dangerouslySetInnerHTML={{ 
          __html: content
            .replace(/\*\*(.*?)\*\*/g, '<strong class="bg-yellow-200 px-1 rounded font-semibold text-gray-900">$1</strong>')
            .replace(/\n/g, '<br />') 
        }}
      />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Analysis
            </Button>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">
                Enhanced Self-Review Results
              </h1>
              <p className="text-lg text-gray-600 mt-2">
                AI-powered article improvements with highlighted changes
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-green-700 border-green-300">
              <CheckCircle className="h-4 w-4 mr-1" />
              Enhancement Complete
            </Badge>
            <Badge variant="outline" className="text-blue-700 border-blue-300">
              Quality Score: {enhancedResult.processingMetadata.qualityScore}/100
            </Badge>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3 mb-6">
          {onExportPDF && (
            <Button onClick={onExportPDF} variant="outline" className="flex items-center gap-2">
              <FileDown className="h-4 w-4" />
              Export PDF
            </Button>
          )}
          {onExportWord && (
            <Button onClick={onExportWord} variant="outline" className="flex items-center gap-2">
              <FileDown className="h-4 w-4" />
              Export Word
            </Button>
          )}
          <Button variant="outline" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            View User Guide
          </Button>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="sections" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Enhanced Sections
            </TabsTrigger>
            <TabsTrigger value="changes" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Change Documentation
            </TabsTrigger>
            <TabsTrigger value="guidance" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              User Guidance
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="border-gray-200 shadow-lg">
              <CardHeader className="bg-gray-50">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <Zap className="h-6 w-6" />
                  Enhancement Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Processing Summary</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Total Sections:</span>
                        <span className="font-medium">{enhancedResult.step2_enhancedArticle.overallImprovements.totalSections}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sections Enhanced:</span>
                        <span className="font-medium text-green-600">
                          {enhancedResult.step2_enhancedArticle.overallImprovements.sectionsEnhanced}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Major Improvements:</span>
                        <span className="font-medium text-blue-600">
                          {enhancedResult.step2_enhancedArticle.overallImprovements.majorImprovements}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Minor Improvements:</span>
                        <span className="font-medium text-gray-600">
                          {enhancedResult.step2_enhancedArticle.overallImprovements.minorImprovements}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Processing Time:</span>
                        <span className="font-medium">
                          {Math.round(enhancedResult.processingMetadata.totalProcessingTime / 1000)}s
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Quality Improvements</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Before Score:</span>
                        <span className="font-medium">{enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.beforeScore}/100</span>
                      </div>
                      <div className="flex justify-between">
                        <span>After Score:</span>
                        <span className={`font-medium ${getImprovementColor(
                          enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.beforeScore,
                          enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.afterScore
                        )}`}>
                          {enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.afterScore}/100
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Improvement:</span>
                        <span className="font-medium text-green-600">
                          +{enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.afterScore - 
                            enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.beforeScore} points
                        </span>
                      </div>
                    </div>
                    <div className="mt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Improvement Areas:</h4>
                      <div className="flex flex-wrap gap-2">
                        {enhancedResult.step3_documentation.changeDocumentation.qualityImprovements.improvementAreas.map((area, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Enhancement Overview</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Total Changes:</span>
                        <span className="font-medium">{enhancedResult.step3_documentation.changeDocumentation.overview.totalChanges}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sections Modified:</span>
                        <span className="font-medium">{enhancedResult.step3_documentation.changeDocumentation.overview.sectionsModified.length}</span>
                      </div>
                    </div>
                    <div className="mt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Modified Sections:</h4>
                      <div className="flex flex-wrap gap-2">
                        {enhancedResult.step3_documentation.changeDocumentation.overview.sectionsModified.map((section, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {formatSectionTitle(section as ArticleSection)}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Enhanced Sections Tab */}
          <TabsContent value="sections" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {enhancedResult.step2_enhancedArticle.sections.map((sectionData) => (
                <Card key={sectionData.section} className="border-gray-200 shadow-sm">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        {formatSectionTitle(sectionData.section)}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        {sectionData.hasChanges && (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Enhanced
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          Score: {sectionData.improvementScore}/100
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleOriginalContent(sectionData.section)}
                        >
                          {showOriginalContent[sectionData.section] ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Hide Original
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Show Original
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Original Content (if shown) */}
                    {showOriginalContent[sectionData.section] && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Original Content</h4>
                        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                            {sectionData.originalContent}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Content */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Enhanced Content</h4>
                      <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                        {renderEnhancedContent(sectionData.enhancedContent)}
                      </div>
                    </div>

                    {/* Changes Summary */}
                    {sectionData.hasChanges && sectionData.changes.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Changes Made ({sectionData.changes.length})</h4>
                        <div className="space-y-2">
                          {sectionData.changes.slice(0, 3).map((change, index) => (
                            <div key={index} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {change.type}
                                </Badge>
                                <span className="text-sm font-medium text-gray-900">
                                  {change.description}
                                </span>
                              </div>
                              <p className="text-xs text-gray-600">{change.reason}</p>
                            </div>
                          ))}
                          {sectionData.changes.length > 3 && (
                            <p className="text-sm text-gray-600">
                              +{sectionData.changes.length - 3} more changes...
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Change Documentation Tab */}
          <TabsContent value="changes" className="space-y-6">
            <Card className="border-gray-200 shadow-lg">
              <CardHeader className="bg-gray-50">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <TrendingUp className="h-6 w-6" />
                  Detailed Change Documentation
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  {/* Overview */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Change Overview</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {enhancedResult.step3_documentation.changeDocumentation.overview.totalChanges}
                        </div>
                        <div className="text-sm text-blue-700">Total Changes</div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {enhancedResult.step3_documentation.changeDocumentation.overview.sectionsModified.length}
                        </div>
                        <div className="text-sm text-green-700">Sections Modified</div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {enhancedResult.step3_documentation.changeDocumentation.overview.improvementAreas.length}
                        </div>
                        <div className="text-sm text-purple-700">Improvement Areas</div>
                      </div>
                    </div>
                  </div>

                  {/* Section-by-Section Changes */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Section-by-Section Changes</h3>
                    <div className="space-y-4">
                      {Object.entries(enhancedResult.step3_documentation.changeDocumentation.sectionBySection).map(([section, sectionData]) => (
                        <Card key={section} className="border-gray-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-base font-semibold text-gray-900">
                              {formatSectionTitle(section as ArticleSection)}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <p className="text-sm text-gray-700">{sectionData.overallImprovement}</p>

                            {sectionData.changes.length > 0 && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2">Changes Made:</h4>
                                <div className="space-y-2">
                                  {sectionData.changes.map((change, index) => (
                                    <div key={index} className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                                      <div className="flex items-center gap-2 mb-1">
                                        <Badge variant="outline" className={`text-xs ${
                                          change.impact === 'major' ? 'border-red-300 text-red-700' : 'border-blue-300 text-blue-700'
                                        }`}>
                                          {change.type} - {change.impact}
                                        </Badge>
                                      </div>
                                      <p className="text-sm font-medium text-gray-900 mb-1">{change.description}</p>
                                      <p className="text-xs text-gray-600 mb-2">{change.reason}</p>
                                      {change.beforeText && (
                                        <div className="text-xs">
                                          <span className="font-medium text-gray-700">Before: </span>
                                          <span className="text-gray-600">{change.beforeText.substring(0, 100)}...</span>
                                        </div>
                                      )}
                                      <div className="text-xs">
                                        <span className="font-medium text-gray-700">After: </span>
                                        <span className="text-gray-600">{change.afterText.substring(0, 100)}...</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {sectionData.additionalRecommendations.length > 0 && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2">Additional Recommendations:</h4>
                                <ul className="list-disc list-inside space-y-1">
                                  {sectionData.additionalRecommendations.map((rec, index) => (
                                    <li key={index} className="text-sm text-gray-700">{rec}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {/* Next Steps */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Recommended Next Steps</h3>
                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <ul className="space-y-2">
                        {enhancedResult.step3_documentation.changeDocumentation.nextSteps.map((step, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center text-xs font-medium text-yellow-800 mt-0.5">
                              {index + 1}
                            </div>
                            <span className="text-sm text-gray-700">{step}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* User Guidance Tab */}
          <TabsContent value="guidance" className="space-y-6">
            <Card className="border-gray-200 shadow-lg">
              <CardHeader className="bg-gray-50">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <Target className="h-6 w-6" />
                  User Guidance & Next Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: enhancedResult.step3_documentation.userGuidance
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                      .replace(/\n/g, '<br />')
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
