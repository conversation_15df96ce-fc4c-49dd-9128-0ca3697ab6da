/**
 * EndNote XML Parser Service
 * Parses EndNote XML (.xml) files and extracts citation data
 */

import { 
  ProcessedCitationData, 
  CitationFileFormat, 
  ReferenceManagerSource,
  CitationDocumentType,
  CitationImportServiceResponse 
} from '../../types/citation-import.types';

export class EndNoteParserService {
  /**
   * Parse EndNote XML file content
   */
  async parseEndNoteXML(content: string): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(content, 'text/xml');
      
      // Check for parsing errors
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        throw new Error('Invalid XML format');
      }
      
      const citations = this.extractCitations(xmlDoc);
      
      return {
        success: true,
        data: citations,
        error: null,
        warnings: this.generateWarnings(citations)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `EndNote XML parsing failed: ${error.message}`
      };
    }
  }

  /**
   * Extract citations from EndNote XML document
   */
  private extractCitations(xmlDoc: Document): ProcessedCitationData[] {
    const citations: ProcessedCitationData[] = [];
    
    // EndNote XML can have different root elements
    const records = xmlDoc.querySelectorAll('record, Record');
    
    records.forEach(record => {
      const citation = this.parseEndNoteRecord(record);
      if (citation) {
        citations.push(citation);
      }
    });
    
    return citations;
  }

  /**
   * Parse individual EndNote record
   */
  private parseEndNoteRecord(record: Element): ProcessedCitationData | null {
    try {
      return {
        title: this.getFieldValue(record, 'title, Title') || '',
        authors: this.parseAuthors(record),
        publicationYear: this.parseYear(record),
        journal: this.getFieldValue(record, 'secondary-title, journal, periodical'),
        volume: this.getFieldValue(record, 'volume'),
        issue: this.getFieldValue(record, 'number, issue'),
        pages: this.getFieldValue(record, 'pages'),
        doi: this.getFieldValue(record, 'electronic-resource-num, doi'),
        abstract: this.getFieldValue(record, 'abstract'),
        keywords: this.parseKeywords(record),
        documentType: this.mapEndNoteType(record),
        publisher: this.getFieldValue(record, 'publisher'),
        isbn: this.getFieldValue(record, 'isbn'),
        issn: this.getFieldValue(record, 'issn'),
        language: this.getFieldValue(record, 'language'),
        url: this.getFieldValue(record, 'url, web-urls url'),
        tags: [],
        notes: this.getFieldValue(record, 'notes, research-notes'),
        sourceFormat: 'endnote' as CitationFileFormat,
        sourceManager: 'endnote' as ReferenceManagerSource,
        processingConfidence: this.calculateConfidence(record),
        processingErrors: [],
        processingWarnings: []
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Get field value from EndNote record
   */
  private getFieldValue(record: Element, selectors: string): string | undefined {
    const selectorList = selectors.split(',').map(s => s.trim());
    
    for (const selector of selectorList) {
      const element = record.querySelector(selector);
      if (element && element.textContent) {
        return element.textContent.trim();
      }
    }
    
    return undefined;
  }

  /**
   * Parse authors from EndNote record
   */
  private parseAuthors(record: Element): string[] {
    const authors: string[] = [];
    
    // Primary authors
    const authorElements = record.querySelectorAll('contributors author, authors author, contributors primary-authors author');
    authorElements.forEach(author => {
      const authorText = author.textContent?.trim();
      if (authorText) {
        authors.push(this.formatAuthorName(authorText));
      }
    });
    
    // Secondary authors (editors, etc.)
    const secondaryElements = record.querySelectorAll('contributors secondary-authors author, contributors editors author');
    secondaryElements.forEach(author => {
      const authorText = author.textContent?.trim();
      if (authorText) {
        authors.push(this.formatAuthorName(authorText));
      }
    });
    
    return authors;
  }

  /**
   * Format author name
   */
  private formatAuthorName(name: string): string {
    // Handle "Last, First" format
    if (name.includes(',')) {
      const parts = name.split(',').map(p => p.trim());
      if (parts.length >= 2) {
        return `${parts[1]} ${parts[0]}`.trim();
      }
    }
    
    return name;
  }

  /**
   * Parse publication year
   */
  private parseYear(record: Element): number | undefined {
    const yearFields = ['year, dates year, pub-dates year, date'];
    
    for (const fieldSelector of yearFields) {
      const yearStr = this.getFieldValue(record, fieldSelector);
      if (yearStr) {
        const match = yearStr.match(/(\d{4})/);
        if (match) {
          return parseInt(match[1]);
        }
      }
    }
    
    return undefined;
  }

  /**
   * Parse keywords
   */
  private parseKeywords(record: Element): string[] {
    const keywords: string[] = [];
    
    const keywordElements = record.querySelectorAll('keywords keyword, keyword');
    keywordElements.forEach(keyword => {
      const keywordText = keyword.textContent?.trim();
      if (keywordText) {
        keywords.push(keywordText);
      }
    });
    
    // Also check for keywords in a single field separated by delimiters
    const keywordField = this.getFieldValue(record, 'keywords');
    if (keywordField) {
      const splitKeywords = keywordField
        .split(/[,;|\n]/)
        .map(k => k.trim())
        .filter(k => k.length > 0);
      keywords.push(...splitKeywords);
    }
    
    return [...new Set(keywords)]; // Remove duplicates
  }

  /**
   * Map EndNote reference type to document type
   */
  private mapEndNoteType(record: Element): CitationDocumentType {
    const refType = this.getFieldValue(record, 'ref-type');
    
    if (!refType) return 'other';
    
    const typeMap: Record<string, CitationDocumentType> = {
      '0': 'journal-article',    // Journal Article
      '1': 'book',               // Book
      '2': 'thesis',             // Thesis
      '3': 'conference-paper',   // Conference Proceedings
      '4': 'conference-paper',   // Conference Paper
      '5': 'book-chapter',       // Book Section
      '6': 'journal-article',    // Journal
      '7': 'journal-article',    // Magazine Article
      '8': 'journal-article',    // Newspaper Article
      '9': 'conference-paper',   // Conference Proceedings
      '10': 'report',            // Report
      '11': 'other',             // Personal Communication
      '12': 'webpage',           // Web Page
      '13': 'other',             // Electronic Source
      '17': 'other',             // Film or Broadcast
      '19': 'other',             // Manuscript
      '21': 'other',             // Equation
      '22': 'other',             // Figure
      '23': 'other',             // Chart or Table
      '27': 'other',             // Online Database
      '28': 'other',             // Online Multimedia
      '31': 'other',             // Classical Work
      '32': 'other',             // Government Document
      '43': 'other',             // Artwork
      '47': 'patent',            // Patent
      '51': 'other',             // Bill
      '52': 'other',             // Case
      '53': 'other',             // Hearing
      '54': 'other'              // Statute
    };
    
    return typeMap[refType] || 'other';
  }

  /**
   * Calculate processing confidence
   */
  private calculateConfidence(record: Element): number {
    let score = 0;
    const maxScore = 10;
    
    // Required fields
    if (this.getFieldValue(record, 'title, Title')) score += 3;
    if (record.querySelectorAll('contributors author, authors author').length > 0) score += 2;
    if (this.parseYear(record)) score += 2;
    
    // Optional but important fields
    if (this.getFieldValue(record, 'secondary-title, journal, periodical')) score += 1;
    if (this.getFieldValue(record, 'electronic-resource-num, doi')) score += 1;
    if (this.getFieldValue(record, 'abstract')) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Generate warnings for parsed citations
   */
  private generateWarnings(citations: ProcessedCitationData[]): string[] {
    const warnings: string[] = [];
    
    citations.forEach((citation, index) => {
      if (!citation.title) {
        warnings.push(`Citation ${index + 1}: Missing title`);
      }
      if (citation.authors.length === 0) {
        warnings.push(`Citation ${index + 1}: Missing authors`);
      }
      if (!citation.publicationYear) {
        warnings.push(`Citation ${index + 1}: Missing publication year`);
      }
    });
    
    return warnings;
  }
}

export const endNoteParserService = new EndNoteParserService();
