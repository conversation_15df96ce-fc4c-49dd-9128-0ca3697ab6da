/**
 * References Sidebar Component
 * Displays extracted citations and sources with links for Paper Generator V2
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  BookOpen,
  ExternalLink,
  Copy,
  ChevronRight,
  ChevronDown,
  Eye,
  EyeOff,
  Download,
  FileText,
  Link as LinkIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface Reference {
  id: string;
  title: string;
  authors?: string[] | string;
  year?: number;
  source: string;
  url: string;
  doi?: string;
  abstract?: string;
  isVerified?: boolean;
  confidence: number;
  sectionUsed?: string[];
}

interface Source {
  id: string;
  title: string;
  url: string;
  description: string;
  domain: string;
  publishedDate?: string;
  relevanceScore: number;
}

interface ReferencesSidebarProps {
  references: Reference[];
  sources?: Source[];
  isVisible: boolean;
  onToggleVisibility: () => void;
  onExportReferences: () => void;
  className?: string;
}

export const ReferencesSidebar: React.FC<ReferencesSidebarProps> = ({
  references,
  sources = [],
  isVisible,
  onToggleVisibility,
  onExportReferences,
  className = ''
}) => {
  const [expandedRefs, setExpandedRefs] = useState<Set<string>>(new Set());
  const [selectedSection, setSelectedSection] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'references' | 'sources'>('references');

  // Deduplicate references to fix duplicate keys issue
  const uniqueReferences = references.reduce((acc, ref, index) => {
    const existingIndex = acc.findIndex(existing => existing.id === ref.id);
    if (existingIndex === -1) {
      // Add unique index to ensure unique keys
      acc.push({ ...ref, uniqueKey: `${ref.id}_${index}` });
    }
    return acc;
  }, [] as (Reference & { uniqueKey: string })[]);

  // Toggle reference expansion
  const toggleReference = (refId: string) => {
    const newExpanded = new Set(expandedRefs);
    if (newExpanded.has(refId)) {
      newExpanded.delete(refId);
    } else {
      newExpanded.add(refId);
    }
    setExpandedRefs(newExpanded);
  };

  // Copy citation to clipboard
  const copyCitation = async (reference: Reference) => {
    const authors = Array.isArray(reference.authors) ? reference.authors.join(', ') : (reference.authors || 'Unknown Author');
    const year = reference.year || 'n.d.';
    const citation = `${authors} (${year}). ${reference.title}. ${reference.source}.`;
    try {
      await navigator.clipboard.writeText(citation);
      toast.success('Citation copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy citation');
    }
  };

  // Open source URL
  const openSource = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Filter references by section
  const filteredReferences = selectedSection === 'all'
    ? uniqueReferences
    : uniqueReferences.filter(ref => ref.sectionUsed?.includes(selectedSection));

  // Get unique sections
  const sections = ['all', ...new Set(uniqueReferences.flatMap(ref => ref.sectionUsed || []))];

  if (!isVisible) {
    return (
      <div className={`fixed right-4 top-1/2 transform -translate-y-1/2 z-50 ${className}`}>
        <Button
          onClick={onToggleVisibility}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-gray-200 hover:bg-gray-50"
        >
          <BookOpen className="h-4 w-4 mr-2" />
          Sources & References ({uniqueReferences.length + sources.length})
        </Button>
      </div>
    );
  }

  return (
    <div className={`w-96 bg-white border-l border-gray-200 flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Sources & References</h3>
            <Badge variant="secondary" className="text-xs">
              {activeTab === 'references' ? filteredReferences.length : sources.length}
            </Badge>
          </div>
          <Button
            onClick={onToggleVisibility}
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <EyeOff className="h-4 w-4" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex gap-1 mb-3 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('references')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'references'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            References ({uniqueReferences.length})
          </button>
          <button
            onClick={() => setActiveTab('sources')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'sources'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Sources ({sources.length})
          </button>
        </div>

        {/* Section Filter - Only for References */}
        {activeTab === 'references' && sections.length > 1 && (
          <div className="mb-3">
            <select
              value={selectedSection}
              onChange={(e) => setSelectedSection(e.target.value)}
              className="w-full text-sm border border-gray-200 rounded px-2 py-1"
            >
              {sections.map(section => (
                <option key={section} value={section}>
                  {section === 'all' ? 'All Sections' : section.charAt(0).toUpperCase() + section.slice(1)}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Export Button */}
        <Button
          onClick={onExportReferences}
          variant="outline"
          size="sm"
          className="w-full"
          disabled={activeTab === 'references' ? filteredReferences.length === 0 : sources.length === 0}
        >
          <Download className="h-4 w-4 mr-2" />
          Export {activeTab === 'references' ? 'References' : 'Sources'}
        </Button>
      </div>

      {/* Content Area */}
      <ScrollArea className="flex-1 p-4">
        {activeTab === 'references' ? (
          // References Tab
          filteredReferences.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No references found</p>
              <p className="text-xs mt-1">
                Generate content with Introduction or Methodology sections to see references
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredReferences.map((reference, index) => (
                <Card key={reference.uniqueKey} className="border border-gray-200 hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium text-gray-500">
                          [{index + 1}]
                        </span>
                        {reference.isVerified && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                            Verified
                          </Badge>
                        )}
                        {reference.confidence && (
                          <Badge variant="outline" className="text-xs">
                            {Math.round(reference.confidence * 100)}%
                          </Badge>
                        )}
                      </div>
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-2 leading-tight">
                        {reference.title}
                      </h4>
                      <p className="text-xs text-gray-600 mt-1">
                        {Array.isArray(reference.authors) ? reference.authors.join(', ') : (reference.authors || 'Unknown Author')} ({reference.year || 'n.d.'})
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {reference.source}
                      </p>
                    </div>
                    <Button
                      onClick={() => toggleReference(reference.id)}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2"
                    >
                      {expandedRefs.has(reference.id) ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </CardHeader>

                {expandedRefs.has(reference.id) && (
                  <CardContent className="pt-0">
                    {reference.abstract && (
                      <div className="mb-3">
                        <p className="text-xs font-medium text-gray-700 mb-1">Abstract:</p>
                        <p className="text-xs text-gray-600 leading-relaxed">
                          {reference.abstract.length > 200 
                            ? `${reference.abstract.substring(0, 200)}...` 
                            : reference.abstract}
                        </p>
                      </div>
                    )}

                    {reference.doi && (
                      <div className="mb-3">
                        <p className="text-xs font-medium text-gray-700 mb-1">DOI:</p>
                        <p className="text-xs text-blue-600 font-mono">{reference.doi}</p>
                      </div>
                    )}

                    {reference.sectionUsed && reference.sectionUsed.length > 0 && (
                      <div className="mb-3">
                        <p className="text-xs font-medium text-gray-700 mb-1">Used in:</p>
                        <div className="flex flex-wrap gap-1">
                          {reference.sectionUsed.map(section => (
                            <Badge key={section} variant="outline" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <Separator className="my-3" />

                    <div className="flex gap-2">
                      <Button
                        onClick={() => copyCitation(reference)}
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        onClick={() => openSource(reference.url)}
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Open
                      </Button>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
          )
        ) : (
          // Sources Tab
          sources.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <LinkIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No sources found</p>
              <p className="text-xs mt-1">
                Generate content with Tavily search to see original sources
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {sources.map((source, index) => (
                <Card key={`source-${source.id}-${index}`} className="border border-gray-200 hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-xs font-medium text-gray-500">
                            [{index + 1}]
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {Math.round(source.relevanceScore * 100)}% relevant
                          </Badge>
                        </div>
                        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 leading-tight mb-2">
                          {source.title}
                        </h4>
                        <p className="text-xs text-gray-600 mb-2 line-clamp-3">
                          {source.description}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span className="font-medium">{source.domain}</span>
                          {source.publishedDate && (
                            <>
                              <span>•</span>
                              <span>{source.publishedDate}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button
                        onClick={() => window.open(source.url, '_blank', 'noopener,noreferrer')}
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Open Source
                      </Button>
                      <Button
                        onClick={() => navigator.clipboard.writeText(source.url)}
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy Link
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )
        )}
      </ScrollArea>

      {/* Footer */}
      {((activeTab === 'references' && filteredReferences.length > 0) || (activeTab === 'sources' && sources.length > 0)) && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            {activeTab === 'references'
              ? `${filteredReferences.length} reference${filteredReferences.length !== 1 ? 's' : ''} from verified academic sources`
              : `${sources.length} source${sources.length !== 1 ? 's' : ''} from online search`
            }
          </div>
        </div>
      )}
    </div>
  );
};
