/**
 * Paper Generator V2 - Main Component
 * Advanced AI-powered academic paper generator with flexible section-based writing
 */

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  FileText,
  Settings,
  Download,
  Save,
  RefreshCw,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  EyeOff,
  Sparkles,
  Upload,
  ArrowLeft
} from 'lucide-react';

import { 
  Paper, 
  PaperSection, 
  PaperMetadata, 
  UIState, 
  ProgressMetrics 
} from '../types';
import { SECTION_TYPES, AI_MODELS } from '../constants';
import { paperAIServiceV2 } from '../services/paper-ai-v2.service';
import { sectionContextService } from '../services/section-context.service';
import { enhancedPersistenceService } from '../services/enhanced-persistence.service';

// Component imports (to be created)
import { SectionSelector } from './SectionSelector';
import { SectionEditor } from './SectionEditor';
import { EnhancedSectionEditor } from './EnhancedSectionEditor';
import { AIModelSelector } from './AIModelSelector';
import { ProgressTracker } from './ProgressTracker';
import { PaperMetadataForm } from './PaperMetadataForm';
import { ReviewPanel } from './ReviewPanel';
import { SimpleExportButtons } from './SimpleExportButtons';
import { ImportEnhanceDialog } from './ImportEnhanceDialog';
import { StreamlinedPaperSetup } from './StreamlinedPaperSetup';
import { StreamlinedSectionWriter } from './StreamlinedSectionWriter';
import { ReferencesSidebar } from './ReferencesSidebar';
// Database setup verification (for development)
import '../database/verify-setup';

interface PaperGeneratorV2Props {
  initialPaper?: Partial<Paper>;
  onSave?: (paper: Paper) => void;
}

// Initialize paper structure function (moved outside component to avoid hoisting issues)
function initializePaper(initial?: Partial<Paper>): Paper {
  const defaultMetadata: PaperMetadata = {
    title: '',
    authors: [],
    researchField: '',
    keywords: [],
    status: 'draft',
    ...initial?.metadata
  };

  const defaultSections: PaperSection[] = SECTION_TYPES.map(sectionType => ({
    id: `section_${sectionType.id}_${Date.now()}`,
    type: sectionType.id,
    title: sectionType.name,
    content: '',
    subsections: [],
    contentBlocks: [], // Initialize empty content blocks array
    status: 'empty',
    aiGenerated: false,
    lastModified: new Date(),
    wordCount: 0,
    citations: [],
    figures: [],
    structure: {
      useHierarchicalContent: false, // Start with legacy mode for backward compatibility
      headingCount: 0,
      paragraphCount: 0,
      figureCount: 0
    }
  }));

  const defaultProgress: ProgressMetrics = {
    totalSections: SECTION_TYPES.length,
    completedSections: 0,
    totalWords: 0,
    sectionsProgress: {},
    estimatedTimeRemaining: 0,
    lastActivity: new Date()
  };

  return {
    metadata: defaultMetadata,
    sections: initial?.sections || defaultSections,
    progress: defaultProgress,
    exportOptions: {
      format: 'pdf',
      includeMetadata: true,
      includeCitations: true,
      citationStyle: 'apa',
      template: 'standard'
    },
    researchOptions: {
      enableTavilySearch: true,
      maxSearchResults: 10,
      prioritizeAcademicSources: true,
      includeCitations: true,
      citationStyle: 'apa',
      enableResearchLibraryContext: true,
      maxLibraryReferences: 5
    },
    ...initial
  };
}

export const PaperGeneratorV2: React.FC<PaperGeneratorV2Props> = ({
  initialPaper,
  onSave
}) => {
  // Main state
  const [paper, setPaper] = useState<Paper>(() => initializePaper(initialPaper));
  const [uiState, setUIState] = useState<UIState>({
    selectedSection: null,
    selectedModel: AI_MODELS[0].id,
    isGenerating: false,
    showReview: false,
    showExport: false,
    sidebarCollapsed: false,
    activeTab: 'write',
    mode: 'step-by-step',
    hideQuickSetup: false,
    hideProgress: false,
    showImportDialog: false
  });



  // Additional UI state for enhanced features
  const [hideHeader, setHideHeader] = useState(false);
  const [hideSectionNav, setHideSectionNav] = useState(false);
  // Always use enhanced editor - unified approach
  const useEnhancedEditor = true;

  // Streamlined workflow state - only show setup for completely new papers
  const [showSetup, setShowSetup] = useState(() => {
    // Show setup only if this is a completely new paper with no content
    const hasContent = initialPaper?.sections?.some(s => s.content && s.content.trim().length > 0);
    const hasTitle = initialPaper?.metadata?.title && initialPaper.metadata.title.trim().length > 0;
    return !hasContent && !hasTitle;
  });
  const [researchContext, setResearchContext] = useState({
    topic: '',
    question: '',
    methodology: '',
    field: '',
    keywords: [] as string[]
  });

  // References and Sources state
  const [references, setReferences] = useState<any[]>([]);
  const [sources, setSources] = useState<any[]>([]);
  const [showReferences, setShowReferences] = useState(false);


  // Update progress metrics
  useEffect(() => {
    const completedSections = paper.sections.filter(s =>
      s.status === 'completed' || s.status === 'reviewed'
    ).length;

    const totalWords = paper.sections.reduce((sum, section) =>
      sum + section.wordCount, 0
    );

    const sectionsProgress = paper.sections.reduce((acc, section) => {
      acc[section.type] = sectionContextService.calculateSectionScore(section);
      return acc;
    }, {} as Record<string, number>);

    setPaper(prev => ({
      ...prev,
      progress: {
        ...prev.progress,
        completedSections,
        totalWords,
        sectionsProgress,
        lastActivity: new Date()
      }
    }));
  }, [paper.sections]);

  // Auto-save functionality
  useEffect(() => {
    // Start auto-save when paper has content
    if (paper.metadata.title && paper.sections.some(s => s.content.trim().length > 0)) {
      const paperId = paper.metadata.id || `temp_${Date.now()}`;
      enhancedPersistenceService.startAutoSave(paperId, paper, 30000); // 30 seconds

      console.log('🔄 Auto-save: Started for paper with content');
    }

    // Cleanup on unmount
    return () => {
      enhancedPersistenceService.stopAutoSave();
    };
  }, [paper.metadata.title, paper.sections.length]);

  // Track section changes for auto-save
  useEffect(() => {
    paper.sections.forEach(section => {
      if (section.content.trim().length > 0) {
        enhancedPersistenceService.markSectionChanged(section.id, section);
      }
    });
  }, [paper.sections]);

  // Handle setup completion
  const handleSetupComplete = (setupData: any) => {
    setResearchContext({
      topic: setupData.researchTopic,
      question: setupData.researchQuestion,
      methodology: setupData.methodology,
      field: setupData.researchField,
      keywords: setupData.keywords
    });

    // Update paper metadata
    setPaper(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        title: setupData.researchTopic.substring(0, 100) + (setupData.researchTopic.length > 100 ? '...' : ''),
        researchField: setupData.researchField,
        keywords: setupData.keywords,
        authors: setupData.authors || []
      }
    }));

    setShowSetup(false);
    toast.success('Research setup completed! You can now start writing your paper.');
  };

  // Handle content generation
  const handleGenerateContent = async (prompt: string, sectionType: string): Promise<string> => {
    try {
      setUIState(prev => ({ ...prev, isGenerating: true }));

      const response = await paperAIServiceV2.generateContent({
        prompt,
        sectionType,
        context: {
          metadata: paper.metadata,
          existingSections: paper.sections.reduce((acc, section) => {
            acc[section.type] = section.content;
            return acc;
          }, {} as Record<string, string>),
          researchContext
        },
        options: {
          model: uiState.selectedModel,
          maxTokens: 2048,
          temperature: 0.7
        }
      });

      return response;
    } catch (error) {
      console.error('Content generation error:', error);
      throw error;
    } finally {
      setUIState(prev => ({ ...prev, isGenerating: false }));
    }
  };

  // Handle complete section generation
  const handleGenerateCompleteSection = async (): Promise<void> => {
    if (!selectedSection) {
      toast.error('No section selected');
      return;
    }

    try {
      setUIState(prev => ({ ...prev, isGenerating: true }));

      const response = await paperAIServiceV2.generateCompleteSection({
        sectionType: selectedSection.type,
        context: {
          metadata: paper.metadata,
          existingSections: paper.sections.reduce((acc, section) => {
            // Only include sections with actual content (not empty)
            if (section.content && section.content.trim().length > 0) {
              acc[section.type] = section.content;
            }
            return acc;
          }, {} as Record<string, string>),
          researchContext
        },
        options: {
          model: uiState.selectedModel,
          maxTokens: 3072, // Larger for complete sections
          temperature: 0.7
        }
      });

      // Update the section with the generated content
      handleSectionUpdate(selectedSection.id, {
        content: response,
        wordCount: response.split(/\s+/).filter(word => word.length > 0).length,
        lastModified: new Date(),
        status: 'draft',
        aiGenerated: true
      });

      toast.success(`Complete ${selectedSection.title} section generated successfully!`);
    } catch (error) {
      console.error('Complete section generation error:', error);
      toast.error('Failed to generate complete section. Please try again.');
    } finally {
      setUIState(prev => ({ ...prev, isGenerating: false }));
    }
  };

  // Handle sending content directly to editor (no AI generation)
  const handleSendToEditor = (content: string) => {
    if (!content.trim()) {
      toast.error('No content to send to editor');
      return;
    }

    if (!selectedSection) {
      toast.error('No section selected to send content to');
      return;
    }

    // Get current section content
    const currentContent = selectedSection.content?.trim() || '';

    // Append to existing content or replace if empty
    const newContent = currentContent
      ? currentContent + '\n\n' + content
      : content;

    // Update the section with the new content
    handleSectionUpdate(selectedSection.id, {
      content: newContent,
      wordCount: newContent.split(/\s+/).filter(word => word.length > 0).length,
      lastModified: new Date(),
      status: 'draft'
    });

    toast.success('Content sent to editor successfully!');
  };

  // Handle section selection
  const handleSectionSelect = (sectionId: string) => {
    setUIState(prev => ({
      ...prev,
      selectedSection: sectionId
    }));
  };

  // Handle section update with enhanced persistence
  const handleSectionUpdate = async (sectionId: string, updates: Partial<PaperSection>) => {
    // Update local state first
    setPaper(prev => ({
      ...prev,
      sections: prev.sections.map(section =>
        section.id === sectionId
          ? {
              ...section,
              ...updates,
              lastModified: new Date(),
              wordCount: updates.content ? updates.content.split(/\s+/).filter(w => w.length > 0).length : section.wordCount
            }
          : section
      )
    }));

    // Save to enhanced persistence if content changed
    if (updates.content !== undefined) {
      const section = paper.sections.find(s => s.id === sectionId);
      if (section) {
        const updatedSection = { ...section, ...updates };
        const paperId = paper.metadata.id || `temp_${Date.now()}`;

        // Determine change type
        const changeType = updates.aiGenerated ? 'ai_generated' : 'edited';

        // Save with history tracking
        await enhancedPersistenceService.saveSectionWithHistory(
          paperId,
          updatedSection,
          changeType,
          `Section "${section.title}" updated`,
          uiState.selectedModel
        );

        // Mark for auto-save
        enhancedPersistenceService.markSectionChanged(sectionId, updatedSection);

        // Trigger auto-save for section update
        await enhancedPersistenceService.autoSavePaper(paperId, paper, 'section_update', `Updated section: ${section.title}`);
      }
    }
  };

  // Handle metadata update
  const handleMetadataUpdate = (metadata: Partial<PaperMetadata>) => {
    setPaper(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        ...metadata
      }
    }));
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    setUIState(prev => ({
      ...prev,
      selectedModel: modelId
    }));
  };

  // Handle tab change
  const handleTabChange = (tab: 'write' | 'review' | 'export') => {
    setUIState(prev => ({
      ...prev,
      activeTab: tab,
      showReview: tab === 'review',
      showExport: tab === 'export'
    }));
  };

  // Handle save
  const handleSave = async () => {
    try {
      if (onSave) {
        await onSave(paper);
        toast.success('Paper saved successfully');
      }
    } catch (error) {
      console.error('Save failed:', error);
      toast.error('Failed to save paper');
    }
  };



  // Handle import and enhance
  const handleImport = async (content: string, targetSections: string[]) => {
    try {
      // Parse the imported content and distribute to sections
      // This is a simplified implementation - in practice, you'd use AI to analyze and distribute content
      const contentLines = content.split('\n\n');

      targetSections.forEach((sectionType, index) => {
        const sectionContent = contentLines[index] || '';
        const section = paper.sections.find(s => s.type === sectionType);

        if (section) {
          handleSectionUpdate(section.id, {
            content: sectionContent,
            status: sectionContent.trim() ? 'draft' : 'empty',
            lastModified: new Date()
          });
        }
      });

      toast.success('Content imported successfully');
    } catch (error) {
      console.error('Import failed:', error);
      toast.error('Failed to import content');
    }
  };

  // Handle mode change
  const handleModeChange = (mode: 'step-by-step' | 'import-enhance') => {
    setUIState(prev => ({ ...prev, mode }));
    if (mode === 'import-enhance') {
      setUIState(prev => ({ ...prev, showImportDialog: true }));
    }
  };

  // Handle toggle visibility
  const handleToggleVisibility = (section: 'quickSetup' | 'progress') => {
    if (section === 'quickSetup') {
      setUIState(prev => ({ ...prev, hideQuickSetup: !prev.hideQuickSetup }));
    } else {
      setUIState(prev => ({ ...prev, hideProgress: !prev.hideProgress }));
    }
  };

  // Get selected section
  const selectedSection = paper.sections.find(s => s.id === uiState.selectedSection);

  // Show setup if not completed
  if (showSetup) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StreamlinedPaperSetup
          onSetupComplete={handleSetupComplete}
          onSkip={() => setShowSetup(false)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Collapsible */}
      {!hideHeader && (
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={() => setShowSetup(true)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Setup
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <FileText className="h-6 w-6 text-blue-600" />
                    AI Research Paper Generator
                  </h1>
                  <p className="text-gray-600 mt-1">
                    {researchContext.topic ? researchContext.topic.substring(0, 80) + '...' : 'Advanced section-based writing with context-aware AI assistance'}
                  </p>
                </div>
              </div>
            
            <div className="flex items-center gap-3">
              {/* Mode Selector */}
              <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                <Button
                  variant={uiState.mode === 'step-by-step' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleModeChange('step-by-step')}
                  className="text-xs"
                >
                  Step-by-Step
                </Button>
                <Button
                  variant={uiState.mode === 'import-enhance' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleModeChange('import-enhance')}
                  className="text-xs flex items-center gap-1"
                >
                  <Upload className="h-3 w-3" />
                  Import & Enhance
                </Button>
              </div>

              {/* Enhanced Mode Badge */}
              <Badge variant="default" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                Enhanced Mode
              </Badge>

              <AIModelSelector
                selectedModel={uiState.selectedModel}
                onModelSelect={handleModelSelect}
                disabled={uiState.isGenerating}
              />

              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleTabChange('export')}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <ProgressTracker paper={paper} />
          </div>
        </div>
      </div>
      )}

      {/* Hide/Show Header Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setHideHeader(!hideHeader)}
          className="bg-white shadow-lg"
          title={hideHeader ? 'Show header' : 'Hide header for more space'}
        >
          {hideHeader ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
        </Button>
      </div>

      {/* Main Content - Clean Two Panel Layout */}
      <div className="max-w-7xl mx-auto p-4">
        {/* Main Content Area */}
        <div className="w-full">
        {/* Quick Paper Setup */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                Quick Setup
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleToggleVisibility('quickSetup')}
                className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
              >
                {uiState.hideQuickSetup ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                {uiState.hideQuickSetup ? 'Show' : 'Hide'}
              </Button>
            </CardTitle>
          </CardHeader>
          {!uiState.hideQuickSetup && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Paper Title</label>
                <input
                  type="text"
                  value={paper.metadata.title}
                  onChange={(e) => handleMetadataUpdate({ title: e.target.value })}
                  placeholder="Enter your paper title..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Research Field</label>
                <input
                  type="text"
                  value={paper.metadata.researchField}
                  onChange={(e) => handleMetadataUpdate({ researchField: e.target.value })}
                  placeholder="e.g., Computer Science"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <AIModelSelector
                  selectedModel={uiState.selectedModel}
                  onModelSelect={handleModelSelect}
                  disabled={uiState.isGenerating}
                />
              </div>
            </div>
          </CardContent>
          )}
        </Card>

        {/* Section Navigation Tabs - Horizontal - Collapsible */}
        {!hideSectionNav && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-xl flex items-center justify-between">
                <span>Paper Sections</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setHideSectionNav(true)}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                  title="Hide section navigation for more space"
                >
                  <EyeOff className="h-4 w-4" />
                  Hide
                </Button>
              </CardTitle>
              <p className="text-gray-600 text-sm">Click on any section to start writing. Complete sections are marked with checkmarks.</p>
            </CardHeader>
            <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {SECTION_TYPES.map(sectionType => {
                const section = paper.sections.find(s => s.type === sectionType.id);
                if (!section) return null;

                const isSelected = section.id === uiState.selectedSection;
                const score = sectionContextService.calculateSectionScore(section);
                const IconComponent = sectionType.icon;
                const isCompleted = score >= 0.8;
                const hasContent = section.content.trim().length > 0;

                return (
                  <button
                    key={section.id}
                    onClick={() => handleSectionSelect(section.id)}
                    className={`
                      relative p-4 rounded-lg border-2 transition-all duration-200 text-left
                      ${isSelected
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : hasContent
                          ? 'border-green-200 bg-green-50 hover:border-green-300'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                      }
                    `}
                  >
                    {/* Status Indicator */}
                    <div className="absolute top-2 right-2">
                      {isCompleted ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : hasContent ? (
                        <Clock className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-gray-400" />
                      )}
                    </div>

                    {/* Section Icon */}
                    <div className={`
                      w-8 h-8 rounded-lg flex items-center justify-center mb-2
                      ${isSelected ? 'bg-blue-500' : sectionType.color}
                    `}>
                      <IconComponent className="h-4 w-4 text-white" />
                    </div>

                    {/* Section Info */}
                    <h3 className={`
                      font-medium text-sm mb-1
                      ${isSelected ? 'text-blue-900' : 'text-gray-900'}
                    `}>
                      {sectionType.name}
                    </h3>

                    {section.wordCount > 0 && (
                      <p className="text-xs text-gray-500">
                        {section.wordCount} words
                      </p>
                    )}

                    {/* Progress Bar */}
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div
                          className={`h-1 rounded-full transition-all duration-300 ${
                            isCompleted ? 'bg-green-500' : hasContent ? 'bg-yellow-500' : 'bg-gray-300'
                          }`}
                          style={{ width: `${score * 100}%` }}
                        />
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </CardContent>
        </Card>
        )}

        {/* Show Section Navigation Toggle */}
        {hideSectionNav && (
          <div className="mb-6 text-center">
            <Button
              variant="outline"
              onClick={() => setHideSectionNav(false)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Show Section Navigation
            </Button>
          </div>
        )}

        {/* Main Editor Area */}
        {selectedSection ? (
          <StreamlinedSectionWriter
            sectionType={selectedSection.type}
            sectionTitle={selectedSection.title}
            currentContent={selectedSection.content}
            researchContext={researchContext}
            onContentChange={(content) => {
              handleSectionUpdate(selectedSection.id, {
                content,
                wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
                lastModified: new Date(),
                status: content.trim().length > 0 ? 'draft' : 'empty'
              });
            }}
            onGenerateContent={handleGenerateContent}
            onGenerateCompleteSection={handleGenerateCompleteSection}
            onSendToEditor={handleSendToEditor}
            availableContext={paper.sections
              .filter(section => section.content && section.content.trim().length > 0)
              .map(section => section.title)
            }
            selectedModel={uiState.selectedModel}
            onReferencesUpdate={(newReferences) => {
              setReferences(prev => {
                // Merge new references with existing ones, avoiding duplicates
                const existingIds = new Set(prev.map(ref => ref.id));
                const uniqueNewRefs = newReferences.filter(ref => !existingIds.has(ref.id));
                const updatedRefs = [...prev, ...uniqueNewRefs];

                // Show references sidebar if we have new references
                if (uniqueNewRefs.length > 0) {
                  setShowReferences(true);
                }

                return updatedRefs;
              });
            }}
            onSourcesUpdate={(newSources) => {
              setSources(prev => {
                // Merge new sources with existing ones, avoiding duplicates
                const existingIds = new Set(prev.map(source => source.id));
                const uniqueNewSources = newSources.filter(source => !existingIds.has(source.id));
                const updatedSources = [...prev, ...uniqueNewSources];

                // Show references sidebar if we have new sources
                if (uniqueNewSources.length > 0) {
                  setShowReferences(true);
                }

                return updatedSources;
              });
            }}
          />
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Select a Section to Start Writing
              </h3>
              <p className="text-gray-600 mb-6">
                Choose any section above to begin writing your academic paper. You can work on sections in any order.
              </p>
              <div className="flex justify-center gap-4">
                <Button
                  onClick={() => {
                    const firstEmptySection = paper.sections.find(s => s.content.trim().length === 0);
                    if (firstEmptySection) handleSectionSelect(firstEmptySection.id);
                  }}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Start with First Empty Section
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleTabChange('review')}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Review Progress
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions Footer */}
        <div className="mt-6 space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => handleTabChange('review')}
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Review Paper
              </Button>
              <Button
                variant="outline"
                onClick={() => handleTabChange('export')}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Options
              </Button>
            </div>

            <div className="text-sm text-gray-600">
              {paper.sections.filter(s => sectionContextService.calculateSectionScore(s) >= 0.8).length} of {paper.sections.length} sections completed
            </div>
          </div>

          {/* Quick Export Buttons */}
          <div className="border-t pt-4">
            <SimpleExportButtons paper={paper} />
          </div>
        </div>

        {/* Review and Export Modals */}
        {uiState.activeTab === 'review' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
              <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-xl font-semibold">Paper Review</h2>
                <Button
                  variant="ghost"
                  onClick={() => handleTabChange('write')}
                  className="p-2"
                >
                  ✕
                </Button>
              </div>
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <ReviewPanel
                  paper={paper}
                  selectedModel={uiState.selectedModel}
                  onSectionUpdate={handleSectionUpdate}
                />
              </div>
            </div>
          </div>
        )}

        {uiState.activeTab === 'export' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
              <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-xl font-semibold">Export Paper</h2>
                <Button
                  variant="ghost"
                  onClick={() => handleTabChange('write')}
                  className="p-2"
                >
                  ✕
                </Button>
              </div>
              <div className="p-6">
                <SimpleExportButtons paper={paper} />
              </div>
            </div>
          </div>
        )}

        {/* Import & Enhance Dialog */}
        <ImportEnhanceDialog
          isOpen={uiState.showImportDialog}
          onClose={() => setUIState(prev => ({ ...prev, showImportDialog: false }))}
          onImport={handleImport}
          existingSections={paper.sections}
        />
        </div> {/* End Main Content Area */}

        {/* References Sidebar */}
        <ReferencesSidebar
          references={references}
          sources={sources}
          isVisible={showReferences}
          onToggleVisibility={() => setShowReferences(!showReferences)}
          onExportReferences={() => {
            // TODO: Implement reference export
            toast.info('Reference export feature coming soon');
          }}
        />
      </div> {/* End Three Panel Layout */}
    </div>
  );
};
