/**
 * Format Detector Service
 * Automatically detects citation file formats and reference manager sources
 */

import { 
  CitationFileFormat, 
  ReferenceManagerSource, 
  ImportFileInfo,
  FileValidationResult,
  CitationImportServiceResponse 
} from '../../types/citation-import.types';

export class FormatDetectorService {
  /**
   * Detect file format and validate content
   */
  async detectFormat(fileInfo: ImportFileInfo): Promise<CitationImportServiceResponse<FileValidationResult>> {
    try {
      const content = typeof fileInfo.content === 'string' 
        ? fileInfo.content 
        : new TextDecoder().decode(fileInfo.content);
      
      const format = this.detectFileFormat(fileInfo.name, content);
      const source = this.detectReferenceManager(content, format);
      const estimatedCitations = this.estimateCitationCount(content, format);
      const validation = this.validateContent(content, format);
      
      const result: FileValidationResult = {
        isValid: validation.isValid,
        format,
        source,
        estimatedCitations,
        errors: validation.errors,
        warnings: validation.warnings,
        supportedFeatures: this.getSupportedFeatures(format)
      };
      
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `Format detection failed: ${error.message}`
      };
    }
  }

  /**
   * Detect file format based on extension and content
   */
  private detectFileFormat(filename: string, content: string): CitationFileFormat {
    const extension = filename.toLowerCase().split('.').pop() || '';
    
    // First check by file extension
    switch (extension) {
      case 'bib':
        return this.validateBibTeX(content) ? 'bibtex' : 'unknown';
      case 'ris':
        return this.validateRIS(content) ? 'ris' : 'unknown';
      case 'json':
        return this.validateJSON(content) ? 'json' : 'unknown';
      case 'xml':
        return this.validateEndNoteXML(content) ? 'endnote' : 'unknown';
      case 'txt':
        return this.validateRefWorks(content) ? 'refworks' : 'unknown';
      case 'csv':
        return this.validateCSV(content) ? 'csv' : 'unknown';
    }
    
    // If extension doesn't match, try content-based detection
    if (this.validateBibTeX(content)) return 'bibtex';
    if (this.validateRIS(content)) return 'ris';
    if (this.validateJSON(content)) return 'json';
    if (this.validateEndNoteXML(content)) return 'endnote';
    if (this.validateRefWorks(content)) return 'refworks';
    if (this.validateCSV(content)) return 'csv';
    
    return 'unknown';
  }

  /**
   * Validate BibTeX format
   */
  private validateBibTeX(content: string): boolean {
    // Check for BibTeX entry patterns
    const bibTexPattern = /@\w+\s*\{[^,\s]+\s*,/;
    return bibTexPattern.test(content);
  }

  /**
   * Validate RIS format
   */
  private validateRIS(content: string): boolean {
    // Check for RIS tag patterns
    const risPattern = /^[A-Z][A-Z0-9]\s*-\s*.+$/m;
    const hasTypeTag = /^TY\s*-\s*.+$/m.test(content);
    const hasEndTag = /^ER\s*-\s*$/m.test(content);
    
    return risPattern.test(content) && hasTypeTag && hasEndTag;
  }

  /**
   * Validate JSON format
   */
  private validateJSON(content: string): boolean {
    try {
      const data = JSON.parse(content);
      
      // Check if it's an array of objects or has citation-like structure
      if (Array.isArray(data)) {
        return data.length > 0 && this.hasJSONCitationStructure(data[0]);
      } else if (typeof data === 'object') {
        // Check for Mendeley-style structure
        return data.documents || this.hasJSONCitationStructure(data);
      }
      
      return false;
    } catch {
      return false;
    }
  }

  /**
   * Check if JSON object has citation-like structure
   */
  private hasJSONCitationStructure(obj: any): boolean {
    if (!obj || typeof obj !== 'object') return false;
    
    // Check for common citation fields
    const hasTitle = obj.title || obj.Title;
    const hasAuthors = obj.author || obj.authors || obj.Authors;
    const hasYear = obj.year || obj.issued || obj.date;
    
    return !!(hasTitle && (hasAuthors || hasYear));
  }

  /**
   * Validate EndNote XML format
   */
  private validateEndNoteXML(content: string): boolean {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(content, 'text/xml');
      
      // Check for parsing errors
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) return false;
      
      // Check for EndNote-specific elements
      const hasRecords = xmlDoc.querySelectorAll('record, Record').length > 0;
      const hasEndNoteStructure = xmlDoc.querySelector('xml, records, library') !== null;
      
      return hasRecords || hasEndNoteStructure;
    } catch {
      return false;
    }
  }

  /**
   * Validate RefWorks format
   */
  private validateRefWorks(content: string): boolean {
    // RefWorks tagged format has specific patterns
    const refWorksPattern = /^RT\s+.+$/m;
    const hasAuthorTag = /^A1\s+.+$/m.test(content);
    const hasTitleTag = /^T1\s+.+$/m.test(content);
    
    return refWorksPattern.test(content) && (hasAuthorTag || hasTitleTag);
  }

  /**
   * Validate CSV format
   */
  private validateCSV(content: string): boolean {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return false;
    
    const headers = lines[0].toLowerCase();
    const hasCitationHeaders = ['title', 'author', 'year', 'journal', 'doi']
      .some(field => headers.includes(field));
    
    return hasCitationHeaders;
  }

  /**
   * Detect reference manager source
   */
  private detectReferenceManager(content: string, format: CitationFileFormat): ReferenceManagerSource {
    const contentLower = content.toLowerCase();
    
    // Check for explicit mentions
    if (contentLower.includes('mendeley')) return 'mendeley';
    if (contentLower.includes('zotero')) return 'zotero';
    if (contentLower.includes('endnote')) return 'endnote';
    if (contentLower.includes('refworks')) return 'refworks';
    if (contentLower.includes('papers')) return 'papers';
    
    // Format-specific detection
    switch (format) {
      case 'json':
        return this.detectJSONSource(content);
      case 'bibtex':
        return this.detectBibTeXSource(content);
      case 'ris':
        return this.detectRISSource(content);
      case 'endnote':
        return 'endnote';
      case 'refworks':
        return 'refworks';
      default:
        return 'generic';
    }
  }

  /**
   * Detect JSON source
   */
  private detectJSONSource(content: string): ReferenceManagerSource {
    try {
      const data = JSON.parse(content);
      
      if (Array.isArray(data) && data.length > 0) {
        const firstItem = data[0];
        
        // Zotero CSL JSON structure
        if (firstItem.type && firstItem.title && firstItem.author) {
          return 'zotero';
        }
        
        // Mendeley structure
        if (firstItem.title && firstItem.authors && firstItem.year) {
          return 'mendeley';
        }
      } else if (data.documents) {
        return 'mendeley';
      }
    } catch {
      // Ignore parsing errors
    }
    
    return 'generic';
  }

  /**
   * Detect BibTeX source
   */
  private detectBibTeXSource(content: string): ReferenceManagerSource {
    if (content.includes('mendeley-groups') || content.includes('mendeley-tags')) {
      return 'mendeley';
    }
    if (content.includes('zotero-key') || content.includes('file = {.*Zotero')) {
      return 'zotero';
    }
    if (content.includes('endnote')) {
      return 'endnote';
    }
    
    return 'generic';
  }

  /**
   * Detect RIS source
   */
  private detectRISSource(content: string): ReferenceManagerSource {
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.startsWith('DB  -')) {
        const db = line.substring(5).trim().toLowerCase();
        if (db.includes('mendeley')) return 'mendeley';
        if (db.includes('zotero')) return 'zotero';
        if (db.includes('endnote')) return 'endnote';
        if (db.includes('refworks')) return 'refworks';
      }
    }
    
    return 'generic';
  }

  /**
   * Estimate citation count
   */
  private estimateCitationCount(content: string, format: CitationFileFormat): number {
    switch (format) {
      case 'bibtex':
        return (content.match(/@\w+\s*\{/g) || []).length;
      case 'ris':
        return (content.match(/^TY\s*-/gm) || []).length;
      case 'json':
        try {
          const data = JSON.parse(content);
          if (Array.isArray(data)) return data.length;
          if (data.documents && Array.isArray(data.documents)) return data.documents.length;
          return 1;
        } catch {
          return 0;
        }
      case 'endnote':
        try {
          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(content, 'text/xml');
          return xmlDoc.querySelectorAll('record, Record').length;
        } catch {
          return 0;
        }
      case 'refworks':
        return (content.match(/^RT\s+/gm) || []).length;
      case 'csv':
        const lines = content.split('\n').filter(line => line.trim());
        return Math.max(0, lines.length - 1); // Subtract header row
      default:
        return 0;
    }
  }

  /**
   * Validate content for specific format
   */
  private validateContent(content: string, format: CitationFileFormat): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!content.trim()) {
      errors.push('File is empty');
      return { isValid: false, errors, warnings };
    }
    
    switch (format) {
      case 'bibtex':
        return this.validateBibTeXContent(content);
      case 'ris':
        return this.validateRISContent(content);
      case 'json':
        return this.validateJSONContent(content);
      case 'endnote':
        return this.validateEndNoteContent(content);
      case 'csv':
        return this.validateCSVContent(content);
      case 'unknown':
        errors.push('Unknown or unsupported file format');
        return { isValid: false, errors, warnings };
      default:
        return { isValid: true, errors, warnings };
    }
  }

  /**
   * Validate BibTeX content
   */
  private validateBibTeXContent(content: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const entries = content.match(/@\w+\s*\{[^}]*\}/g) || [];
    if (entries.length === 0) {
      errors.push('No valid BibTeX entries found');
    }
    
    // Check for common issues
    const unclosedBraces = (content.match(/\{/g) || []).length - (content.match(/\}/g) || []).length;
    if (unclosedBraces !== 0) {
      warnings.push('Unmatched braces detected - some entries may not parse correctly');
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate RIS content
   */
  private validateRISContent(content: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const hasTypeTag = /^TY\s*-/m.test(content);
    const hasEndTag = /^ER\s*-/m.test(content);
    
    if (!hasTypeTag) {
      errors.push('No TY (Type) tags found');
    }
    if (!hasEndTag) {
      warnings.push('No ER (End Record) tags found - file may be incomplete');
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate JSON content
   */
  private validateJSONContent(content: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      const data = JSON.parse(content);
      
      if (!Array.isArray(data) && !data.documents && typeof data !== 'object') {
        errors.push('JSON must be an array of citations or an object with citation data');
      }
    } catch (error: any) {
      errors.push(`Invalid JSON format: ${error.message}`);
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate EndNote content
   */
  private validateEndNoteContent(content: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(content, 'text/xml');
      
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        errors.push('Invalid XML format');
      }
      
      const records = xmlDoc.querySelectorAll('record, Record');
      if (records.length === 0) {
        warnings.push('No record elements found');
      }
    } catch (error: any) {
      errors.push(`XML parsing error: ${error.message}`);
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate CSV content
   */
  private validateCSVContent(content: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      errors.push('CSV must have at least a header row and one data row');
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Get supported features for format
   */
  private getSupportedFeatures(format: CitationFileFormat): string[] {
    const features: Record<CitationFileFormat, string[]> = {
      'bibtex': ['Full metadata', 'LaTeX formatting', 'Custom fields', 'Cross-references'],
      'ris': ['Full metadata', 'Multiple authors', 'Keywords', 'Notes'],
      'json': ['Full metadata', 'Structured data', 'Custom fields', 'Nested objects'],
      'endnote': ['Full metadata', 'Reference types', 'Custom fields', 'File attachments'],
      'refworks': ['Basic metadata', 'Tagged format', 'Multiple authors'],
      'csv': ['Basic metadata', 'Custom columns', 'Bulk import'],
      'unknown': []
    };
    
    return features[format] || [];
  }
}

export const formatDetectorService = new FormatDetectorService();
