import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';

// Types for user articles
export interface UserArticle {
  id: string;
  user_id: string;
  title: string;
  research_field?: string;
  description?: string;
  status: 'draft' | 'in_progress' | 'completed' | 'archived';
  progress_percentage: number;
  total_words: number;
  total_sections: number;
  completed_sections: number;
  ai_model: string;
  keywords?: string[];
  figures_count: number;
  references_count: number;
  created_at: string;
  updated_at: string;
  last_accessed_at: string;
  is_favorite: boolean;
  tags?: string[];
  template_type: string;
  export_count: number;
  collaboration_enabled: boolean;
  folder_path: string;
  thumbnail_url?: string;
  estimated_completion_time?: string;
}

export interface UserArticleSection {
  id: string;
  article_id: string;
  section_type: string;
  section_order: number;
  title?: string;
  content?: string;
  word_count: number;
  status: 'empty' | 'draft' | 'completed' | 'reviewed';
  ai_generated: boolean;
  ai_model_used?: string;
  generation_prompt?: string;
  created_at: string;
  updated_at: string;
  version: number;
  parent_version_id?: string;
  enhancement_count: number;
  last_enhanced_at?: string;
}

export interface UserArticleReference {
  id: string;
  article_id: string;
  section_id?: string;
  reference_type: 'citation' | 'source' | 'figure' | 'table';
  title: string;
  authors?: string[];
  publication_year?: number;
  journal?: string;
  doi?: string;
  url?: string;
  abstract?: string;
  citation_style: string;
  citation_text?: string;
  is_verified: boolean;
  verification_score: number;
  source_type: 'ai_generated' | 'tavily' | 'manual' | 'imported';
  created_at: string;
  usage_count: number;
  last_used_at?: string;
}

export interface CreateArticleRequest {
  title: string;
  research_field?: string;
  description?: string;
  keywords?: string[];
  ai_model?: string;
  template_type?: string;
  tags?: string[];
}

export interface UpdateArticleRequest {
  title?: string;
  research_field?: string;
  description?: string;
  status?: 'draft' | 'in_progress' | 'completed' | 'archived';
  keywords?: string[];
  ai_model?: string;
  tags?: string[];
  is_favorite?: boolean;
}

export class UserArticlesService {
  private static instance: UserArticlesService;

  public static getInstance(): UserArticlesService {
    if (!UserArticlesService.instance) {
      UserArticlesService.instance = new UserArticlesService();
    }
    return UserArticlesService.instance;
  }

  // Create a new article
  async createArticle(request: CreateArticleRequest): Promise<UserArticle> {
    console.log('🆕 Creating new article:', request.title);
    
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const articleData = {
      user_id: user.user.id,
      title: request.title,
      research_field: request.research_field,
      description: request.description,
      keywords: request.keywords || [],
      ai_model: request.ai_model || 'gemini-2.5-flash',
      template_type: request.template_type || 'research_paper',
      tags: request.tags || [],
      status: 'draft' as const,
      progress_percentage: 0,
      total_words: 0,
      total_sections: 13,
      completed_sections: 0,
      figures_count: 0,
      references_count: 0,
      is_favorite: false,
      export_count: 0,
      collaboration_enabled: false,
      folder_path: '/'
    };

    const { data, error } = await supabase
      .from('user_articles')
      .insert(articleData)
      .select()
      .single();

    if (error) {
      console.error('❌ Failed to create article:', error);
      throw new Error(`Failed to create article: ${error.message}`);
    }

    console.log('✅ Article created successfully:', data.id);

    // Create default sections
    await this.createDefaultSections(data.id);

    return data;
  }

  // Get user's articles with filtering and sorting
  async getUserArticles(options: {
    status?: string;
    search?: string;
    tags?: string[];
    folder_path?: string;
    limit?: number;
    offset?: number;
    sort_by?: 'updated_at' | 'created_at' | 'title' | 'progress_percentage';
    sort_order?: 'asc' | 'desc';
  } = {}): Promise<{ articles: UserArticle[]; total_count: number }> {
    console.log('📋 Fetching user articles with options:', options);

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    let query = supabase
      .from('user_articles')
      .select('*', { count: 'exact' })
      .eq('user_id', user.user.id);

    // Apply filters
    if (options.status) {
      query = query.eq('status', options.status);
    }

    if (options.folder_path) {
      query = query.eq('folder_path', options.folder_path);
    }

    if (options.tags && options.tags.length > 0) {
      query = query.overlaps('tags', options.tags);
    }

    if (options.search) {
      query = query.textSearch('search_vector', options.search);
    }

    // Apply sorting
    const sortBy = options.sort_by || 'updated_at';
    const sortOrder = options.sort_order || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('❌ Failed to fetch articles:', error);
      throw new Error(`Failed to fetch articles: ${error.message}`);
    }

    console.log(`✅ Fetched ${data?.length || 0} articles`);

    return {
      articles: data || [],
      total_count: count || 0
    };
  }

  // Get a specific article with all its sections and references
  async getArticleById(articleId: string): Promise<{
    article: UserArticle;
    sections: UserArticleSection[];
    references: UserArticleReference[];
  }> {
    console.log('📖 Fetching article:', articleId);

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    // Get article
    const { data: article, error: articleError } = await supabase
      .from('user_articles')
      .select('*')
      .eq('id', articleId)
      .eq('user_id', user.user.id)
      .single();

    if (articleError) {
      console.error('❌ Failed to fetch article:', articleError);
      throw new Error(`Failed to fetch article: ${articleError.message}`);
    }

    // Get sections
    const { data: sections, error: sectionsError } = await supabase
      .from('user_article_sections')
      .select('*')
      .eq('article_id', articleId)
      .order('section_order');

    if (sectionsError) {
      console.error('❌ Failed to fetch sections:', sectionsError);
      throw new Error(`Failed to fetch sections: ${sectionsError.message}`);
    }

    // Get references
    const { data: references, error: referencesError } = await supabase
      .from('user_article_references')
      .select('*')
      .eq('article_id', articleId)
      .order('created_at');

    if (referencesError) {
      console.error('❌ Failed to fetch references:', referencesError);
      throw new Error(`Failed to fetch references: ${referencesError.message}`);
    }

    // Update last accessed time
    await this.updateLastAccessed(articleId);

    console.log('✅ Article fetched successfully');

    return {
      article,
      sections: sections || [],
      references: references || []
    };
  }

  // Update article metadata
  async updateArticle(articleId: string, updates: UpdateArticleRequest): Promise<UserArticle> {
    console.log('📝 Updating article:', articleId, updates);

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_articles')
      .update(updates)
      .eq('id', articleId)
      .eq('user_id', user.user.id)
      .select()
      .single();

    if (error) {
      console.error('❌ Failed to update article:', error);
      throw new Error(`Failed to update article: ${error.message}`);
    }

    console.log('✅ Article updated successfully');
    return data;
  }

  // Delete an article
  async deleteArticle(articleId: string): Promise<void> {
    console.log('🗑️ Deleting article:', articleId);

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('user_articles')
      .delete()
      .eq('id', articleId)
      .eq('user_id', user.user.id);

    if (error) {
      console.error('❌ Failed to delete article:', error);
      throw new Error(`Failed to delete article: ${error.message}`);
    }

    console.log('✅ Article deleted successfully');
  }

  // Create default sections for a new article
  private async createDefaultSections(articleId: string): Promise<void> {
    const defaultSections = [
      { type: 'title', order: 1, title: 'Title' },
      { type: 'authors', order: 2, title: 'Authors' },
      { type: 'keywords', order: 3, title: 'Keywords' },
      { type: 'abstract', order: 4, title: 'Abstract' },
      { type: 'introduction', order: 5, title: 'Introduction' },
      { type: 'literature_review', order: 6, title: 'Literature Review' },
      { type: 'methodology', order: 7, title: 'Methodology' },
      { type: 'results', order: 8, title: 'Results' },
      { type: 'discussion', order: 9, title: 'Discussion' },
      { type: 'results_discussion', order: 10, title: 'Results & Discussion' },
      { type: 'discussion_conclusion', order: 11, title: 'Discussion & Conclusion' },
      { type: 'conclusion', order: 12, title: 'Conclusion' },
      { type: 'acknowledgments', order: 13, title: 'Acknowledgments' }
    ];

    const sectionsData = defaultSections.map(section => ({
      article_id: articleId,
      section_type: section.type,
      section_order: section.order,
      title: section.title,
      content: '',
      word_count: 0,
      status: 'empty' as const,
      ai_generated: false,
      version: 1,
      enhancement_count: 0
    }));

    const { error } = await supabase
      .from('user_article_sections')
      .insert(sectionsData);

    if (error) {
      console.error('❌ Failed to create default sections:', error);
      throw new Error(`Failed to create default sections: ${error.message}`);
    }

    console.log('✅ Default sections created');
  }

  // Update last accessed time
  private async updateLastAccessed(articleId: string): Promise<void> {
    await supabase
      .from('user_articles')
      .update({ last_accessed_at: new Date().toISOString() })
      .eq('id', articleId);
  }

  // Get article statistics for dashboard
  async getArticleStats(): Promise<{
    total_articles: number;
    draft_articles: number;
    in_progress_articles: number;
    completed_articles: number;
    total_words: number;
    recent_activity: any[];
  }> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    // Get article counts by status
    const { data: stats } = await supabase
      .from('user_articles')
      .select('status, total_words')
      .eq('user_id', user.user.id);

    const totalArticles = stats?.length || 0;
    const draftArticles = stats?.filter(a => a.status === 'draft').length || 0;
    const inProgressArticles = stats?.filter(a => a.status === 'in_progress').length || 0;
    const completedArticles = stats?.filter(a => a.status === 'completed').length || 0;
    const totalWords = stats?.reduce((sum, a) => sum + (a.total_words || 0), 0) || 0;

    // Get recent activity
    const { data: recentActivity } = await supabase
      .from('user_article_history')
      .select(`
        *,
        user_articles!inner(title)
      `)
      .eq('user_id', user.user.id)
      .order('created_at', { ascending: false })
      .limit(10);

    return {
      total_articles: totalArticles,
      draft_articles: draftArticles,
      in_progress_articles: inProgressArticles,
      completed_articles: completedArticles,
      total_words: totalWords,
      recent_activity: recentActivity || []
    };
  }
}
