/**
 * Modern Response to Reviews Component
 * Enhanced design with focus on results display and better comment processing
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  MessageSquare,
  FileText,
  Download,
  CheckCircle,
  Eye,
  Lightbulb,
  Clock,
  Target,
  FileDown,
  AlertTriangle,
  Brain,
  Star,
  Play,
  Zap,
  Users,
  Edit3,
  ArrowRight,
  ChevronDown,
  ChevronUp,
  BookOpen,
  FileCheck,
  Sparkles
} from 'lucide-react';

import { CompleteArticle, ReviewerCommentsSet, ArticleSection } from '../types';
import { EnhancedResponseAIService, EnhancedResponseResult } from '../services/enhanced-response-ai.service';
import { enhancedExportService } from '../services/enhanced-response-export.service';
import { EnhancedResultsView } from './EnhancedResultsView';

interface ModernResponseToReviewsProps {
  article: CompleteArticle;
  selectedModel?: string;
  researchField?: string;
}

export function ModernResponseToReviews({
  article,
  selectedModel = 'google/gemini-2.5-flash',
  researchField = 'Computer Science'
}: ModernResponseToReviewsProps) {
  const [activeTab, setActiveTab] = useState('input');
  const [showFullResults, setShowFullResults] = useState(false);
  const [reviewerComments, setReviewerComments] = useState<ReviewerCommentsSet[]>([
    { reviewerNumber: 1, comments: [], bulkComments: '', overallRecommendation: 'minor-revision' },
    { reviewerNumber: 2, comments: [], bulkComments: '', overallRecommendation: 'minor-revision' },
    { reviewerNumber: 3, comments: [], bulkComments: '', overallRecommendation: 'minor-revision' }
  ]);

  // Enhanced state
  const [enhancedResult, setEnhancedResult] = useState<EnhancedResponseResult | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);
  const [stepMessage, setStepMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Enhanced analysis function
  const startEnhancedAnalysis = async () => {
    setIsProcessing(true);
    setCurrentStep(0);
    setStepProgress(0);
    setStepMessage('Initializing enhanced analysis...');
    setActiveTab('results'); // Switch to results immediately
    
    try {
      const aiService = new EnhancedResponseAIService();
      
      const options = {
        model: selectedModel,
        analysisDepth: 'comprehensive' as const,
        researchField,
        includeNoveltyAnalysis: false,
        includeJournalRecommendations: false,
        targetJournals: []
      };

      const result = await aiService.generateEnhancedResponse(
        article,
        reviewerComments,
        options,
        (step: number, progress: number, message: string) => {
          setCurrentStep(step);
          setStepProgress(progress);
          setStepMessage(message);
        }
      );

      setEnhancedResult(result);
      setIsProcessing(false);
      setStepMessage('Enhanced analysis complete!');
      setShowFullResults(true); // Show full results page

    } catch (error) {
      console.error('Enhanced analysis failed:', error);
      setIsProcessing(false);
      setStepMessage('Analysis failed. Please try again.');
    }
  };

  // Update reviewer comments
  const updateReviewerComments = (reviewerNumber: number, field: string, value: string) => {
    setReviewerComments(prev => prev.map(reviewer => 
      reviewer.reviewerNumber === reviewerNumber 
        ? { ...reviewer, [field]: value }
        : reviewer
    ));
  };

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Back to input handler
  const handleBackToInput = () => {
    setShowFullResults(false);
    setActiveTab('input');
  };

  const totalComments = reviewerComments.reduce((total, reviewer) => {
    const bulkCount = reviewer.bulkComments?.trim() ? 1 : 0;
    return total + bulkCount;
  }, 0);

  // Show full results view if analysis is complete
  if (showFullResults && enhancedResult) {
    return (
      <EnhancedResultsView
        result={enhancedResult}
        onBack={handleBackToInput}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Enhanced Response to Reviews
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Advanced AI-powered system for generating comprehensive responses to academic peer reviews
          </p>
        </div>

        {/* Main Content - Input Only */}
        <div className="w-full">

          {/* Input Section */}
          <div className="space-y-6">
            {/* Article Overview */}
            <Card className="border-blue-200 shadow-lg">
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center gap-2 text-blue-900">
                  <BookOpen className="h-5 w-5" />
                  Article Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Title</Label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">{article.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Sections</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {Object.keys(article.content).length} sections available
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reviewer Comments Input */}
            <Card className="border-purple-200 shadow-lg">
              <CardHeader className="bg-purple-50">
                <CardTitle className="flex items-center gap-2 text-purple-900">
                  <Users className="h-5 w-5" />
                  Reviewer Comments
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {reviewerComments.map((reviewer) => (
                    <div key={reviewer.reviewerNumber} className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">
                          Reviewer {reviewer.reviewerNumber}
                        </h3>
                        <Badge variant="outline" className="text-xs">
                          {reviewer.overallRecommendation}
                        </Badge>
                      </div>
                      
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">
                          Comments (paste all comments here)
                        </Label>
                        <Textarea
                          placeholder={`Paste all comments from Reviewer ${reviewer.reviewerNumber} here. The system will automatically parse numbered points (1., 2., 3.) and bullet points.`}
                          value={reviewer.bulkComments}
                          onChange={(e) => updateReviewerComments(reviewer.reviewerNumber, 'bulkComments', e.target.value)}
                          className="min-h-[120px] text-sm"
                        />
                        {reviewer.bulkComments && (
                          <p className="text-xs text-green-600">
                            ✓ Comments ready for processing
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Start Analysis Button */}
                <div className="mt-8 text-center">
                  <Button 
                    onClick={startEnhancedAnalysis} 
                    size="lg" 
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 text-lg"
                    disabled={isProcessing || totalComments === 0}
                  >
                    <Zap className="h-6 w-6 mr-3" />
                    {isProcessing ? 'Processing...' : 'Start Enhanced Analysis'}
                  </Button>
                  <p className="text-sm text-gray-600 mt-3">
                    {totalComments === 0 
                      ? 'Please add reviewer comments to begin analysis'
                      : `Ready to process ${totalComments} reviewer comment sets`
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Processing Status */}
          {isProcessing && (
              <Card className="border-blue-200 shadow-lg">
                <CardHeader className="bg-blue-50">
                  <CardTitle className="flex items-center gap-2 text-blue-900">
                    <Clock className="h-5 w-5 animate-spin" />
                    Processing Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium">
                        Step {currentStep}/3: {stepMessage}
                      </span>
                      <span className="text-lg font-bold text-blue-600">
                        {stepProgress}%
                      </span>
                    </div>
                    <Progress value={stepProgress} className="h-3" />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                      {[
                        { step: 1, title: 'Comment Analysis', desc: 'Understanding reviewer feedback' },
                        { step: 2, title: 'Article Revision', desc: 'Making targeted improvements' },
                        { step: 3, title: 'Output Generation', desc: 'Creating response documents' }
                      ].map((phase) => (
                        <div key={phase.step} className="flex items-center gap-3 p-4 border rounded-lg">
                          <div className={`w-4 h-4 rounded-full ${
                            currentStep > phase.step ? 'bg-green-500' :
                            currentStep === phase.step ? 'bg-blue-500 animate-pulse' :
                            'bg-gray-300'
                          }`} />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{phase.title}</div>
                            <div className="text-sm text-gray-600">{phase.desc}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Completion Message */}
          {enhancedResult && !isProcessing && (
            <Card className="border-green-200 shadow-lg">
              <CardHeader className="bg-green-50">
                <CardTitle className="flex items-center gap-2 text-green-900">
                  <CheckCircle className="h-6 w-6" />
                  Analysis Complete!
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-lg text-gray-700 mb-6">
                    Your enhanced response to reviews has been generated successfully.
                    View the beautiful, formatted results with proper document export options.
                  </p>
                  <Button
                    onClick={() => setShowFullResults(true)}
                    size="lg"
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-8 py-4 text-lg"
                  >
                    <Eye className="h-6 w-6 mr-3" />
                    View Enhanced Results
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
