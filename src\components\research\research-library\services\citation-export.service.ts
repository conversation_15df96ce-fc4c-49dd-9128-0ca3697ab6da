/**
 * Citation Export Service
 * Handles exporting citations in multiple formats and bibliography generation
 */

import { 
  ProcessedCitationData,
  CitationExportOptions,
  BibliographyOptions,
  CitationImportServiceResponse
} from '../types/citation-import.types';

export class CitationExportService {
  /**
   * Export citations in specified format
   */
  async exportCitations(
    citations: any[], // LibraryArticle format from database
    options: CitationExportOptions
  ): Promise<CitationImportServiceResponse<string>> {
    try {
      let exportContent: string;
      
      switch (options.format) {
        case 'bibtex':
          exportContent = this.exportToBibTeX(citations, options);
          break;
        case 'ris':
          exportContent = this.exportToRIS(citations, options);
          break;
        case 'json':
          exportContent = this.exportToJSON(citations, options);
          break;
        case 'csv':
          exportContent = this.exportToCSV(citations, options);
          break;
        case 'endnote':
          exportContent = this.exportToEndNote(citations, options);
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }
      
      return {
        success: true,
        data: exportContent,
        error: null
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `Export failed: ${error.message}`
      };
    }
  }

  /**
   * Generate bibliography in specified style
   */
  async generateBibliography(
    citations: any[],
    options: BibliographyOptions
  ): Promise<CitationImportServiceResponse<string>> {
    try {
      // Sort citations
      const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
      
      // Group citations if requested
      const groupedCitations = options.groupBy 
        ? this.groupCitations(sortedCitations, options.groupBy)
        : { 'Bibliography': sortedCitations };
      
      let bibliography = '';
      
      for (const [groupName, groupCitations] of Object.entries(groupedCitations)) {
        if (Object.keys(groupedCitations).length > 1) {
          bibliography += this.formatGroupHeader(groupName, options.format);
        }
        
        for (const citation of groupCitations) {
          const formattedCitation = this.formatCitation(citation, options.style, options.annotated);
          bibliography += this.formatBibliographyEntry(formattedCitation, options.format);
        }
        
        if (Object.keys(groupedCitations).length > 1) {
          bibliography += this.formatGroupFooter(options.format);
        }
      }
      
      return {
        success: true,
        data: bibliography,
        error: null
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `Bibliography generation failed: ${error.message}`
      };
    }
  }

  /**
   * Export to BibTeX format
   */
  private exportToBibTeX(citations: any[], options: CitationExportOptions): string {
    const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
    
    return sortedCitations.map(citation => {
      const type = this.mapToBibTeXType(citation.extracted_metadata?.documentType || 'other');
      const key = this.generateBibTeXKey(citation);
      
      let entry = `@${type}{${key},\n`;
      
      // Required fields
      if (citation.title) {
        entry += `  title = {${this.escapeBibTeX(citation.title)}},\n`;
      }
      
      if (citation.authors && citation.authors.length > 0) {
        const authors = citation.authors.join(' and ');
        entry += `  author = {${this.escapeBibTeX(authors)}},\n`;
      }
      
      if (citation.publication_year) {
        entry += `  year = {${citation.publication_year}},\n`;
      }
      
      // Optional fields
      if (citation.journal) {
        entry += `  journal = {${this.escapeBibTeX(citation.journal)}},\n`;
      }
      
      if (citation.volume) {
        entry += `  volume = {${citation.volume}},\n`;
      }
      
      if (citation.issue) {
        entry += `  number = {${citation.issue}},\n`;
      }
      
      if (citation.pages) {
        entry += `  pages = {${citation.pages}},\n`;
      }
      
      if (citation.doi) {
        entry += `  doi = {${citation.doi}},\n`;
      }
      
      if (options.includeAbstracts && citation.abstract) {
        entry += `  abstract = {${this.escapeBibTeX(citation.abstract)}},\n`;
      }
      
      if (options.includeKeywords && citation.keywords && citation.keywords.length > 0) {
        entry += `  keywords = {${citation.keywords.join(', ')}},\n`;
      }
      
      if (citation.source_url) {
        entry += `  url = {${citation.source_url}},\n`;
      }
      
      // Remove trailing comma and close entry
      entry = entry.replace(/,\n$/, '\n');
      entry += '}\n\n';
      
      return entry;
    }).join('');
  }

  /**
   * Export to RIS format
   */
  private exportToRIS(citations: any[], options: CitationExportOptions): string {
    const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
    
    return sortedCitations.map(citation => {
      const type = this.mapToRISType(citation.extracted_metadata?.documentType || 'other');
      
      let entry = `TY  - ${type}\n`;
      
      if (citation.title) {
        entry += `TI  - ${citation.title}\n`;
      }
      
      if (citation.authors && citation.authors.length > 0) {
        citation.authors.forEach((author: string) => {
          entry += `AU  - ${author}\n`;
        });
      }
      
      if (citation.publication_year) {
        entry += `PY  - ${citation.publication_year}\n`;
      }
      
      if (citation.journal) {
        entry += `JO  - ${citation.journal}\n`;
      }
      
      if (citation.volume) {
        entry += `VL  - ${citation.volume}\n`;
      }
      
      if (citation.issue) {
        entry += `IS  - ${citation.issue}\n`;
      }
      
      if (citation.pages) {
        const pages = citation.pages.split('-');
        if (pages.length >= 2) {
          entry += `SP  - ${pages[0].trim()}\n`;
          entry += `EP  - ${pages[1].trim()}\n`;
        } else {
          entry += `SP  - ${citation.pages}\n`;
        }
      }
      
      if (citation.doi) {
        entry += `DO  - ${citation.doi}\n`;
      }
      
      if (options.includeAbstracts && citation.abstract) {
        entry += `AB  - ${citation.abstract}\n`;
      }
      
      if (options.includeKeywords && citation.keywords && citation.keywords.length > 0) {
        citation.keywords.forEach((keyword: string) => {
          entry += `KW  - ${keyword}\n`;
        });
      }
      
      if (citation.source_url) {
        entry += `UR  - ${citation.source_url}\n`;
      }
      
      entry += 'ER  - \n\n';
      
      return entry;
    }).join('');
  }

  /**
   * Export to JSON format
   */
  private exportToJSON(citations: any[], options: CitationExportOptions): string {
    const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
    
    const exportData = sortedCitations.map(citation => {
      const jsonCitation: any = {
        title: citation.title,
        authors: citation.authors || [],
        year: citation.publication_year,
        journal: citation.journal,
        volume: citation.volume,
        issue: citation.issue,
        pages: citation.pages,
        doi: citation.doi,
        url: citation.source_url
      };
      
      if (options.includeAbstracts && citation.abstract) {
        jsonCitation.abstract = citation.abstract;
      }
      
      if (options.includeKeywords && citation.keywords) {
        jsonCitation.keywords = citation.keywords;
      }
      
      if (options.includeTags && citation.tags) {
        jsonCitation.tags = citation.tags;
      }
      
      if (options.includeNotes && citation.notes) {
        jsonCitation.notes = citation.notes;
      }
      
      return jsonCitation;
    });
    
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export to CSV format
   */
  private exportToCSV(citations: any[], options: CitationExportOptions): string {
    const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
    
    // Define CSV headers
    const headers = [
      'Title',
      'Authors',
      'Year',
      'Journal',
      'Volume',
      'Issue',
      'Pages',
      'DOI',
      'URL'
    ];
    
    if (options.includeAbstracts) headers.push('Abstract');
    if (options.includeKeywords) headers.push('Keywords');
    if (options.includeTags) headers.push('Tags');
    if (options.includeNotes) headers.push('Notes');
    
    let csv = headers.join(',') + '\n';
    
    sortedCitations.forEach(citation => {
      const row = [
        this.escapeCSV(citation.title || ''),
        this.escapeCSV(citation.authors?.join('; ') || ''),
        citation.publication_year || '',
        this.escapeCSV(citation.journal || ''),
        citation.volume || '',
        citation.issue || '',
        citation.pages || '',
        citation.doi || '',
        citation.source_url || ''
      ];
      
      if (options.includeAbstracts) row.push(this.escapeCSV(citation.abstract || ''));
      if (options.includeKeywords) row.push(this.escapeCSV(citation.keywords?.join('; ') || ''));
      if (options.includeTags) row.push(this.escapeCSV(citation.tags?.join('; ') || ''));
      if (options.includeNotes) row.push(this.escapeCSV(citation.notes || ''));
      
      csv += row.join(',') + '\n';
    });
    
    return csv;
  }

  /**
   * Export to EndNote XML format
   */
  private exportToEndNote(citations: any[], options: CitationExportOptions): string {
    const sortedCitations = this.sortCitations(citations, options.sortBy, options.sortOrder);
    
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<xml>\n<records>\n';
    
    sortedCitations.forEach((citation, index) => {
      xml += `<record>\n`;
      xml += `<database name="Research Library" path="Research Library.enl">${index + 1}</database>\n`;
      xml += `<source-app name="Research Library" version="1.0">Research Library</source-app>\n`;
      xml += `<rec-number>${index + 1}</rec-number>\n`;
      
      const refType = this.mapToEndNoteType(citation.extracted_metadata?.documentType || 'other');
      xml += `<ref-type name="${refType}">${refType}</ref-type>\n`;
      
      xml += '<contributors>\n<authors>\n';
      if (citation.authors && citation.authors.length > 0) {
        citation.authors.forEach((author: string) => {
          xml += `<author>${this.escapeXML(author)}</author>\n`;
        });
      }
      xml += '</authors>\n</contributors>\n';
      
      xml += '<titles>\n';
      if (citation.title) {
        xml += `<title>${this.escapeXML(citation.title)}</title>\n`;
      }
      if (citation.journal) {
        xml += `<secondary-title>${this.escapeXML(citation.journal)}</secondary-title>\n`;
      }
      xml += '</titles>\n';
      
      xml += '<dates>\n';
      if (citation.publication_year) {
        xml += `<year>${citation.publication_year}</year>\n`;
      }
      xml += '</dates>\n';
      
      if (citation.volume) {
        xml += `<volume>${citation.volume}</volume>\n`;
      }
      
      if (citation.issue) {
        xml += `<number>${citation.issue}</number>\n`;
      }
      
      if (citation.pages) {
        xml += `<pages>${citation.pages}</pages>\n`;
      }
      
      if (citation.doi) {
        xml += `<electronic-resource-num>${citation.doi}</electronic-resource-num>\n`;
      }
      
      if (options.includeAbstracts && citation.abstract) {
        xml += `<abstract>${this.escapeXML(citation.abstract)}</abstract>\n`;
      }
      
      if (options.includeKeywords && citation.keywords && citation.keywords.length > 0) {
        xml += '<keywords>\n';
        citation.keywords.forEach((keyword: string) => {
          xml += `<keyword>${this.escapeXML(keyword)}</keyword>\n`;
        });
        xml += '</keywords>\n';
      }
      
      if (citation.source_url) {
        xml += '<urls>\n';
        xml += `<related-urls><url>${this.escapeXML(citation.source_url)}</url></related-urls>\n`;
        xml += '</urls>\n';
      }
      
      xml += '</record>\n';
    });
    
    xml += '</records>\n</xml>';
    
    return xml;
  }

  /**
   * Sort citations
   */
  private sortCitations(citations: any[], sortBy: string, sortOrder: 'asc' | 'desc'): any[] {
    return [...citations].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title || '';
          bValue = b.title || '';
          break;
        case 'author':
          aValue = a.authors?.[0] || '';
          bValue = b.authors?.[0] || '';
          break;
        case 'year':
          aValue = a.publication_year || 0;
          bValue = b.publication_year || 0;
          break;
        case 'journal':
          aValue = a.journal || '';
          bValue = b.journal || '';
          break;
        default:
          return 0;
      }
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Group citations
   */
  private groupCitations(citations: any[], groupBy: string): Record<string, any[]> {
    const groups: Record<string, any[]> = {};
    
    citations.forEach(citation => {
      let groupKey: string;
      
      switch (groupBy) {
        case 'author':
          groupKey = citation.authors?.[0]?.split(' ').pop() || 'Unknown';
          break;
        case 'year':
          groupKey = citation.publication_year?.toString() || 'Unknown';
          break;
        case 'journal':
          groupKey = citation.journal || 'Unknown';
          break;
        case 'type':
          groupKey = citation.extracted_metadata?.documentType || 'Unknown';
          break;
        default:
          groupKey = 'All';
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(citation);
    });
    
    return groups;
  }

  /**
   * Format citation according to style
   */
  private formatCitation(citation: any, style: string, annotated: boolean): string {
    // This is a simplified implementation - in a real system you'd use a proper citation formatter
    const authors = citation.authors?.join(', ') || 'Unknown Author';
    const year = citation.publication_year || 'n.d.';
    const title = citation.title || 'Untitled';
    const journal = citation.journal || '';
    
    let formatted = '';
    
    switch (style) {
      case 'apa':
        formatted = `${authors} (${year}). ${title}. ${journal}`;
        if (citation.volume) formatted += `, ${citation.volume}`;
        if (citation.issue) formatted += `(${citation.issue})`;
        if (citation.pages) formatted += `, ${citation.pages}`;
        if (citation.doi) formatted += `. https://doi.org/${citation.doi}`;
        break;
      case 'mla':
        formatted = `${authors}. "${title}" ${journal}`;
        if (citation.volume) formatted += ` ${citation.volume}`;
        if (citation.issue) formatted += `.${citation.issue}`;
        formatted += ` (${year})`;
        if (citation.pages) formatted += `: ${citation.pages}`;
        break;
      default:
        formatted = `${authors}. ${title}. ${journal}. ${year}`;
    }
    
    if (annotated && citation.abstract) {
      formatted += `\n\nAbstract: ${citation.abstract}`;
    }
    
    return formatted;
  }

  /**
   * Format bibliography entry
   */
  private formatBibliographyEntry(citation: string, format: string): string {
    switch (format) {
      case 'html':
        return `<p>${citation}</p>\n`;
      case 'markdown':
        return `${citation}\n\n`;
      case 'rtf':
        return `${citation}\\par\n`;
      default:
        return `${citation}\n\n`;
    }
  }

  /**
   * Format group header
   */
  private formatGroupHeader(groupName: string, format: string): string {
    switch (format) {
      case 'html':
        return `<h3>${groupName}</h3>\n`;
      case 'markdown':
        return `### ${groupName}\n\n`;
      case 'rtf':
        return `{\\b ${groupName}}\\par\n`;
      default:
        return `${groupName}\n${'='.repeat(groupName.length)}\n\n`;
    }
  }

  /**
   * Format group footer
   */
  private formatGroupFooter(format: string): string {
    switch (format) {
      case 'html':
        return '\n';
      case 'markdown':
        return '\n';
      case 'rtf':
        return '\\par\n';
      default:
        return '\n';
    }
  }

  // Helper methods for escaping and mapping
  private escapeBibTeX(text: string): string {
    return text.replace(/[{}]/g, '\\$&');
  }

  private escapeCSV(text: string): string {
    if (text.includes(',') || text.includes('"') || text.includes('\n')) {
      return `"${text.replace(/"/g, '""')}"`;
    }
    return text;
  }

  private escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  private generateBibTeXKey(citation: any): string {
    const author = citation.authors?.[0]?.split(' ').pop()?.toLowerCase() || 'unknown';
    const year = citation.publication_year || 'nodate';
    const title = citation.title?.split(' ')[0]?.toLowerCase() || 'untitled';
    return `${author}${year}${title}`;
  }

  private mapToBibTeXType(documentType: string): string {
    const typeMap: Record<string, string> = {
      'journal-article': 'article',
      'conference-paper': 'inproceedings',
      'book': 'book',
      'book-chapter': 'incollection',
      'thesis': 'phdthesis',
      'report': 'techreport',
      'webpage': 'misc',
      'patent': 'misc',
      'preprint': 'unpublished',
      'other': 'misc'
    };
    return typeMap[documentType] || 'misc';
  }

  private mapToRISType(documentType: string): string {
    const typeMap: Record<string, string> = {
      'journal-article': 'JOUR',
      'conference-paper': 'CONF',
      'book': 'BOOK',
      'book-chapter': 'CHAP',
      'thesis': 'THES',
      'report': 'RPRT',
      'webpage': 'ELEC',
      'patent': 'PAT',
      'preprint': 'UNPB',
      'other': 'GEN'
    };
    return typeMap[documentType] || 'GEN';
  }

  private mapToEndNoteType(documentType: string): string {
    const typeMap: Record<string, string> = {
      'journal-article': 'Journal Article',
      'conference-paper': 'Conference Proceedings',
      'book': 'Book',
      'book-chapter': 'Book Section',
      'thesis': 'Thesis',
      'report': 'Report',
      'webpage': 'Web Page',
      'patent': 'Patent',
      'preprint': 'Manuscript',
      'other': 'Generic'
    };
    return typeMap[documentType] || 'Generic';
  }
}

export const citationExportService = new CitationExportService();
