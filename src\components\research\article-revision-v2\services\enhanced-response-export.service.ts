/**
 * Enhanced Export Service for Response to Reviews
 * Handles markdown content conversion and beautiful document formatting
 */

import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';
import jsPDF from 'jspdf';

/**
 * Convert markdown-style content to properly formatted HTML
 */
export function markdownToHtml(content: string): string {
  if (!content) return '';
  
  let html = content
    // Convert headings
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    
    // Convert bold text (preserve existing bold formatting)
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    
    // Convert italic text
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // Convert line breaks to paragraphs
    .split('\n\n')
    .map(paragraph => {
      if (paragraph.trim()) {
        // Don't wrap headings in paragraphs
        if (paragraph.includes('<h1>') || paragraph.includes('<h2>') || paragraph.includes('<h3>')) {
          return paragraph;
        }
        return `<p>${paragraph.replace(/\n/g, '<br>')}</p>`;
      }
      return '';
    })
    .filter(p => p)
    .join('\n');
    
  return html;
}

/**
 * Create a beautifully formatted Word document from markdown content
 */
async function createEnhancedDocx(title: string, content: string): Promise<Document> {
  const FONT = {
    name: "Times New Roman",
    ascii: "Times New Roman",
    eastAsia: "Times New Roman",
    hAnsi: "Times New Roman"
  };

  const documentChildren = [];

  // Add title
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 32,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { after: 400 },
      alignment: AlignmentType.CENTER
    })
  );

  // Convert markdown to HTML and parse
  const htmlContent = markdownToHtml(content);
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  // Process each element
  Array.from(tempDiv.children).forEach((element) => {
    const tagName = element.tagName.toLowerCase();
    const text = element.textContent?.trim() || '';
    
    if (!text) return;

    switch (tagName) {
      case 'h1':
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text,
                bold: true,
                size: 28,
                color: "000000",
                font: FONT
              })
            ],
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
            alignment: AlignmentType.LEFT
          })
        );
        break;
        
      case 'h2':
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text,
                bold: true,
                size: 26,
                color: "000000",
                font: FONT
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 150 },
            alignment: AlignmentType.LEFT
          })
        );
        break;
        
      case 'h3':
        documentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text,
                bold: true,
                size: 24,
                color: "000000",
                font: FONT
              })
            ],
            heading: HeadingLevel.HEADING_3,
            spacing: { before: 250, after: 120 },
            alignment: AlignmentType.LEFT
          })
        );
        break;
        
      case 'p':
        // Parse paragraph for mixed formatting
        const runs: TextRun[] = [];
        
        // Simple approach: split by HTML tags and process
        const innerHTML = element.innerHTML;
        const parts = innerHTML.split(/(<\/?(?:strong|em|b|i)>)/);
        
        let isBold = false;
        let isItalic = false;
        
        parts.forEach(part => {
          if (part === '<strong>' || part === '<b>') {
            isBold = true;
          } else if (part === '</strong>' || part === '</b>') {
            isBold = false;
          } else if (part === '<em>' || part === '<i>') {
            isItalic = true;
          } else if (part === '</em>' || part === '</i>') {
            isItalic = false;
          } else if (part && !part.startsWith('<')) {
            // Clean text content
            const cleanText = part.replace(/<br>/g, '\n').replace(/&nbsp;/g, ' ');
            if (cleanText.trim()) {
              runs.push(
                new TextRun({
                  text: cleanText,
                  bold: isBold,
                  italics: isItalic,
                  font: FONT,
                  size: 22,
                  color: "000000"
                })
              );
            }
          }
        });
        
        if (runs.length > 0) {
          documentChildren.push(
            new Paragraph({
              children: runs,
              spacing: { after: 200, line: 300 },
              alignment: AlignmentType.JUSTIFY
            })
          );
        }
        break;
    }
  });

  return new Document({
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,  // 1 inch
              bottom: 1440, // 1 inch
              left: 1440,   // 1 inch
            },
          },
        },
        children: documentChildren,
      },
    ],
  });
}

/**
 * Export enhanced content as DOCX
 */
export async function exportEnhancedDocx(title: string, content: string, fileName: string): Promise<void> {
  try {
    const doc = await createEnhancedDocx(title, content);
    const blob = await Packer.toBlob(doc);
    const finalFileName = fileName.endsWith('.docx') ? fileName : `${fileName}.docx`;
    saveAs(blob, finalFileName);
  } catch (error) {
    console.error('Error exporting enhanced DOCX:', error);
    throw error;
  }
}

/**
 * Export enhanced content as PDF with optimized size and formatting
 */
export async function exportEnhancedPdf(title: string, content: string, fileName: string): Promise<void> {
  try {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set up fonts and styling
    pdf.setFont('times', 'normal');
    pdf.setFontSize(12);

    const pageWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const margin = 20;
    const contentWidth = pageWidth - (2 * margin);

    let yPosition = margin;

    // Add title
    pdf.setFontSize(16);
    pdf.setFont('times', 'bold');
    const titleLines = pdf.splitTextToSize(title, contentWidth);
    pdf.text(titleLines, margin, yPosition);
    yPosition += titleLines.length * 8 + 10;

    // Convert markdown to clean text with formatting markers
    const htmlContent = markdownToHtml(content);
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // Process content elements
    Array.from(tempDiv.children).forEach((element) => {
      const tagName = element.tagName.toLowerCase();
      const text = element.textContent?.trim() || '';

      if (!text) return;

      // Check if we need a new page
      if (yPosition > pageHeight - 30) {
        pdf.addPage();
        yPosition = margin;
      }

      switch (tagName) {
        case 'h1':
          pdf.setFontSize(14);
          pdf.setFont('times', 'bold');
          yPosition += 8;
          const h1Lines = pdf.splitTextToSize(text, contentWidth);
          pdf.text(h1Lines, margin, yPosition);
          yPosition += h1Lines.length * 7 + 6;
          break;

        case 'h2':
          pdf.setFontSize(13);
          pdf.setFont('times', 'bold');
          yPosition += 6;
          const h2Lines = pdf.splitTextToSize(text, contentWidth);
          pdf.text(h2Lines, margin, yPosition);
          yPosition += h2Lines.length * 6.5 + 5;
          break;

        case 'h3':
          pdf.setFontSize(12);
          pdf.setFont('times', 'bold');
          yPosition += 5;
          const h3Lines = pdf.splitTextToSize(text, contentWidth);
          pdf.text(h3Lines, margin, yPosition);
          yPosition += h3Lines.length * 6 + 4;
          break;

        case 'p':
          pdf.setFontSize(11);
          pdf.setFont('times', 'normal');

          // Handle bold text within paragraphs
          const innerHTML = element.innerHTML;
          if (innerHTML.includes('<strong>') || innerHTML.includes('<b>')) {
            // Split by bold tags and process separately
            const parts = innerHTML.split(/(<\/?(?:strong|b)>)/);
            let currentX = margin;

            parts.forEach(part => {
              if (part === '<strong>' || part === '<b>') {
                pdf.setFont('times', 'bold');
              } else if (part === '</strong>' || part === '</b>') {
                pdf.setFont('times', 'normal');
              } else if (part && !part.startsWith('<')) {
                const cleanText = part.replace(/<br>/g, ' ').replace(/&nbsp;/g, ' ');
                if (cleanText.trim()) {
                  const textWidth = pdf.getTextWidth(cleanText);
                  if (currentX + textWidth > pageWidth - margin) {
                    yPosition += 6;
                    currentX = margin;
                  }
                  pdf.text(cleanText, currentX, yPosition);
                  currentX += textWidth;
                }
              }
            });
            yPosition += 8;
          } else {
            // Regular paragraph
            const pLines = pdf.splitTextToSize(text, contentWidth);
            pdf.text(pLines, margin, yPosition);
            yPosition += pLines.length * 5.5 + 4;
          }
          break;
      }
    });

    // Save the PDF
    pdf.save(fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`);
  } catch (error) {
    console.error('Error exporting enhanced PDF:', error);
    throw error;
  }
}

export const enhancedExportService = {
  exportEnhancedDocx,
  exportEnhancedPdf,
  markdownToHtml
};
