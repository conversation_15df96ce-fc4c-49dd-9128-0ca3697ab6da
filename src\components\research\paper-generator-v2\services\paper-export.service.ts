/**
 * Simple Paper Export Service
 * Handles combining sections and exporting to Word/PDF formats
 */

import { Paper } from '../types';
import { SECTION_TYPES } from '../constants';
import { documentExportService } from '../../paper-generator/document-export.service';
import { toast } from 'sonner';

export interface CombinedPaperContent {
  title: string;
  content: string;
  wordCount: number;
  sectionCount: number;
}

export class PaperExportService {
  /**
   * Combine all sections with substantial content into a single formatted document
   */
  static combinePaperSections(paper: Paper): CombinedPaperContent {
    console.log('🔄 Export: Starting paper section combination', {
      totalSections: paper.sections.length,
      paperTitle: paper.metadata.title
    });

    // Filter sections with more than 5 words
    const sectionsWithContent = paper.sections.filter(section => {
      const trimmedContent = section.content.trim();
      if (!trimmedContent) return false;
      const wordCount = trimmedContent.split(/\s+/).filter(word => word.length > 0).length;
      const hasContent = wordCount > 5;

      console.log(`📝 Export: Section "${section.title}" (${section.type}) - ${wordCount} words, included: ${hasContent}`);
      if (hasContent) {
        console.log(`📄 Export: Section content preview: "${trimmedContent.substring(0, 100)}..."`);
      }
      return hasContent;
    });

    console.log(`✅ Export: Found ${sectionsWithContent.length} sections with substantial content`);

    if (sectionsWithContent.length === 0) {
      console.log('⚠️ Export: No sections with content found');
      return {
        title: paper.metadata.title || 'Untitled Paper',
        content: '',
        wordCount: 0,
        sectionCount: 0
      };
    }

    let combinedContent = '';
    let totalWordCount = 0;

    // Extract title from the first section if it's a title section, otherwise use metadata
    let title = paper.metadata.title || 'Untitled Paper';
    let titleSection = sectionsWithContent.find(s => s.title.toLowerCase() === 'title');

    if (titleSection) {
      // Extract clean title from content, removing markdown formatting
      const titleContent = titleSection.content.trim();
      const titleMatch = titleContent.match(/(?:#+\s*)?(?:Research Paper Title:\s*)?(?:\*\*)?(.+?)(?:\*\*)?$/m);
      if (titleMatch) {
        title = titleMatch[1].trim();
      }
    }

    console.log(`📄 Export: Using title "${title}"`);

    // Add main title
    combinedContent += `# ${title}\n\n`;

    // Add metadata section if available
    const hasMetadata = (paper.metadata.authors && paper.metadata.authors.length > 0) ||
                       paper.metadata.researchField ||
                       (paper.metadata.keywords && paper.metadata.keywords.length > 0);

    if (hasMetadata) {
      if (paper.metadata.authors && paper.metadata.authors.length > 0) {
        combinedContent += `**Authors: <AUTHORS>
      }

      if (paper.metadata.researchField) {
        combinedContent += `**Research Field:** ${paper.metadata.researchField}\n\n`;
      }

      if (paper.metadata.keywords && paper.metadata.keywords.length > 0) {
        combinedContent += `**Keywords:** ${paper.metadata.keywords.join(', ')}\n\n`;
      }

      combinedContent += '---\n\n';
    }

    // Sort sections by their order in SECTION_TYPES, excluding title since we already processed it
    const contentSections = sectionsWithContent
      .filter(s => s.title.toLowerCase() !== 'title')
      .sort((a, b) => {
        const aType = SECTION_TYPES.find(t => t.id === a.type);
        const bType = SECTION_TYPES.find(t => t.id === b.type);
        return (aType?.order || 0) - (bType?.order || 0);
      });

    // Process each section
    contentSections.forEach((section, index) => {
      console.log(`🔧 Export: Processing section "${section.title}" (${section.type})`);

      // Get raw section content
      let sectionContent = section.content.trim();

      // Clean section content to remove duplicate titles and improve formatting
      sectionContent = sectionContent.replace(/^#+\s*Research Paper Title:.*?\n\n?/gm, '');
      sectionContent = sectionContent.replace(/^#+\s*\*\*.*?\*\*\s*\n\n?/gm, '');

      // Clean up section headers that might be duplicated
      const sectionTitleRegex = new RegExp(`^#+\\s*${section.title}\\s*\n\n?`, 'gm');
      sectionContent = sectionContent.replace(sectionTitleRegex, '');

      // Add section title as H2 (unless content already starts with a heading)
      if (!sectionContent.match(/^#+\s/m)) {
        combinedContent += `## ${section.title}\n\n`;
      }

      // Add the section content directly (preserve existing formatting)
      combinedContent += `${sectionContent}\n\n`;

      // Add extra spacing between sections
      if (index < contentSections.length - 1) {
        combinedContent += '\n';
      }

      // Count words more accurately (excluding markdown formatting)
      const cleanContent = sectionContent
        .replace(/\[.*?\]/g, '') // Remove citations
        .replace(/#+\s*/g, '') // Remove heading markers
        .replace(/\*\*(.+?)\*\*/g, '$1') // Remove bold formatting
        .replace(/\*(.+?)\*/g, '$1') // Remove italic formatting
        .replace(/`(.+?)`/g, '$1') // Remove code formatting
        .trim();

      if (cleanContent) {
        const wordCount = cleanContent.split(/\s+/).filter(w => w.length > 0).length;
        totalWordCount += wordCount;
        console.log(`📊 Export: Section "${section.title}" contributed ${wordCount} words`);
      }
    });

    // Add references section if there are citations
    const allCitations = paper.sections.flatMap(s => s.citations || []);
    if (allCitations.length > 0) {
      console.log(`📚 Export: Adding ${allCitations.length} citations to references`);
      combinedContent += '\n## References\n\n';

      // Group citations by source to avoid duplicates
      const uniqueCitations = allCitations.reduce((acc, citation) => {
        if (!acc.find(c => c.source === citation.source)) {
          acc.push(citation);
        }
        return acc;
      }, [] as typeof allCitations);

      uniqueCitations.forEach((citation, index) => {
        combinedContent += `${index + 1}. ${citation.source}`;
        if (citation.url) {
          combinedContent += ` Available at: ${citation.url}`;
        }
        combinedContent += '\n\n';
      });
    }

    const result = {
      title,
      content: combinedContent,
      wordCount: totalWordCount,
      sectionCount: sectionsWithContent.length
    };

    console.log('✅ Export: Paper combination completed', {
      title: result.title,
      wordCount: result.wordCount,
      sectionCount: result.sectionCount,
      contentLength: result.content.length,
      hasReferences: allCitations.length > 0
    });

    return result;
  }

  /**
   * Export paper to Word format with enhanced logging and persistence
   */
  static async exportToWord(paper: Paper): Promise<void> {
    try {
      console.log('🔄 Export: Starting Word export for paper:', paper.metadata.title);

      const combinedPaper = this.combinePaperSections(paper);

      if (combinedPaper.sectionCount === 0) {
        console.warn('⚠️ Export: No sections with substantial content found');
        toast.error('No sections with substantial content found to export');
        return;
      }

      const fileName = `${combinedPaper.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}`;

      console.log('📄 Export: Exporting to Word with:', {
        title: combinedPaper.title,
        contentLength: combinedPaper.content.length,
        wordCount: combinedPaper.wordCount,
        sectionCount: combinedPaper.sectionCount,
        fileName
      });

      await documentExportService.exportMarkdownToDocx(
        combinedPaper.title,
        combinedPaper.content,
        fileName
      );

      // Save export event to auto-saves for tracking
      if (paper.metadata.id) {
        try {
          const { enhancedPersistenceService } = await import('./enhanced-persistence.service');
          await enhancedPersistenceService.autoSavePaper(
            paper.metadata.id,
            paper,
            'export',
            `Word export: ${fileName}.docx`
          );
        } catch (persistError) {
          console.warn('⚠️ Export: Failed to save export event:', persistError);
        }
      }

      toast.success(`Word document exported successfully! (${combinedPaper.sectionCount} sections, ${combinedPaper.wordCount} words)`);
      console.log('✅ Export: Word export completed successfully');
    } catch (error) {
      console.error('❌ Export: Word export failed:', error);
      toast.error('Failed to export Word document. Please try again.');
      throw error;
    }
  }

  /**
   * Export paper to PDF format with enhanced logging and persistence
   */
  static async exportToPDF(paper: Paper): Promise<void> {
    try {
      console.log('🔄 Export: Starting PDF export for paper:', paper.metadata.title);

      const combinedPaper = this.combinePaperSections(paper);

      if (combinedPaper.sectionCount === 0) {
        console.warn('⚠️ Export: No sections with substantial content found');
        toast.error('No sections with substantial content found to export');
        return;
      }

      const fileName = `${combinedPaper.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}`;

      console.log('📄 Export: Exporting to PDF with:', {
        title: combinedPaper.title,
        contentLength: combinedPaper.content.length,
        wordCount: combinedPaper.wordCount,
        sectionCount: combinedPaper.sectionCount,
        fileName
      });

      await documentExportService.exportMarkdownToPdf(
        combinedPaper.title,
        combinedPaper.content,
        fileName
      );

      // Save export event to auto-saves for tracking
      if (paper.metadata.id) {
        try {
          const { enhancedPersistenceService } = await import('./enhanced-persistence.service');
          await enhancedPersistenceService.autoSavePaper(
            paper.metadata.id,
            paper,
            'export',
            `PDF export: ${fileName}.pdf`
          );
        } catch (persistError) {
          console.warn('⚠️ Export: Failed to save export event:', persistError);
        }
      }

      toast.success(`PDF document exported successfully! (${combinedPaper.sectionCount} sections, ${combinedPaper.wordCount} words)`);
      console.log('✅ Export: PDF export completed successfully');
    } catch (error) {
      console.error('❌ Export: PDF export failed:', error);
      toast.error('Failed to export PDF document. Please try again.');
      throw error;
    }
  }

  /**
   * Export paper to LaTeX format (if needed in the future)
   */
  static async exportToLatex(paper: Paper): Promise<void> {
    try {
      const combinedPaper = this.combinePaperSections(paper);
      
      if (combinedPaper.sectionCount === 0) {
        toast.error('No sections with substantial content found to export');
        return;
      }

      // Convert markdown to LaTeX format
      let latexContent = combinedPaper.content;
      
      // Basic markdown to LaTeX conversion
      latexContent = latexContent.replace(/^# (.+)$/gm, '\\title{$1}');
      latexContent = latexContent.replace(/^## (.+)$/gm, '\\section{$1}');
      latexContent = latexContent.replace(/^### (.+)$/gm, '\\subsection{$1}');
      latexContent = latexContent.replace(/\*\*(.+?)\*\*/g, '\\textbf{$1}');
      latexContent = latexContent.replace(/\*(.+?)\*/g, '\\textit{$1}');

      // Create LaTeX document structure
      const fullLatexContent = `\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}

\\begin{document}

${latexContent}

\\end{document}`;

      // Download as .tex file
      const blob = new Blob([fullLatexContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${combinedPaper.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.tex`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success(`LaTeX file exported successfully! (${combinedPaper.sectionCount} sections, ${combinedPaper.wordCount} words)`);
    } catch (error) {
      console.error('LaTeX export failed:', error);
      toast.error('Failed to export LaTeX file. Please try again.');
      throw error;
    }
  }

  /**
   * Get export statistics for display
   */
  static getExportStats(paper: Paper): { sectionsReady: number; totalWords: number; completeness: number } {
    const sectionsWithContent = paper.sections.filter(section => {
      const trimmedContent = section.content.trim();
      if (!trimmedContent) return false;
      const wordCount = trimmedContent.split(/\s+/).filter(word => word.length > 0).length;
      return wordCount > 5;
    });

    const totalWords = sectionsWithContent.reduce((sum, section) => {
      const trimmedContent = section.content.trim();
      if (!trimmedContent) return sum;
      const wordCount = trimmedContent.split(/\s+/).filter(word => word.length > 0).length;
      return sum + wordCount;
    }, 0);

    const completeness = paper.sections.length > 0 ? (sectionsWithContent.length / paper.sections.length) * 100 : 0;

    return {
      sectionsReady: sectionsWithContent.length,
      totalWords,
      completeness: Math.round(completeness)
    };
  }
}
