-- Citation Import Sessions Table
-- Stores information about citation import sessions for persistence across browser refreshes

CREATE TABLE IF NOT EXISTS public.citation_import_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    library_id UUID REFERENCES public.research_libraries(id) ON DELETE CASCADE NOT NULL,
    
    -- Session information
    session_name TEXT NOT NULL,
    import_date TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Citation data (stored as JSONB for flexibility)
    citations JSONB NOT NULL DEFAULT '[]',
    
    -- Progress tracking
    total_citations INTEGER NOT NULL DEFAULT 0,
    successful_imports INTEGER NOT NULL DEFAULT 0,
    failed_imports INTEGER NOT NULL DEFAULT 0,
    
    -- Status tracking
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IF NOT EXISTS idx_citation_import_sessions_user_id ON public.citation_import_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_citation_import_sessions_library_id ON public.citation_import_sessions(library_id);
CREATE INDEX IF NOT EXISTS idx_citation_import_sessions_status ON public.citation_import_sessions(status);
CREATE INDEX IF NOT EXISTS idx_citation_import_sessions_import_date ON public.citation_import_sessions(import_date DESC);

-- Enable RLS
ALTER TABLE public.citation_import_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own import sessions" ON public.citation_import_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own import sessions" ON public.citation_import_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own import sessions" ON public.citation_import_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own import sessions" ON public.citation_import_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_citation_import_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_citation_import_sessions_updated_at
    BEFORE UPDATE ON public.citation_import_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_citation_import_sessions_updated_at();

-- Grant permissions
GRANT ALL ON public.citation_import_sessions TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
