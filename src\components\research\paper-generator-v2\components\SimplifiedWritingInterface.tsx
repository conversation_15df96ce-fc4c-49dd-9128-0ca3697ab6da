import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Save, 
  Download, 
  Eye, 
  Edit3, 
  Sparkles, 
  FileText, 
  ChevronDown, 
  ChevronRight,
  Plus,
  BookOpen,
  Zap,
  Settings,
  Layout
} from 'lucide-react';
import { UserArticlesService, UserArticle, UserArticleSection } from '../services/user-articles.service';

interface SimplifiedWritingInterfaceProps {
  articleId: string;
  onBack: () => void;
}

export const SimplifiedWritingInterface: React.FC<SimplifiedWritingInterfaceProps> = ({
  articleId,
  onBack
}) => {
  const [article, setArticle] = useState<UserArticle | null>(null);
  const [sections, setSections] = useState<UserArticleSection[]>([]);
  const [currentSection, setCurrentSection] = useState<string>('introduction');
  const [viewMode, setViewMode] = useState<'section' | 'unified'>('section');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [autoSave, setAutoSave] = useState(true);

  const articlesService = UserArticlesService.getInstance();

  useEffect(() => {
    loadArticle();
  }, [articleId]);

  const loadArticle = async () => {
    try {
      const data = await articlesService.getArticleById(articleId);
      setArticle(data.article);
      setSections(data.sections);
    } catch (error) {
      console.error('Failed to load article:', error);
    }
  };

  const getCurrentSection = () => {
    return sections.find(s => s.section_type === currentSection);
  };

  const getCompletedSections = () => {
    return sections.filter(s => s.status === 'completed' && s.content && s.content.trim().length > 0);
  };

  const handleSectionChange = (sectionType: string) => {
    setCurrentSection(sectionType);
  };

  const handleGenerateContent = async (prompt: string, useAI: boolean = true) => {
    setIsGenerating(true);
    try {
      // Implementation for AI content generation
      console.log('Generating content for:', currentSection, 'with prompt:', prompt);
      // This would integrate with the existing AI service
    } catch (error) {
      console.error('Failed to generate content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExport = async (format: 'word' | 'pdf') => {
    try {
      console.log('Exporting article as:', format);
      // Implementation for export functionality
    } catch (error) {
      console.error('Failed to export:', error);
    }
  };

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading article...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Clean Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 truncate max-w-md">
                  {article.title}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>{article.total_words} words</span>
                  <span>{article.completed_sections}/{article.total_sections} sections</span>
                  <span className="text-blue-600">{article.progress_percentage}% complete</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('section')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'section' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Edit3 className="h-4 w-4 mr-1 inline" />
                  Section
                </button>
                <button
                  onClick={() => setViewMode('unified')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'unified' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Layout className="h-4 w-4 mr-1 inline" />
                  Unified
                </button>
              </div>

              {/* Export */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleExport('word')}
                  className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Download className="h-4 w-4 mr-1 inline" />
                  Export
                </button>
              </div>

              {/* Auto-save indicator */}
              {autoSave && (
                <div className="flex items-center text-sm text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Auto-saved
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {viewMode === 'section' ? (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Section Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sticky top-24">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Sections</h3>
                <nav className="space-y-1">
                  {sections.map((section) => (
                    <button
                      key={section.section_type}
                      onClick={() => handleSectionChange(section.section_type)}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                        currentSection === section.section_type
                          ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{section.title}</span>
                        <div className="flex items-center space-x-1">
                          {section.status === 'completed' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          )}
                          {section.word_count > 0 && (
                            <span className="text-xs text-gray-400">{section.word_count}w</span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3">
              <SectionEditor
                section={getCurrentSection()}
                onGenerate={handleGenerateContent}
                isGenerating={isGenerating}
                showAdvanced={showAdvanced}
                onToggleAdvanced={() => setShowAdvanced(!showAdvanced)}
              />
            </div>
          </div>
        ) : (
          /* Unified View */
          <div className="max-w-4xl mx-auto">
            <UnifiedArticleView
              sections={getCompletedSections()}
              article={article}
              onExport={handleExport}
            />
          </div>
        )}
      </div>
    </div>
  );
};

// Section Editor Component
interface SectionEditorProps {
  section?: UserArticleSection;
  onGenerate: (prompt: string, useAI: boolean) => void;
  isGenerating: boolean;
  showAdvanced: boolean;
  onToggleAdvanced: () => void;
}

const SectionEditor: React.FC<SectionEditorProps> = ({
  section,
  onGenerate,
  isGenerating,
  showAdvanced,
  onToggleAdvanced
}) => {
  const [content, setContent] = useState(section?.content || '');
  const [prompt, setPrompt] = useState('');

  useEffect(() => {
    setContent(section?.content || '');
  }, [section]);

  if (!section) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Section</h3>
        <p className="text-gray-500">Choose a section from the sidebar to start writing.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">{section.title}</h2>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              section.status === 'completed' ? 'bg-green-100 text-green-800' :
              section.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {section.status}
            </span>
            {section.word_count > 0 && (
              <span className="text-sm text-gray-500">{section.word_count} words</span>
            )}
          </div>
        </div>

        {/* Quick Generate */}
        <div className="space-y-3">
          <div className="flex space-x-3">
            <input
              type="text"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={`What should this ${section.title.toLowerCase()} cover?`}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={() => onGenerate(prompt, true)}
              disabled={isGenerating || !prompt.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {isGenerating ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              Generate
            </button>
          </div>

          {/* Advanced Options Toggle */}
          <button
            onClick={onToggleAdvanced}
            className="text-sm text-gray-600 hover:text-gray-900 flex items-center"
          >
            {showAdvanced ? <ChevronDown className="h-4 w-4 mr-1" /> : <ChevronRight className="h-4 w-4 mr-1" />}
            Advanced options
          </button>

          {showAdvanced && (
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <button className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <Zap className="h-4 w-4 mr-1 inline" />
                  With Sources
                </button>
                <button className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <BookOpen className="h-4 w-4 mr-1 inline" />
                  Deep Research
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content Editor */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">Content</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>{content.length} characters</span>
              <span>•</span>
              <span>{content.split(' ').filter(w => w.length > 0).length} words</span>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={`Start writing your ${section.title.toLowerCase()}...`}
            className="w-full h-96 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>
      </div>

      {/* Inline References */}
      {section.content && section.content.includes('[') && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">References in this section</h3>
          <div className="space-y-2">
            <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
              <p className="font-medium">Sample Reference</p>
              <p className="text-xs text-gray-500">This would show extracted references from the content</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Unified Article View Component
interface UnifiedArticleViewProps {
  sections: UserArticleSection[];
  article: UserArticle;
  onExport: (format: 'word' | 'pdf') => void;
}

const UnifiedArticleView: React.FC<UnifiedArticleViewProps> = ({
  sections,
  article,
  onExport
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="border-b border-gray-200 px-8 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{article.title}</h1>
            <p className="text-gray-600 mt-1">{article.research_field}</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => onExport('word')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="h-4 w-4 mr-2 inline" />
              Export Word
            </button>
            <button
              onClick={() => onExport('pdf')}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Download className="h-4 w-4 mr-2 inline" />
              Export PDF
            </button>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="px-8 py-6 prose prose-lg max-w-none">
        {sections.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content yet</h3>
            <p className="text-gray-500">Start writing sections to see your unified article here.</p>
          </div>
        ) : (
          sections.map((section) => (
            <div key={section.id} className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                {section.title}
              </h2>
              <div 
                className="prose prose-gray max-w-none"
                dangerouslySetInnerHTML={{ __html: section.content || '' }}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
};
