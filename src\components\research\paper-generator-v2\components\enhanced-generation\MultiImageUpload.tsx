import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  Image as ImageIcon,
  X,
  Eye,
  Sparkles,
  RefreshCw,
  Plus,
  Grid3X3,
  List,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { toast } from 'sonner';

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  base64: string;
  title: string;
  caption: string;
  analysis?: string;
  isAnalyzing: boolean;
}

interface MultiImageUploadProps {
  images: UploadedImage[];
  onImagesChange: (images: UploadedImage[]) => void;
  onAnalyzeImage?: (base64: string, prompt?: string) => Promise<string>;
  maxImages?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
  selectedModel?: string;
}

export function MultiImageUpload({
  images,
  onImagesChange,
  onAnalyzeImage,
  maxImages = 5,
  maxFileSize = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  className = "",
  selectedModel
}: MultiImageUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isExpanded, setIsExpanded] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Convert file to base64
  const fileToBase64 = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]); // Remove data:image/...;base64, prefix
      };
      reader.onerror = error => reject(error);
    });
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList) => {
    const validFiles: File[] = [];
    
    // Validate files
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!acceptedTypes.includes(file.type)) {
        toast.error(`File type ${file.type} is not supported`);
        continue;
      }

      if (file.size > maxFileSize * 1024 * 1024) {
        toast.error(`File ${file.name} is too large (max ${maxFileSize}MB)`);
        continue;
      }

      if (images.length + validFiles.length >= maxImages) {
        toast.error(`Maximum ${maxImages} images allowed`);
        break;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    try {
      setUploadProgress(10);
      const newImages: UploadedImage[] = [];

      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        setUploadProgress(10 + (i / validFiles.length) * 80);

        // Create preview URL
        const url = URL.createObjectURL(file);
        
        // Convert to base64
        const base64 = await fileToBase64(file);
        
        // Create image object
        const newImage: UploadedImage = {
          id: `img_${Date.now()}_${i}`,
          file,
          url,
          base64,
          title: file.name.replace(/\.[^/.]+$/, "").replace(/[-_]/g, ' '),
          caption: '',
          isAnalyzing: false
        };

        newImages.push(newImage);
      }

      setUploadProgress(100);
      onImagesChange([...images, ...newImages]);
      toast.success(`${newImages.length} image(s) uploaded successfully!`);
      
      // Reset progress after delay
      setTimeout(() => setUploadProgress(0), 1000);
      
    } catch (error) {
      console.error('File upload error:', error);
      toast.error('Failed to upload images');
      setUploadProgress(0);
    }
  }, [images, onImagesChange, acceptedTypes, maxFileSize, maxImages, fileToBase64]);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect]);

  // Handle file input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  }, [handleFileSelect]);

  // Remove image
  const removeImage = useCallback((imageId: string) => {
    const updatedImages = images.filter(img => img.id !== imageId);
    onImagesChange(updatedImages);
    toast.success('Image removed');
  }, [images, onImagesChange]);

  // Update image metadata
  const updateImage = useCallback((imageId: string, updates: Partial<UploadedImage>) => {
    const updatedImages = images.map(img => 
      img.id === imageId ? { ...img, ...updates } : img
    );
    onImagesChange(updatedImages);
  }, [images, onImagesChange]);

  // Analyze single image
  const analyzeImage = useCallback(async (imageId: string) => {
    if (!onAnalyzeImage) return;

    const image = images.find(img => img.id === imageId);
    if (!image) return;

    updateImage(imageId, { isAnalyzing: true });

    try {
      const prompt = "Analyze this image and provide a detailed academic description suitable for a research paper. Include what the image shows, its key features, and its potential significance.";

      // Ensure base64 data has proper format for AI analysis
      const base64Data = image.base64.startsWith('data:') ? image.base64 : `data:image/jpeg;base64,${image.base64}`;
      const analysis = await onAnalyzeImage(base64Data, prompt);

      updateImage(imageId, {
        analysis,
        isAnalyzing: false,
        caption: analysis.split('.')[0] + '.' // Auto-generate caption from first sentence
      });

      toast.success('Image analysis completed!');
    } catch (error) {
      console.error('Analysis error:', error);
      updateImage(imageId, { isAnalyzing: false });
      toast.error('Failed to analyze image');
    }
  }, [images, onAnalyzeImage, updateImage]);

  // Analyze all images
  const analyzeAllImages = useCallback(async () => {
    if (!onAnalyzeImage) return;

    const unanalyzedImages = images.filter(img => !img.analysis && !img.isAnalyzing);
    if (unanalyzedImages.length === 0) {
      toast.info('All images are already analyzed');
      return;
    }

    for (const image of unanalyzedImages) {
      await analyzeImage(image.id);
    }
  }, [images, analyzeImage]);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base">
            <ImageIcon className="h-4 w-4" />
            Images ({images.length}/{maxImages})
            {selectedModel && (
              <Badge variant="outline" className="text-xs ml-2">
                {selectedModel}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {images.length > 0 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="h-6 w-6 p-0"
                >
                  {viewMode === 'grid' ? <List className="h-3 w-3" /> : <Grid3X3 className="h-3 w-3" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-6 w-6 p-0"
                >
                  {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Upload Area */}
          {images.length < maxImages && (
            <div
              className={`
                relative border-2 border-dashed rounded-lg p-4 text-center transition-colors
                ${dragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
                }
              `}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept={acceptedTypes.join(',')}
                onChange={handleInputChange}
                multiple
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              
              <div className="space-y-2">
                <Upload className="h-6 w-6 text-gray-600 mx-auto" />
                <p className="text-sm font-medium text-gray-900">
                  Drop images here or click to browse
                </p>
                <p className="text-xs text-gray-500">
                  {maxImages - images.length} more images allowed • Max {maxFileSize}MB each
                </p>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {uploadProgress > 0 && uploadProgress < 100 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading images...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {/* Images Display */}
          {images.length > 0 && (
            <div className="space-y-4">
              {/* Batch Actions */}
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploaded Images</span>
                {onAnalyzeImage && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={analyzeAllImages}
                    className="flex items-center gap-1"
                  >
                    <Sparkles className="h-3 w-3" />
                    Analyze All
                  </Button>
                )}
              </div>

              {/* Images Grid/List */}
              <div className={`
                ${viewMode === 'grid' 
                  ? 'grid grid-cols-2 gap-3' 
                  : 'space-y-3'
                }
              `}>
                {images.map((image) => (
                  <div
                    key={image.id}
                    className="border rounded-lg p-3 bg-white"
                  >
                    {/* Image Preview */}
                    <div className="relative mb-2">
                      <img
                        src={image.url}
                        alt={image.title}
                        className={`
                          w-full object-cover rounded border
                          ${viewMode === 'grid' ? 'h-24' : 'h-32'}
                        `}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeImage(image.id)}
                        className="absolute top-1 right-1 h-6 w-6 p-0 bg-white/80 hover:bg-white"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Image Metadata */}
                    <div className="space-y-2">
                      <Input
                        value={image.title}
                        onChange={(e) => updateImage(image.id, { title: e.target.value })}
                        placeholder="Image title..."
                        className="text-xs h-7"
                      />
                      
                      {image.analysis && (
                        <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded border">
                          {image.analysis.substring(0, 100)}...
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-1">
                        {onAnalyzeImage && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => analyzeImage(image.id)}
                            disabled={image.isAnalyzing}
                            className="flex-1 h-7 text-xs"
                          >
                            {image.isAnalyzing ? (
                              <RefreshCw className="h-3 w-3 animate-spin" />
                            ) : (
                              <Sparkles className="h-3 w-3" />
                            )}
                            {image.isAnalyzing ? 'Analyzing...' : 'Analyze'}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
