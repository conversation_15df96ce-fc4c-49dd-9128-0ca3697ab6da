/**
 * Citation Enhancement Service
 * AI-powered enhancement service to clean, standardize, and enrich imported citation data
 */

import { 
  ProcessedCitationData, 
  CitationEnhancementResult,
  QualityAssessment,
  QualityIssue,
  QualitySuggestion,
  CitationImportServiceResponse 
} from '../types/citation-import.types';

// Import existing AI services
import { researchAIService } from '../../research-search/services/research-ai.service';
import { enhancedAIService } from '../../paper-generator/enhanced-ai.service';

export interface EnhancementOptions {
  aiModel: string;
  enableDataCleaning: boolean;
  enableKeywordExtraction: boolean;
  enableTagSuggestion: boolean;
  enableDomainDetection: boolean;
  enableAuthorStandardization: boolean;
  enableTitleCleaning: boolean;
  batchSize: number;
}

export class CitationEnhancementService {
  private readonly defaultModel = 'google/gemini-2.5-flash';

  /**
   * Enhance a batch of citations with AI processing
   */
  async enhanceCitations(
    citations: ProcessedCitationData[],
    options: EnhancementOptions
  ): Promise<CitationImportServiceResponse<CitationEnhancementResult[]>> {
    try {
      const results: CitationEnhancementResult[] = [];
      
      // Process citations in batches
      for (let i = 0; i < citations.length; i += options.batchSize) {
        const batch = citations.slice(i, i + options.batchSize);
        const batchResults = await this.processBatch(batch, options);
        results.push(...batchResults);
      }
      
      return {
        success: true,
        data: results,
        error: null,
        warnings: this.generateEnhancementWarnings(results)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `Citation enhancement failed: ${error.message}`
      };
    }
  }

  /**
   * Process a batch of citations
   */
  private async processBatch(
    citations: ProcessedCitationData[],
    options: EnhancementOptions
  ): Promise<CitationEnhancementResult[]> {
    const results: CitationEnhancementResult[] = [];
    
    for (const citation of citations) {
      try {
        const enhancementResult = await this.enhanceSingleCitation(citation, options);
        results.push(enhancementResult);
      } catch (error) {
        console.error('Failed to enhance citation:', citation.title, error);
        // Create a fallback result
        results.push(this.createFallbackResult(citation, options.aiModel));
      }
    }
    
    return results;
  }

  /**
   * Enhance a single citation
   */
  private async enhanceSingleCitation(
    citation: ProcessedCitationData,
    options: EnhancementOptions
  ): Promise<CitationEnhancementResult> {
    const enhancedData = { ...citation };
    const enhancementNotes: string[] = [];
    
    // Data cleaning
    if (options.enableDataCleaning) {
      await this.cleanCitationData(enhancedData, enhancementNotes);
    }
    
    // Author name standardization
    if (options.enableAuthorStandardization) {
      await this.standardizeAuthorNames(enhancedData, enhancementNotes);
    }
    
    // Title cleaning
    if (options.enableTitleCleaning) {
      await this.cleanTitle(enhancedData, enhancementNotes);
    }
    
    // AI-powered enhancements
    const aiEnhancements = await this.performAIEnhancements(citation, options);
    
    // Merge AI enhancements
    if (options.enableKeywordExtraction && aiEnhancements.extractedKeywords) {
      enhancedData.keywords = [...new Set([...enhancedData.keywords, ...aiEnhancements.extractedKeywords])];
      enhancementNotes.push('Added AI-extracted keywords');
    }
    
    if (options.enableTagSuggestion && aiEnhancements.suggestedTags) {
      enhancedData.tags = [...new Set([...enhancedData.tags, ...aiEnhancements.suggestedTags])];
      enhancementNotes.push('Added AI-suggested tags');
    }
    
    // Quality assessment
    const dataQuality = this.assessDataQuality(enhancedData);
    
    return {
      enhancedData,
      extractedKeywords: aiEnhancements.extractedKeywords || [],
      suggestedTags: aiEnhancements.suggestedTags || [],
      researchDomain: aiEnhancements.researchDomain,
      methodologyType: aiEnhancements.methodologyType,
      dataQuality,
      aiModel: options.aiModel,
      enhancementTimestamp: new Date(),
      enhancementConfidence: this.calculateEnhancementConfidence(enhancedData, aiEnhancements),
      enhancementNotes
    };
  }

  /**
   * Clean citation data
   */
  private async cleanCitationData(citation: ProcessedCitationData, notes: string[]): Promise<void> {
    // Clean title
    if (citation.title) {
      const originalTitle = citation.title;
      citation.title = this.cleanText(citation.title);
      if (originalTitle !== citation.title) {
        notes.push('Cleaned title formatting');
      }
    }
    
    // Clean abstract
    if (citation.abstract) {
      const originalAbstract = citation.abstract;
      citation.abstract = this.cleanText(citation.abstract);
      if (originalAbstract !== citation.abstract) {
        notes.push('Cleaned abstract formatting');
      }
    }
    
    // Clean journal name
    if (citation.journal) {
      const originalJournal = citation.journal;
      citation.journal = this.cleanText(citation.journal);
      if (originalJournal !== citation.journal) {
        notes.push('Cleaned journal name');
      }
    }
  }

  /**
   * Clean text by removing formatting artifacts
   */
  private cleanText(text: string): string {
    return text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove LaTeX commands
      .replace(/\\[a-zA-Z]+\{([^}]*)\}/g, '$1')
      .replace(/\\[a-zA-Z]+/g, '')
      // Remove HTML entities
      .replace(/&[a-zA-Z]+;/g, ' ')
      // Clean up punctuation
      .replace(/\s+([.,;:])/g, '$1')
      .trim();
  }

  /**
   * Standardize author names
   */
  private async standardizeAuthorNames(citation: ProcessedCitationData, notes: string[]): Promise<void> {
    const standardizedAuthors = citation.authors.map(author => {
      // Convert "Last, First" to "First Last"
      if (author.includes(',')) {
        const parts = author.split(',').map(p => p.trim());
        if (parts.length >= 2) {
          return `${parts[1]} ${parts[0]}`.trim();
        }
      }
      return author.trim();
    });
    
    if (JSON.stringify(standardizedAuthors) !== JSON.stringify(citation.authors)) {
      citation.authors = standardizedAuthors;
      notes.push('Standardized author name formats');
    }
  }

  /**
   * Clean title using AI
   */
  private async cleanTitle(citation: ProcessedCitationData, notes: string[]): Promise<void> {
    if (!citation.title) return;
    
    try {
      const cleaningPrompt = `
Clean and standardize this academic paper title by removing formatting artifacts, fixing capitalization, and ensuring proper academic style:

Title: "${citation.title}"

Return only the cleaned title, nothing else.
`;
      
      const cleanedTitle = await this.callAI(cleaningPrompt, this.defaultModel);
      
      if (cleanedTitle && cleanedTitle !== citation.title) {
        citation.title = cleanedTitle.trim();
        notes.push('AI-cleaned title');
      }
    } catch (error) {
      console.warn('Failed to clean title with AI:', error);
    }
  }

  /**
   * Perform AI-powered enhancements
   */
  private async performAIEnhancements(
    citation: ProcessedCitationData,
    options: EnhancementOptions
  ): Promise<{
    extractedKeywords?: string[];
    suggestedTags?: string[];
    researchDomain?: string;
    methodologyType?: string;
  }> {
    const enhancements: any = {};
    
    try {
      const enhancementPrompt = this.buildEnhancementPrompt(citation, options);
      const response = await this.callAI(enhancementPrompt, options.aiModel);
      
      // Parse AI response
      const parsed = this.parseAIEnhancementResponse(response);
      return parsed;
    } catch (error) {
      console.warn('AI enhancement failed:', error);
      return {};
    }
  }

  /**
   * Build enhancement prompt for AI
   */
  private buildEnhancementPrompt(citation: ProcessedCitationData, options: EnhancementOptions): string {
    let prompt = `
Analyze this academic citation and provide enhancements:

Title: ${citation.title}
Authors: <AUTHORS>
Journal: ${citation.journal || 'N/A'}
Year: ${citation.publicationYear || 'N/A'}
Abstract: ${citation.abstract || 'N/A'}
Existing Keywords: ${citation.keywords.join(', ') || 'None'}

Please provide:
`;

    if (options.enableKeywordExtraction) {
      prompt += `
1. KEYWORDS: Extract 5-10 relevant academic keywords from the title and abstract
`;
    }

    if (options.enableTagSuggestion) {
      prompt += `
2. TAGS: Suggest 3-5 research tags for categorization
`;
    }

    if (options.enableDomainDetection) {
      prompt += `
3. DOMAIN: Identify the primary research domain/field
4. METHODOLOGY: Identify the research methodology type (empirical, theoretical, review, etc.)
`;
    }

    prompt += `
Format your response as JSON:
{
  "keywords": ["keyword1", "keyword2", ...],
  "tags": ["tag1", "tag2", ...],
  "domain": "research domain",
  "methodology": "methodology type"
}
`;

    return prompt;
  }

  /**
   * Parse AI enhancement response
   */
  private parseAIEnhancementResponse(response: string): {
    extractedKeywords?: string[];
    suggestedTags?: string[];
    researchDomain?: string;
    methodologyType?: string;
  } {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          extractedKeywords: parsed.keywords || [],
          suggestedTags: parsed.tags || [],
          researchDomain: parsed.domain,
          methodologyType: parsed.methodology
        };
      }
    } catch (error) {
      console.warn('Failed to parse AI response as JSON:', error);
    }
    
    // Fallback: try to extract information from text
    return this.extractFromText(response);
  }

  /**
   * Extract information from text response
   */
  private extractFromText(text: string): {
    extractedKeywords?: string[];
    suggestedTags?: string[];
    researchDomain?: string;
    methodologyType?: string;
  } {
    const result: any = {};
    
    // Extract keywords
    const keywordMatch = text.match(/keywords?:?\s*(.+)/i);
    if (keywordMatch) {
      result.extractedKeywords = keywordMatch[1]
        .split(/[,;]/)
        .map(k => k.trim())
        .filter(k => k.length > 0);
    }
    
    // Extract tags
    const tagMatch = text.match(/tags?:?\s*(.+)/i);
    if (tagMatch) {
      result.suggestedTags = tagMatch[1]
        .split(/[,;]/)
        .map(t => t.trim())
        .filter(t => t.length > 0);
    }
    
    return result;
  }

  /**
   * Call AI service
   */
  private async callAI(prompt: string, model: string): Promise<string> {
    try {
      // Use the existing research AI service
      const response = await researchAIService.generateSimpleResponse(prompt, model);
      return response;
    } catch (error) {
      // Fallback to enhanced AI service
      try {
        return await enhancedAIService.generateText(prompt, model);
      } catch (fallbackError) {
        throw new Error(`Both AI services failed: ${error}, ${fallbackError}`);
      }
    }
  }

  /**
   * Assess data quality
   */
  private assessDataQuality(citation: ProcessedCitationData): {
    completeness: number;
    accuracy: number;
    consistency: number;
    overall: number;
  } {
    let completeness = 0;
    let accuracy = 0;
    let consistency = 0;
    
    // Completeness assessment
    const requiredFields = ['title', 'authors', 'publicationYear'];
    const optionalFields = ['journal', 'doi', 'abstract', 'keywords'];
    
    const hasRequired = requiredFields.every(field => {
      const value = citation[field as keyof ProcessedCitationData];
      return value && (Array.isArray(value) ? value.length > 0 : true);
    });
    
    const optionalCount = optionalFields.filter(field => {
      const value = citation[field as keyof ProcessedCitationData];
      return value && (Array.isArray(value) ? value.length > 0 : true);
    }).length;
    
    completeness = hasRequired ? 0.6 + (optionalCount / optionalFields.length) * 0.4 : 0.3;
    
    // Accuracy assessment (basic validation)
    accuracy = 0.8; // Default, would need more sophisticated validation
    
    if (citation.doi && !citation.doi.match(/^10\.\d+/)) {
      accuracy -= 0.1; // Invalid DOI format
    }
    
    if (citation.publicationYear && (citation.publicationYear < 1800 || citation.publicationYear > new Date().getFullYear() + 1)) {
      accuracy -= 0.1; // Suspicious year
    }
    
    // Consistency assessment
    consistency = 0.9; // Default high consistency for processed data
    
    const overall = (completeness + accuracy + consistency) / 3;
    
    return { completeness, accuracy, consistency, overall };
  }

  /**
   * Calculate enhancement confidence
   */
  private calculateEnhancementConfidence(
    citation: ProcessedCitationData,
    aiEnhancements: any
  ): number {
    let confidence = citation.processingConfidence || 0.5;
    
    // Boost confidence based on AI enhancements
    if (aiEnhancements.extractedKeywords?.length > 0) confidence += 0.1;
    if (aiEnhancements.suggestedTags?.length > 0) confidence += 0.1;
    if (aiEnhancements.researchDomain) confidence += 0.1;
    if (aiEnhancements.methodologyType) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Create fallback result for failed enhancements
   */
  private createFallbackResult(citation: ProcessedCitationData, aiModel: string): CitationEnhancementResult {
    return {
      enhancedData: citation,
      extractedKeywords: [],
      suggestedTags: [],
      dataQuality: this.assessDataQuality(citation),
      aiModel,
      enhancementTimestamp: new Date(),
      enhancementConfidence: citation.processingConfidence || 0.5,
      enhancementNotes: ['Enhancement failed - using original data']
    };
  }

  /**
   * Generate enhancement warnings
   */
  private generateEnhancementWarnings(results: CitationEnhancementResult[]): string[] {
    const warnings: string[] = [];
    
    const lowQualityCount = results.filter(r => r.dataQuality.overall < 0.5).length;
    if (lowQualityCount > 0) {
      warnings.push(`${lowQualityCount} citations have low data quality scores`);
    }
    
    const lowConfidenceCount = results.filter(r => r.enhancementConfidence < 0.6).length;
    if (lowConfidenceCount > 0) {
      warnings.push(`${lowConfidenceCount} citations have low enhancement confidence`);
    }
    
    return warnings;
  }
}

export const citationEnhancementService = new CitationEnhancementService();
