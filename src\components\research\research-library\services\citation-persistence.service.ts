import { supabase } from '@/lib/supabase';
import { ProcessedCitationData } from './types/citation-import.types';

export interface ImportedCitationSession {
  id: string;
  user_id: string;
  library_id: string;
  session_name: string;
  citations: ProcessedCitationData[];
  import_date: string;
  total_citations: number;
  successful_imports: number;
  failed_imports: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  // New batch processing fields
  batch_size?: number;
  current_batch?: number;
  total_batches?: number;
  processing_errors?: any[];
  last_processed_at?: string;
}

export interface CitationImportBatch {
  id: string;
  session_id: string;
  batch_number: number;
  citations: ProcessedCitationData[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processed_count: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface CitationImportProgress {
  sessionId: string;
  current: number;
  total: number;
  status: string;
  errors: string[];
  // New batch progress fields
  currentBatch?: number;
  totalBatches?: number;
  batchProgress?: number;
}

export class CitationPersistenceService {
  private readonly TABLE_IMPORT_SESSIONS = 'citation_import_sessions';
  private readonly TABLE_IMPORT_BATCHES = 'citation_import_batches';
  private readonly TABLE_LIBRARY_ARTICLES = 'library_articles';
  private readonly DEFAULT_BATCH_SIZE = 25; // Reduced from 50 for better performance

  /**
   * Create a new import session with batch processing
   */
  async createImportSession(
    userId: string,
    libraryId: string,
    sessionName: string,
    citations: ProcessedCitationData[],
    batchSize: number = this.DEFAULT_BATCH_SIZE
  ): Promise<{ success: boolean; sessionId?: string; error?: string }> {
    try {
      // For large imports, don't store all citations in the main session
      const shouldUseBatches = citations.length > 100;
      const totalBatches = shouldUseBatches ? Math.ceil(citations.length / batchSize) : 0;

      const sessionData = {
        user_id: userId,
        library_id: libraryId,
        session_name: sessionName,
        citations: shouldUseBatches ? [] : citations, // Empty for large imports
        import_date: new Date().toISOString(),
        total_citations: citations.length,
        successful_imports: 0,
        failed_imports: 0,
        status: 'pending' as const,
        batch_size: shouldUseBatches ? batchSize : null,
        current_batch: 0,
        total_batches: totalBatches,
        processing_errors: []
      };

      const { data, error } = await supabase
        .from(this.TABLE_IMPORT_SESSIONS)
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        console.error('Failed to create import session:', error);
        return { success: false, error: error.message };
      }

      // Create batches for large imports
      if (shouldUseBatches) {
        const batchResult = await this.createCitationBatches(data.id, citations, batchSize);
        if (!batchResult.success) {
          // Clean up session if batch creation fails
          await this.deleteImportSession(data.id);
          return { success: false, error: batchResult.error };
        }
      }

      return { success: true, sessionId: data.id };
    } catch (error: any) {
      console.error('Error creating import session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create citation batches for large imports
   */
  private async createCitationBatches(
    sessionId: string,
    citations: ProcessedCitationData[],
    batchSize: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const batches: any[] = [];

      for (let i = 0; i < citations.length; i += batchSize) {
        const batchCitations = citations.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize);

        batches.push({
          session_id: sessionId,
          batch_number: batchNumber,
          citations: batchCitations,
          status: 'pending',
          processed_count: 0
        });
      }

      const { error } = await supabase
        .from(this.TABLE_IMPORT_BATCHES)
        .insert(batches);

      if (error) {
        console.error('Failed to create citation batches:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      console.error('Error creating citation batches:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get import session by ID
   */
  async getImportSession(sessionId: string): Promise<{ success: boolean; session?: ImportedCitationSession; error?: string }> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_IMPORT_SESSIONS)
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, session: data };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all import sessions for a library
   */
  async getLibraryImportSessions(
    userId: string,
    libraryId: string
  ): Promise<{ success: boolean; sessions?: ImportedCitationSession[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_IMPORT_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .eq('library_id', libraryId)
        .order('import_date', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, sessions: data || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Update import session progress
   */
  async updateImportProgress(
    sessionId: string,
    progress: Partial<CitationImportProgress>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updates: any = {};
      
      if (progress.current !== undefined && progress.total !== undefined) {
        updates.successful_imports = progress.current;
        updates.failed_imports = progress.total - progress.current;
      }
      
      if (progress.status) {
        updates.status = progress.status;
      }

      const { error } = await supabase
        .from(this.TABLE_IMPORT_SESSIONS)
        .update(updates)
        .eq('id', sessionId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get citation batches for a session
   */
  async getCitationBatches(sessionId: string): Promise<{ success: boolean; batches?: CitationImportBatch[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_IMPORT_BATCHES)
        .select('*')
        .eq('session_id', sessionId)
        .order('batch_number', { ascending: true });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, batches: data || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Process citations in batches to avoid resource exhaustion
   */
  async processCitationBatches(
    sessionId: string,
    userId: string,
    libraryId: string,
    onProgress?: (progress: CitationImportProgress) => void
  ): Promise<{ success: boolean; importedIds?: string[]; errors?: string[] }> {
    const allImportedIds: string[] = [];
    const allErrors: string[] = [];

    try {
      // Get session info
      const sessionResult = await this.getImportSession(sessionId);
      if (!sessionResult.success || !sessionResult.session) {
        return { success: false, errors: ['Session not found'] };
      }

      const session = sessionResult.session;

      // Check if this session uses batches
      if (!session.total_batches || session.total_batches === 0) {
        // Fall back to direct processing for small imports
        return await this.saveCitationsToLibrary(sessionId, session.citations, userId, libraryId,
          onProgress ? (current, total) => onProgress({ sessionId, current, total, status: 'processing', errors: [] }) : undefined
        );
      }

      // Update session status to processing
      await this.updateImportProgress(sessionId, { status: 'processing' });

      // Get all batches
      const batchesResult = await this.getCitationBatches(sessionId);
      if (!batchesResult.success || !batchesResult.batches) {
        return { success: false, errors: ['Failed to load citation batches'] };
      }

      const batches = batchesResult.batches;
      let totalProcessed = 0;

      // Process each batch
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];

        if (onProgress) {
          onProgress({
            sessionId,
            current: totalProcessed,
            total: session.total_citations,
            status: 'processing',
            errors: allErrors,
            currentBatch: i + 1,
            totalBatches: batches.length,
            batchProgress: 0
          });
        }

        // Update batch status to processing
        await this.updateBatchStatus(batch.id, 'processing');

        // Process citations in this batch
        const batchResult = await this.processSingleBatch(batch, userId, libraryId, (current, total) => {
          if (onProgress) {
            onProgress({
              sessionId,
              current: totalProcessed + current,
              total: session.total_citations,
              status: 'processing',
              errors: allErrors,
              currentBatch: i + 1,
              totalBatches: batches.length,
              batchProgress: Math.round((current / total) * 100)
            });
          }
        });

        if (batchResult.success) {
          allImportedIds.push(...(batchResult.importedIds || []));
          allErrors.push(...(batchResult.errors || []));
          totalProcessed += batch.citations.length;

          // Update batch status to completed
          await this.updateBatchStatus(batch.id, 'completed', batch.citations.length);
        } else {
          allErrors.push(...(batchResult.errors || []));
          await this.updateBatchStatus(batch.id, 'failed', 0, batchResult.errors?.join('; '));
        }

        // Update session progress
        await this.updateImportProgress(sessionId, {
          current: allImportedIds.length,
          total: session.total_citations,
          status: 'processing'
        });

        // Small delay between batches to prevent overwhelming the database
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Update session with final results
      const finalStatus = allImportedIds.length === 0 ? 'failed' : 'completed';
      await this.updateImportProgress(sessionId, {
        current: allImportedIds.length,
        total: session.total_citations,
        status: finalStatus
      });

      if (onProgress) {
        onProgress({
          sessionId,
          current: allImportedIds.length,
          total: session.total_citations,
          status: finalStatus,
          errors: allErrors,
          currentBatch: batches.length,
          totalBatches: batches.length,
          batchProgress: 100
        });
      }

      return {
        success: true,
        importedIds: allImportedIds,
        errors: allErrors.length > 0 ? allErrors : undefined
      };
    } catch (error: any) {
      await this.updateImportProgress(sessionId, { status: 'failed' });
      return { success: false, errors: [error.message] };
    }
  }

  /**
   * Process a single batch of citations
   */
  private async processSingleBatch(
    batch: CitationImportBatch,
    userId: string,
    libraryId: string,
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: boolean; importedIds?: string[]; errors?: string[] }> {
    const importedIds: string[] = [];
    const errors: string[] = [];

    try {
      for (let i = 0; i < batch.citations.length; i++) {
        const citation = batch.citations[i];

        if (onProgress) {
          onProgress(i + 1, batch.citations.length);
        }

        try {
          // Convert citation to library article format
          const articleData = this.convertToLibraryArticle(citation, libraryId, userId);

          // Check for duplicates
          const existingArticle = await this.findDuplicateArticle(articleData);
          if (existingArticle) {
            console.log(`Skipping duplicate article: ${citation.title}`);
            continue;
          }

          // Insert article
          const { data, error } = await supabase
            .from(this.TABLE_LIBRARY_ARTICLES)
            .insert(articleData)
            .select()
            .single();

          if (error) {
            console.error(`Failed to save citation ${i + 1} in batch ${batch.batch_number}:`, error);
            errors.push(`Batch ${batch.batch_number}, Citation ${i + 1}: ${error.message}`);
          } else {
            importedIds.push(data.id);
            console.log(`Adding article to library:`, data.title);
            console.log(`Full article data being inserted:`, data);
          }
        } catch (error: any) {
          console.error(`Error processing citation ${i + 1} in batch ${batch.batch_number}:`, error);
          errors.push(`Batch ${batch.batch_number}, Citation ${i + 1}: ${error.message}`);
        }
      }

      return {
        success: true,
        importedIds,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      return { success: false, errors: [error.message] };
    }
  }

  /**
   * Update batch status
   */
  private async updateBatchStatus(
    batchId: string,
    status: 'pending' | 'processing' | 'completed' | 'failed',
    processedCount: number = 0,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updates: any = {
        status,
        processed_count: processedCount,
        updated_at: new Date().toISOString()
      };

      if (errorMessage) {
        updates.error_message = errorMessage;
      }

      await supabase
        .from(this.TABLE_IMPORT_BATCHES)
        .update(updates)
        .eq('id', batchId);
    } catch (error) {
      console.error('Failed to update batch status:', error);
    }
  }

  /**
   * Save citations to library_articles table (legacy method for small imports)
   */
  async saveCitationsToLibrary(
    sessionId: string,
    citations: ProcessedCitationData[],
    userId: string,
    libraryId: string,
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: boolean; importedIds?: string[]; errors?: string[] }> {
    const importedIds: string[] = [];
    const errors: string[] = [];

    try {
      // Update session status to processing
      await this.updateImportProgress(sessionId, { status: 'processing' });

      for (let i = 0; i < citations.length; i++) {
        const citation = citations[i];

        if (onProgress) {
          onProgress(i + 1, citations.length);
        }

        try {
          // Convert citation to library article format
          const articleData = this.convertToLibraryArticle(citation, libraryId, userId);

          // Check for duplicates
          const existingArticle = await this.findDuplicateArticle(articleData);
          if (existingArticle) {
            console.log(`Skipping duplicate article: ${citation.title}`);
            continue;
          }

          // Insert article
          const { data, error } = await supabase
            .from(this.TABLE_LIBRARY_ARTICLES)
            .insert(articleData)
            .select()
            .single();

          if (error) {
            console.error(`Failed to save citation ${i + 1}:`, error);
            errors.push(`Citation ${i + 1}: ${error.message}`);
          } else {
            importedIds.push(data.id);
          }
        } catch (error: any) {
          console.error(`Error processing citation ${i + 1}:`, error);
          errors.push(`Citation ${i + 1}: ${error.message}`);
        }
      }

      // Update session with final results
      const finalStatus = errors.length === citations.length ? 'failed' : 'completed';
      await this.updateImportProgress(sessionId, {
        current: importedIds.length,
        total: citations.length,
        status: finalStatus
      });

      return {
        success: true,
        importedIds,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      await this.updateImportProgress(sessionId, { status: 'failed' });
      return { success: false, errors: [error.message] };
    }
  }

  /**
   * Convert ProcessedCitationData to library article format
   */
  private convertToLibraryArticle(citation: ProcessedCitationData, libraryId: string, userId: string): any {
    return {
      user_id: userId,
      library_id: libraryId,
      title: citation.title || 'Untitled',
      authors: citation.authors || [],
      abstract: citation.abstract || null,
      doi: citation.doi || null,
      publication_year: citation.publicationYear || null,
      journal: citation.journal || null,
      volume: citation.volume || null,
      issue: citation.issue || null,
      pages: citation.pages || null,
      keywords: citation.keywords || [],
      tags: citation.tags || [],
      source_type: 'search', // Imported citations are treated as search results
      source_url: citation.url || null,
      notes: citation.notes || null,
      favorite: false,
      extraction_confidence: citation.processingConfidence || 0.5,
      extracted_metadata: {
        sourceFormat: citation.sourceFormat,
        sourceManager: citation.sourceManager,
        documentType: citation.documentType,
        publisher: citation.publisher,
        isbn: citation.isbn,
        issn: citation.issn,
        language: citation.language,
        processingWarnings: citation.processingWarnings || [],
        processingErrors: citation.processingErrors || []
      }
    };
  }

  /**
   * Find duplicate article by title and DOI
   */
  private async findDuplicateArticle(articleData: any): Promise<boolean> {
    try {
      let query = supabase
        .from(this.TABLE_LIBRARY_ARTICLES)
        .select('id')
        .eq('user_id', articleData.user_id)
        .eq('library_id', articleData.library_id);

      // Check by DOI first (most reliable)
      if (articleData.doi) {
        const { data: doiMatch } = await query.eq('doi', articleData.doi).limit(1);
        if (doiMatch && doiMatch.length > 0) {
          return true;
        }
      }

      // Check by title (less reliable but still useful)
      if (articleData.title) {
        const { data: titleMatch } = await query
          .ilike('title', `%${articleData.title.substring(0, 50)}%`)
          .limit(1);
        if (titleMatch && titleMatch.length > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return false; // If we can't check, allow the insert
    }
  }

  /**
   * Delete import session
   */
  async deleteImportSession(sessionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from(this.TABLE_IMPORT_SESSIONS)
        .delete()
        .eq('id', sessionId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }
}

export const citationPersistenceService = new CitationPersistenceService();
