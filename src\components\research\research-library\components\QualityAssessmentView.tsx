/**
 * Quality Assessment View Component
 * Displays quality assessment results and improvement suggestions
 */

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info, 
  TrendingUp, 
  FileText, 
  AlertCircle,
  Lightbulb,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { 
  QualityAssessment,
  QualityIssue,
  QualitySuggestion,
  ProcessedCitationData
} from '../types/citation-import.types';
import { qualityAssuranceService } from '../services/quality-assurance.service';

interface QualityAssessmentViewProps {
  citations: ProcessedCitationData[];
  onClose?: () => void;
}

interface QualityReport {
  overallScore: number;
  totalIssues: number;
  issuesByType: Record<string, number>;
  issuesBySeverity: Record<string, number>;
  recommendations: string[];
  topIssues: QualityIssue[];
}

export function QualityAssessmentView({ citations, onClose }: QualityAssessmentViewProps) {
  const [assessments, setAssessments] = useState<QualityAssessment[]>([]);
  const [qualityReport, setQualityReport] = useState<QualityReport | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCitation, setSelectedCitation] = useState<number | null>(null);

  useEffect(() => {
    generateQualityAssessment();
  }, [citations]);

  const generateQualityAssessment = async () => {
    try {
      setIsLoading(true);
      
      // Generate individual assessments
      const assessmentResult = await qualityAssuranceService.assessBatchQuality(citations);
      
      if (assessmentResult.success && assessmentResult.data) {
        setAssessments(assessmentResult.data);
      }
      
      // Generate overall quality report
      const report = await qualityAssuranceService.generateQualityReport(citations);
      setQualityReport(report);
      
    } catch (error) {
      console.error('Failed to generate quality assessment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number): string => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <BarChart3 className="h-8 w-8 animate-pulse text-blue-500 mx-auto mb-2" />
          <p className="text-gray-600">Analyzing citation quality...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Quality Assessment</h2>
          <p className="text-gray-600">Analysis of {citations.length} citations</p>
        </div>
        {onClose && (
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        )}
      </div>

      {/* Overall Quality Report */}
      {qualityReport && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Overall Quality Report
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className={`text-3xl font-bold ${getScoreColor(qualityReport.overallScore)}`}>
                  {(qualityReport.overallScore * 100).toFixed(0)}%
                </div>
                <div className="text-sm text-gray-600">Overall Score</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-gray-900">
                  {qualityReport.totalIssues}
                </div>
                <div className="text-sm text-gray-600">Total Issues</div>
              </div>
              
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-3xl font-bold text-red-600">
                  {qualityReport.issuesBySeverity.high || 0}
                </div>
                <div className="text-sm text-gray-600">High Priority</div>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-3xl font-bold text-yellow-600">
                  {qualityReport.issuesBySeverity.medium || 0}
                </div>
                <div className="text-sm text-gray-600">Medium Priority</div>
              </div>
            </div>

            {/* Issue Types Breakdown */}
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Issues by Type</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {Object.entries(qualityReport.issuesByType).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-700 capitalize">
                      {type.replace('_', ' ')}
                    </span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* Recommendations */}
            {qualityReport.recommendations.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Recommendations
                </h4>
                <ul className="space-y-2">
                  {qualityReport.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Individual Citation Assessments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Citation Quality Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {assessments.map((assessment, index) => (
              <div 
                key={index}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedCitation === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedCitation(selectedCitation === index ? null : index)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 truncate">
                      {citations[index].title || 'Untitled Citation'}
                    </h4>
                    <p className="text-sm text-gray-600 truncate">
                      {citations[index].authors.join(', ') || 'Unknown Authors'}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge className={getScoreBadgeColor(assessment.score)}>
                      {(assessment.score * 100).toFixed(0)}%
                    </Badge>
                    
                    {assessment.issues.length > 0 && (
                      <Badge variant="outline" className="text-red-600">
                        {assessment.issues.length} issues
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Expanded Details */}
                {selectedCitation === index && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    {/* Quality Scores */}
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className={`text-lg font-semibold ${getScoreColor(assessment.completeness)}`}>
                          {(assessment.completeness * 100).toFixed(0)}%
                        </div>
                        <div className="text-xs text-gray-600">Completeness</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-lg font-semibold ${getScoreColor(assessment.accuracy)}`}>
                          {(assessment.accuracy * 100).toFixed(0)}%
                        </div>
                        <div className="text-xs text-gray-600">Accuracy</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-lg font-semibold ${getScoreColor(assessment.consistency)}`}>
                          {(assessment.consistency * 100).toFixed(0)}%
                        </div>
                        <div className="text-xs text-gray-600">Consistency</div>
                      </div>
                    </div>

                    {/* Issues */}
                    {assessment.issues.length > 0 && (
                      <div className="mb-4">
                        <h5 className="font-medium text-gray-900 mb-2">Issues</h5>
                        <div className="space-y-2">
                          {assessment.issues.map((issue, issueIndex) => (
                            <div key={issueIndex} className="flex items-start space-x-2 p-2 bg-gray-50 rounded">
                              {getSeverityIcon(issue.severity)}
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium text-gray-900">
                                    {issue.field}
                                  </span>
                                  <Badge className={getSeverityColor(issue.severity)}>
                                    {issue.severity}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{issue.message}</p>
                                {issue.value && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Current value: {issue.value}
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Suggestions */}
                    {assessment.suggestions.length > 0 && (
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Suggestions</h5>
                        <div className="space-y-2">
                          {assessment.suggestions.map((suggestion, suggestionIndex) => (
                            <div key={suggestionIndex} className="flex items-start space-x-2 p-2 bg-green-50 rounded">
                              <Lightbulb className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium text-gray-900">
                                    {suggestion.field}
                                  </span>
                                  <Badge variant="outline">
                                    {(suggestion.confidence * 100).toFixed(0)}% confidence
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{suggestion.message}</p>
                                {suggestion.suggestedValue && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Suggested: {suggestion.suggestedValue}
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default QualityAssessmentView;
