/**
 * Enhanced Paper Generator V2 - Main Component
 * User-friendly interface with history management and simplified writing
 */

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Plus, 
  ArrowLeft, 
  Settings, 
  Save, 
  Clock,
  BookOpen,
  Sparkles,
  Users,
  Target
} from 'lucide-react';

import { ArticleHistoryManager } from './ArticleHistoryManager';
import { PaperGeneratorV2 } from './PaperGeneratorV2';
import { UserArticlesService, UserArticle, CreateArticleRequest } from '../services/user-articles.service';
import { Paper, PaperMetadata, PaperSection } from '../types';
import { SECTION_TYPES } from '../constants';

export interface EnhancedPaperGeneratorV2Props {
  onClose?: () => void;
}

type ViewMode = 'dashboard' | 'writing' | 'creating';

export const EnhancedPaperGeneratorV2: React.FC<EnhancedPaperGeneratorV2Props> = ({ onClose }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('dashboard');
  const [currentArticle, setCurrentArticle] = useState<UserArticle | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPaper, setCurrentPaper] = useState<Paper | null>(null);

  const articlesService = UserArticlesService.getInstance();

  // Handle article selection from dashboard
  const handleSelectArticle = async (articleId: string) => {
    try {
      setIsLoading(true);
      const articleData = await articlesService.getArticleById(articleId);
      setCurrentArticle(articleData.article);
      
      // Convert article data to Paper format for the generator
      const paper = convertArticleToPaper(articleData.article, articleData.sections, articleData.references);
      setCurrentPaper(paper);
      setViewMode('writing');
      
      toast.success('Article loaded successfully');
    } catch (error) {
      console.error('Failed to load article:', error);
      toast.error('Failed to load article');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle creating new article
  const handleCreateNew = () => {
    setViewMode('creating');
  };

  // Handle article creation
  const handleCreateArticle = async (data: CreateArticleRequest) => {
    try {
      setIsLoading(true);
      const newArticle = await articlesService.createArticle(data);
      setCurrentArticle(newArticle);
      
      // Create empty paper for new article
      const paper = createEmptyPaper(newArticle);
      setCurrentPaper(paper);
      setViewMode('writing');
      
      toast.success('New article created successfully');
    } catch (error) {
      console.error('Failed to create article:', error);
      toast.error('Failed to create article');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle going back to dashboard
  const handleBackToDashboard = () => {
    setCurrentArticle(null);
    setCurrentPaper(null);
    setViewMode('dashboard');
  };

  // Handle paper save with auto-save functionality
  const handleSavePaper = async (paper: Paper) => {
    if (!currentArticle) return;

    try {
      // Update article sections in database
      await updateArticleSections(currentArticle.id, paper);
      
      // Update article metadata
      await articlesService.updateArticle(currentArticle.id, {
        title: paper.metadata.title,
        total_words: calculateTotalWords(paper),
        progress_percentage: calculateProgress(paper),
        completed_sections: countCompletedSections(paper)
      });

      toast.success('Article saved successfully');
    } catch (error) {
      console.error('Failed to save article:', error);
      toast.error('Failed to save article');
    }
  };

  // Convert article data to Paper format
  const convertArticleToPaper = (article: UserArticle, sections: any[], references: any[]): Paper => {
    const paperSections: PaperSection[] = sections.map(section => ({
      id: section.id,
      type: section.section_type,
      title: section.title || '',
      content: section.content || '',
      subsections: [],
      status: section.status as any,
      aiGenerated: section.ai_generated,
      lastModified: new Date(section.updated_at),
      wordCount: section.word_count,
      citations: [],
      figures: []
    }));

    const metadata: PaperMetadata = {
      title: article.title,
      authors: [],
      researchField: article.research_field || '',
      keywords: article.keywords || [],
      status: article.status as any,
      createdAt: new Date(article.created_at),
      updatedAt: new Date(article.updated_at)
    };

    return {
      id: article.id,
      metadata,
      sections: paperSections,
      progress: {
        totalSections: article.total_sections,
        completedSections: article.completed_sections,
        totalWords: article.total_words,
        progressPercentage: article.progress_percentage
      },
      exportOptions: {},
      researchOptions: {}
    };
  };

  // Create empty paper for new article
  const createEmptyPaper = (article: UserArticle): Paper => {
    const paperSections: PaperSection[] = SECTION_TYPES.map(sectionType => ({
      id: `section_${sectionType.id}_${Date.now()}`,
      type: sectionType.id,
      title: sectionType.name,
      content: '',
      subsections: [],
      status: 'empty',
      aiGenerated: false,
      lastModified: new Date(),
      wordCount: 0,
      citations: [],
      figures: []
    }));

    const metadata: PaperMetadata = {
      title: article.title,
      authors: [],
      researchField: article.research_field || '',
      keywords: article.keywords || [],
      status: 'draft',
      createdAt: new Date(article.created_at),
      updatedAt: new Date(article.updated_at)
    };

    return {
      id: article.id,
      metadata,
      sections: paperSections,
      progress: {
        totalSections: article.total_sections,
        completedSections: 0,
        totalWords: 0,
        progressPercentage: 0
      },
      exportOptions: {},
      researchOptions: {}
    };
  };

  // Helper functions for calculations
  const calculateTotalWords = (paper: Paper): number => {
    return paper.sections.reduce((total, section) => total + section.wordCount, 0);
  };

  const calculateProgress = (paper: Paper): number => {
    const completedSections = paper.sections.filter(s => s.status === 'completed').length;
    return Math.round((completedSections / paper.sections.length) * 100);
  };

  const countCompletedSections = (paper: Paper): number => {
    return paper.sections.filter(s => s.status === 'completed').length;
  };

  // Update article sections in database
  const updateArticleSections = async (articleId: string, paper: Paper) => {
    // This would update the sections in the database
    // Implementation would depend on the specific service methods available
    console.log('Updating article sections for:', articleId, paper);
  };

  // Render different views based on current mode
  if (viewMode === 'writing' && currentPaper) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Writing Interface Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  onClick={handleBackToDashboard}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Dashboard</span>
                </Button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">{currentPaper.metadata.title}</h1>
                  <p className="text-sm text-gray-500">{currentPaper.metadata.researchField}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant="outline">
                  {currentPaper.progress?.progressPercentage || 0}% Complete
                </Badge>
                <Button
                  variant="outline"
                  onClick={() => handleSavePaper(currentPaper)}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Save</span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Paper Generator Component */}
        <PaperGeneratorV2
          initialPaper={currentPaper}
          onSave={handleSavePaper}
        />
      </div>
    );
  }

  if (viewMode === 'creating') {
    return (
      <div className="min-h-screen bg-gray-50">
        <CreateArticleInterface
          onBack={handleBackToDashboard}
          onCreate={handleCreateArticle}
          isLoading={isLoading}
        />
      </div>
    );
  }

  // Dashboard view
  return (
    <div className="min-h-screen bg-gray-50">
      <ArticleHistoryManager
        onSelectArticle={handleSelectArticle}
        onCreateNew={handleCreateNew}
      />
    </div>
  );
};

// Create Article Interface Component
interface CreateArticleInterfaceProps {
  onBack: () => void;
  onCreate: (data: CreateArticleRequest) => void;
  isLoading: boolean;
}

const CreateArticleInterface: React.FC<CreateArticleInterfaceProps> = ({ onBack, onCreate, isLoading }) => {
  const [formData, setFormData] = useState<CreateArticleRequest>({
    title: '',
    research_field: '',
    description: '',
    keywords: [],
    ai_model: 'gemini-2.5-flash',
    template_type: 'research_paper'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.title.trim()) {
      onCreate(formData);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">Create New Article</CardTitle>
              <p className="text-gray-500 mt-1">Start your research paper with AI assistance</p>
            </div>
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Article Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your research paper title..."
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Research Field
                </label>
                <input
                  type="text"
                  value={formData.research_field}
                  onChange={(e) => setFormData({ ...formData, research_field: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Computer Science, Biology..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  AI Model
                </label>
                <select
                  value={formData.ai_model}
                  onChange={(e) => setFormData({ ...formData, ai_model: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                  <option value="gpt-4">GPT-4</option>
                  <option value="claude-3">Claude 3</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                placeholder="Brief description of your research topic and objectives..."
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={onBack}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading || !formData.title.trim()}>
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Article
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
