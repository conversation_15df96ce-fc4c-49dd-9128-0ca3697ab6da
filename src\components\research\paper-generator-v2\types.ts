import { LucideIcon } from "lucide-react";

// Paper metadata and structure
export interface PaperMetadata {
  id?: string;
  title: string;
  authors: string[];
  researchField: string;
  keywords: string[];
  abstract?: string;
  createdAt?: Date;
  updatedAt?: Date;
  status: 'draft' | 'in-progress' | 'completed';
}

// Section types and structure
export interface SectionType {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
  description: string;
  order: number;
  required: boolean;
  category: 'core' | 'optional' | 'flexible';
}

export interface SubSection {
  id: string;
  title: string;
  content: string;
  order: number;
  aiGenerated: boolean;
  lastModified: Date;
}

// Enhanced content block for hierarchical writing
export interface ContentBlock {
  id: string;
  type: 'heading' | 'subheading' | 'paragraph' | 'figure' | 'citation-block';
  level?: number; // 1, 2, 3 for heading hierarchy (H1, H2, H3)
  title?: string; // For headings and subheadings
  content: string;
  order: number;
  aiGenerated: boolean;
  lastModified: Date;
  citations?: Citation[];
  figures?: Figure[];
  metadata?: {
    wordCount?: number;
    aiContext?: string; // Context used for AI generation
    suggestions?: string[]; // AI suggestions for this block
    isExpanded?: boolean; // UI state for outline view
  };
}

export interface PaperSection {
  id: string;
  type: string;
  title: string;
  content: string; // Legacy content field - maintained for backward compatibility
  subsections: SubSection[]; // Legacy subsections - maintained for backward compatibility
  contentBlocks?: ContentBlock[]; // New hierarchical content structure
  status: 'empty' | 'draft' | 'completed' | 'reviewed';
  aiGenerated: boolean;
  lastModified: Date;
  wordCount: number;
  citations: Citation[];
  figures: Figure[];
  // Enhanced metadata for hierarchical content
  structure?: {
    useHierarchicalContent: boolean; // Flag to determine which content system to use
    headingCount: number;
    paragraphCount: number;
    figureCount: number;
  };
}

// Content and media types
export interface Figure {
  id: string;
  title: string;
  caption: string;
  imageUrl?: string;
  base64Data?: string;
  analysis?: string;
  aiAnalysis?: string;
  order: number;
}

export interface Citation {
  id: string;
  text: string;
  source: string;
  url?: string;
  doi?: string;
  authors: string[];
  year: number;
  title: string;
  journal?: string;
  verified: boolean;
}

// AI model configuration
export interface AIModel {
  id: string;
  name: string;
  provider: 'openrouter' | 'google' | 'openai';
  description: string;
  maxTokens: number;
  supportsImages: boolean;
  cost: 'low' | 'medium' | 'high';
  category: 'fast' | 'balanced' | 'premium';
  strengths: string[];
  bestFor: string[];
}

// Generation options and context
export interface GenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  includeContext: boolean;
  includeCitations: boolean;
  enhancementType?: 'grammar' | 'clarity' | 'academic' | 'structure';
}

export interface SectionContext {
  previousSections: PaperSection[];
  paperMetadata: PaperMetadata;
  researchField: string;
  keywords: string[];
  targetAudience: 'academic' | 'general' | 'expert';
  writingStyle: 'formal' | 'conversational' | 'technical';
}

// Generation requests and responses
export interface GenerationRequest {
  sectionType: string;
  prompt: string;
  context: SectionContext;
  options: GenerationOptions;
  existingContent?: string;
  enhancementMode?: boolean;
}

export interface GenerationResponse {
  content: string;
  citations: Citation[];
  suggestions: string[];
  confidence: number;
  tokens: number;
  model: string;
  processingTime: number;
}

// Enhanced interfaces for hierarchical content generation
export interface ContentBlockGenerationRequest {
  blockType: ContentBlock['type'];
  blockLevel?: number;
  context: {
    sectionType: string;
    sectionTitle: string;
    existingBlocks: ContentBlock[];
    paperMetadata: PaperMetadata;
    previousContent?: string;
    targetHeading?: string;
  };
  options: GenerationOptions;
  prompt?: string; // User-provided prompt for specific content
}

export interface ContentBlockGenerationResponse {
  content: string;
  title?: string; // For headings
  citations: Citation[];
  suggestions: string[];
  followUpPrompts: string[]; // Suggested next actions/content
  tokens: number;
  confidence: number;
}

export interface HeadingSuggestion {
  id: string;
  title: string;
  level: number;
  description: string;
  estimatedWordCount: number;
  priority: 'high' | 'medium' | 'low';
  aiReasoning: string;
}

export interface WritingAssistanceRequest {
  currentBlock: ContentBlock;
  context: {
    sectionType: string;
    allBlocks: ContentBlock[];
    paperMetadata: PaperMetadata;
    userInput?: string;
  };
  assistanceType: 'continue' | 'expand' | 'improve' | 'suggest-next';
}

export interface WritingAssistanceResponse {
  suggestions: string[];
  continuationText?: string;
  improvementSuggestions?: string[];
  nextHeadingSuggestions?: HeadingSuggestion[];
  citations?: Citation[];
}

// Review and quality control
export interface ReviewCriteria {
  grammar: boolean;
  clarity: boolean;
  academicTone: boolean;
  structure: boolean;
  citations: boolean;
  coherence: boolean;
}

export interface ReviewResult {
  score: number;
  feedback: ReviewFeedback[];
  suggestions: string[];
  strengths: string[];
  improvements: string[];
}

export interface ReviewFeedback {
  type: 'error' | 'warning' | 'suggestion' | 'strength';
  category: 'grammar' | 'clarity' | 'structure' | 'citations' | 'content';
  message: string;
  location?: {
    start: number;
    end: number;
  };
  suggestion?: string;
}

// User interface state
export interface UIState {
  selectedSection: string | null;
  selectedModel: string;
  isGenerating: boolean;
  showReview: boolean;
  showExport: boolean;
  sidebarCollapsed: boolean;
  activeTab: 'write' | 'review' | 'export';
  mode: 'step-by-step' | 'import-enhance';
  hideQuickSetup: boolean;
  hideProgress: boolean;
  showImportDialog: boolean;
  viewMode: 'sections' | 'complete-article';
}

// Progress tracking
export interface ProgressMetrics {
  totalSections: number;
  completedSections: number;
  totalWords: number;
  sectionsProgress: Record<string, number>;
  estimatedTimeRemaining: number;
  lastActivity: Date;
}

// Export options
export interface ExportOptions {
  format: 'pdf' | 'docx' | 'latex' | 'html' | 'markdown';
  includeMetadata: boolean;
  includeCitations: boolean;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'ieee';
  template: 'standard' | 'journal' | 'conference' | 'thesis';
}

// Import and enhance options
export interface ImportOptions {
  mode: 'paste-content' | 'upload-file';
  targetSections: string[];
  enhanceExisting: boolean;
  generateMissing: boolean;
}

// Research integration options
export interface ResearchOptions {
  enableTavilySearch: boolean;
  maxSearchResults: number;
  prioritizeAcademicSources: boolean;
  includeCitations: boolean;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'ieee';
  // Research library integration
  researchLibraryId?: string;
  enableResearchLibraryContext: boolean;
  maxLibraryReferences: number;
}

// Complete paper structure
export interface Paper {
  metadata: PaperMetadata;
  sections: PaperSection[];
  progress: ProgressMetrics;
  exportOptions: ExportOptions;
  importOptions?: ImportOptions;
  researchOptions: ResearchOptions;
}

// Service interfaces
export interface PaperAIService {
  generateSection(request: GenerationRequest): Promise<GenerationResponse>;
  enhanceContent(content: string, options: GenerationOptions): Promise<string>;
  reviewSection(section: PaperSection, criteria: ReviewCriteria): Promise<ReviewResult>;
  analyzeFigure(figure: Figure): Promise<string>;
  generateCitations(content: string): Promise<Citation[]>;
}

export interface SectionContextService {
  buildContext(sectionType: string, paper: Paper): SectionContext;
  getRelevantSections(sectionType: string, sections: PaperSection[]): PaperSection[];
  generateContextPrompt(context: SectionContext): string;
}

export interface ExportService {
  exportPaper(paper: Paper, options: ExportOptions): Promise<Blob>;
  generatePreview(paper: Paper): string;
  validateExport(paper: Paper): string[];
}
