import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  GenerationModeSelector,
  EnhancedInputPanel,
  MultiImageUpload,
  OutlineGenerator,
  StepByStepGenerator,
  type EnhancedInputs,
  type UploadedImage,
  type OutlineItem,
  type GenerationMode,
  type DeepModeWorkflow
} from '../components/enhanced-generation';
import { Brain, Sparkles, Target, FileText } from 'lucide-react';
import { toast } from 'sonner';

export function EnhancedGenerationDemo() {
  const [generationMode, setGenerationMode] = useState<GenerationMode>('direct');
  const [enhancedInputs, setEnhancedInputs] = useState<EnhancedInputs>({
    prompt: 'Write an introduction section for a research paper about machine learning applications in healthcare.',
    requirements: 'Include background information, problem statement, and research objectives.',
    keyPoints: ['AI in medical diagnosis', 'Healthcare data challenges', 'Ethical considerations'],
    writingStyle: 'Academic & Formal',
    targetWordCount: 500,
    specificInstructions: 'Focus on recent developments and cite relevant literature.'
  });
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [deepModeWorkflow, setDeepModeWorkflow] = useState<DeepModeWorkflow>({
    step: 'input',
    outline: [],
    generationSteps: [],
    currentStepIndex: 0
  });
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false);
  const [isGeneratingStep, setIsGeneratingStep] = useState(false);

  // Demo handlers
  const handleGenerateOutline = async (): Promise<OutlineItem[]> => {
    setIsGeneratingOutline(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const demoOutline: OutlineItem[] = [
      {
        id: 'intro_1',
        type: 'h2',
        title: 'Background and Context',
        description: 'Provide background information about AI in healthcare and current state of the field',
        estimatedWords: 150,
        children: [],
        generated: false
      },
      {
        id: 'intro_2',
        type: 'h2',
        title: 'Problem Statement',
        description: 'Clearly define the research problem and its significance in healthcare AI',
        estimatedWords: 100,
        children: [],
        generated: false
      },
      {
        id: 'intro_3',
        type: 'h2',
        title: 'Research Objectives',
        description: 'State the main objectives and research questions for this study',
        estimatedWords: 100,
        children: [],
        generated: false
      },
      {
        id: 'intro_4',
        type: 'h2',
        title: 'Paper Organization',
        description: 'Brief overview of the paper structure and organization',
        estimatedWords: 50,
        children: [],
        generated: false
      }
    ];

    setDeepModeWorkflow(prev => ({
      ...prev,
      step: 'review',
      outline: demoOutline
    }));
    
    setIsGeneratingOutline(false);
    toast.success('Outline generated successfully!');
    return demoOutline;
  };

  const handleGenerateFromOutline = async (
    outlineItem: OutlineItem,
    inputs: EnhancedInputs,
    images: UploadedImage[],
    previousContent?: string
  ): Promise<string> => {
    setIsGeneratingStep(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const demoContent = `This is demo content for "${outlineItem.title}".

${outlineItem.description}

This section demonstrates the enhanced generation workflow with approximately ${outlineItem.estimatedWords} words of academic content. The content is generated based on:

- User inputs: ${inputs.prompt}
- Writing style: ${inputs.writingStyle}
- Key points: ${inputs.keyPoints.join(', ')}
- Target word count: ${inputs.targetWordCount}
- Images uploaded: ${images.length}

${previousContent ? 'This content builds upon the previous sections and maintains consistency with the overall paper structure.' : 'This is the first section being generated.'}

The enhanced generation system allows for structured, step-by-step content creation that ensures coherence and academic quality throughout the writing process.`;

    setIsGeneratingStep(false);
    toast.success(`Content generated for "${outlineItem.title}"`);
    return demoContent;
  };

  const handleAnalyzeImage = async (base64: string, prompt?: string): Promise<string> => {
    // Simulate image analysis
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const analysis = `This image appears to be a research-related figure that demonstrates key concepts relevant to the study. The image contains visual elements that support the academic discussion and provides valuable context for understanding the research methodology and findings. This analysis would typically include detailed descriptions of charts, diagrams, or other visual content that enhances the paper's academic value.`;
    
    toast.success('Image analysis completed!');
    return analysis;
  };

  const handleAcceptOutline = () => {
    setDeepModeWorkflow(prev => ({
      ...prev,
      step: 'generate'
    }));
    toast.success('Outline accepted! Starting content generation...');
  };

  const handleRejectOutline = () => {
    setDeepModeWorkflow(prev => ({
      ...prev,
      step: 'input',
      outline: []
    }));
    toast.info('Outline rejected. You can generate a new one.');
  };

  const handleRegenerateOutline = async () => {
    await handleGenerateOutline();
  };

  const handleGenerateStepContent = async (itemId: string): Promise<string> => {
    const outlineItem = deepModeWorkflow.outline.find(item => item.id === itemId);
    if (!outlineItem) return '';

    const previousContent = deepModeWorkflow.outline
      .filter(item => item.generated && item.content)
      .map(item => item.content)
      .join('\n\n');

    return await handleGenerateFromOutline(outlineItem, enhancedInputs, uploadedImages, previousContent);
  };

  const handleStepComplete = (itemId: string, content: string) => {
    setDeepModeWorkflow(prev => ({
      ...prev,
      outline: prev.outline.map(item =>
        item.id === itemId
          ? { ...item, generated: true, content }
          : item
      )
    }));
  };

  const handleAllStepsComplete = () => {
    setDeepModeWorkflow(prev => ({
      ...prev,
      step: 'complete'
    }));
    toast.success('All content generation completed!');
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Brain className="h-6 w-6 text-blue-600" />
            Enhanced Paper Generation Demo
          </CardTitle>
          <p className="text-gray-600">
            Test the new enhanced generation workflow with Deep Mode (outline-first) and Direct Mode capabilities.
          </p>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Input and Configuration */}
        <div className="space-y-4">
          <GenerationModeSelector
            selectedMode={generationMode}
            onModeChange={setGenerationMode}
          />

          <EnhancedInputPanel
            inputs={enhancedInputs}
            onInputsChange={setEnhancedInputs}
            sectionType="introduction"
          />

          <MultiImageUpload
            images={uploadedImages}
            onImagesChange={setUploadedImages}
            onAnalyzeImage={handleAnalyzeImage}
            selectedModel="gemini-2.0-flash-exp"
          />
        </div>

        {/* Right Column - Generation Workflow */}
        <div className="space-y-4">
          {generationMode === 'deep' && (
            <>
              {/* Outline Generation */}
              {deepModeWorkflow.step === 'input' && (
                <OutlineGenerator
                  isGenerating={isGeneratingOutline}
                  generatedOutline={[]}
                  onGenerateOutline={handleGenerateOutline}
                  onAcceptOutline={handleAcceptOutline}
                  onRejectOutline={handleRejectOutline}
                  onRegenerateOutline={handleRegenerateOutline}
                  sectionType="introduction"
                />
              )}

              {/* Outline Review */}
              {deepModeWorkflow.step === 'review' && (
                <OutlineGenerator
                  isGenerating={false}
                  generatedOutline={deepModeWorkflow.outline}
                  onGenerateOutline={handleGenerateOutline}
                  onAcceptOutline={handleAcceptOutline}
                  onRejectOutline={handleRejectOutline}
                  onRegenerateOutline={handleRegenerateOutline}
                  sectionType="introduction"
                />
              )}

              {/* Step-by-Step Generation */}
              {deepModeWorkflow.step === 'generate' && (
                <StepByStepGenerator
                  outline={deepModeWorkflow.outline}
                  onGenerateStep={handleGenerateStepContent}
                  onStepComplete={handleStepComplete}
                  onAllComplete={handleAllStepsComplete}
                  isGenerating={isGeneratingStep}
                />
              )}

              {/* Completion */}
              {deepModeWorkflow.step === 'complete' && (
                <Card className="border-green-200 bg-green-50">
                  <CardContent className="p-6 text-center">
                    <Target className="h-12 w-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-green-900 mb-2">
                      Generation Complete!
                    </h3>
                    <p className="text-green-700 mb-4">
                      All outline items have been generated successfully.
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Badge variant="outline" className="bg-white">
                        {deepModeWorkflow.outline.length} sections
                      </Badge>
                      <Badge variant="outline" className="bg-white">
                        {deepModeWorkflow.outline.reduce((total, item) => total + (item.content?.split(' ').length || 0), 0)} words
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {generationMode === 'direct' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Direct Mode Generation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  In Direct Mode, content is generated immediately using all your enhanced inputs and uploaded images.
                </p>
                <Button className="w-full" onClick={() => toast.success('Direct generation would start here!')}>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Enhanced Content
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
