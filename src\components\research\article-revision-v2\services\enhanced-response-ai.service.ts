/**
 * Enhanced Response to Reviews AI Service
 * Implements sophisticated 3-step workflow for academic response generation
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import {
  CompleteArticle,
  ReviewerCommentsSet,
  ReviewerComment,
  RevisionOptions,
  ArticleSection,
  ResponseToReviewsResult
} from '../types';
import { AI_MODELS, DEFAULT_MODEL, API_CONFIG } from '../constants';

// Enhanced interfaces for the sophisticated workflow
export interface CommentAnalysisResult {
  totalComments: number;
  categorizedComments: {
    substantive: ReviewerComment[];
    clarity: ReviewerComment[];
    factual: ReviewerComment[];
    formatting: ReviewerComment[];
  };
  sectionMapping: {
    [key in ArticleSection]?: ReviewerComment[];
  };
  conflictingOpinions: {
    comment1: ReviewerComment;
    comment2: ReviewerComment;
    conflict: string;
  }[];
  addressabilityAssessment: {
    addressable: ReviewerComment[];
    nonAddressable: ReviewerComment[];
    reasoning: string[];
  };
}

export interface RevisedArticleOutput {
  title: string;
  sections: {
    [key in ArticleSection]?: {
      originalContent: string;
      revisedContent: string;
      hasChanges: boolean;
      changesSummary: string[];
    };
  };
  overallChangesSummary: {
    totalSections: number;
    sectionsModified: number;
    majorChanges: number;
    minorChanges: number;
  };
}

export interface DetailedChangeSummary {
  overview: {
    totalChanges: number;
    sectionsModified: string[];
    majorRevisions: string[];
  };
  sectionBySection: {
    [key in ArticleSection]?: {
      changes: {
        description: string;
        type: 'addition' | 'modification' | 'deletion' | 'restructuring';
        addressedComment: string;
        reviewerReference: string;
      }[];
      noChangesReason?: string;
    };
  };
  rationaleForChanges: string;
}

export interface ResponseLetterOutput {
  coverLetter: string;
  pointByPointResponses: {
    reviewerNumber: number;
    responses: {
      commentId: string;
      originalComment: string;
      response: string;
      changesImplemented: string[];
      locationOfChanges: string;
    }[];
  }[];
  summaryOfMajorChanges: string[];
}

export interface EnhancedResponseResult {
  step1_commentAnalysis: CommentAnalysisResult;
  step2_revisedArticle: RevisedArticleOutput;
  step3_outputs: {
    revisedArticle: string;
    changeSummary: string;
    responseLetter: string;
  };
  processingMetadata: {
    startTime: Date;
    endTime: Date;
    totalProcessingTime: number;
    stepsCompleted: number;
    qualityScore: number;
  };
}

export class EnhancedResponseAIService {
  private openRouterApiKey: string;
  private geminiApiKey: string;
  private geminiAI: GoogleGenerativeAI | null = null;

  constructor() {
    // Initialize API keys (same pattern as existing service)
    this.openRouterApiKey = import.meta.env.VITE_OPENROUTER_API_KEY ||
                           import.meta.env.VITE_AI_API_KEY ||
                           import.meta.env.VITE_OPENAI_API_KEY ||
                           import.meta.env.VITE_GEMINI_API_KEY ||
                           import.meta.env.VITE_API_KEY || '';

    this.geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY || '';

    if (this.geminiApiKey) {
      try {
        this.geminiAI = new GoogleGenerativeAI(this.geminiApiKey);
      } catch (error) {
        console.warn('Failed to initialize Gemini AI:', error);
      }
    }

    console.log('Enhanced Response AI Service initialized:', {
      hasOpenRouterKey: !!this.openRouterApiKey,
      hasGeminiKey: !!this.geminiApiKey,
      keyFormat: this.openRouterApiKey ? `${this.openRouterApiKey.substring(0, 5)}...` : 'none'
    });
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    const isValidKey = this.openRouterApiKey &&
                       !this.openRouterApiKey.includes('your_') &&
                       this.openRouterApiKey.length > 20;
    return Boolean(isValidKey || this.geminiApiKey);
  }

  /**
   * Main method: Generate enhanced response to reviews following 3-step workflow
   */
  async generateEnhancedResponse(
    originalArticle: CompleteArticle,
    reviewerComments: ReviewerCommentsSet[],
    options: RevisionOptions,
    onProgress?: (step: number, progress: number, message: string) => void
  ): Promise<EnhancedResponseResult> {
    const startTime = new Date();
    
    try {
      console.log('🚀 Starting Enhanced Response to Reviews workflow...');
      
      // Step 1: Comment Analysis
      onProgress?.(1, 0, 'Starting comment analysis...');
      const commentAnalysis = await this.performCommentAnalysis(
        reviewerComments, 
        originalArticle, 
        options,
        (progress) => onProgress?.(1, progress, 'Analyzing reviewer comments...')
      );
      
      // Step 2: Article Revision
      onProgress?.(2, 0, 'Starting article revision...');
      const revisedArticle = await this.performArticleRevision(
        originalArticle,
        commentAnalysis,
        options,
        (progress) => onProgress?.(2, progress, 'Revising article sections...')
      );
      
      // Step 3: Generate Three Outputs
      onProgress?.(3, 0, 'Generating final outputs...');
      const finalOutputs = await this.generateThreeOutputs(
        originalArticle,
        revisedArticle,
        commentAnalysis,
        reviewerComments,
        options,
        (progress) => onProgress?.(3, progress, 'Creating response documents...')
      );
      
      const endTime = new Date();
      const totalProcessingTime = endTime.getTime() - startTime.getTime();
      
      onProgress?.(3, 100, 'Enhanced response generation complete!');
      
      return {
        step1_commentAnalysis: commentAnalysis,
        step2_revisedArticle: revisedArticle,
        step3_outputs: finalOutputs,
        processingMetadata: {
          startTime,
          endTime,
          totalProcessingTime,
          stepsCompleted: 3,
          qualityScore: this.calculateQualityScore(commentAnalysis, revisedArticle)
        }
      };
      
    } catch (error) {
      console.error('Enhanced response generation failed:', error);
      throw error;
    }
  }

  /**
   * Step 1: Comment Analysis
   * Read and understand each reviewer comment carefully
   * Identify which section(s) each comment refers to
   * Categorize comments by type: substantive changes, clarity improvements, factual corrections, formatting issues
   * Note any conflicting reviewer opinions
   */
  private async performCommentAnalysis(
    reviewerComments: ReviewerCommentsSet[],
    originalArticle: CompleteArticle,
    options: RevisionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CommentAnalysisResult> {
    console.log('📋 Step 1: Performing comment analysis...');
    
    // Parse bulk comments into structured format
    onProgress?.(20);
    const parsedComments = await this.parseBulkCommentsToStructured(reviewerComments, options);
    
    // Analyze and categorize all comments
    onProgress?.(60);
    const analysisResult = await this.analyzeAndCategorizeComments(parsedComments, originalArticle, options);
    
    onProgress?.(100);
    console.log('✅ Step 1 complete: Comment analysis finished');
    return analysisResult;
  }

  /**
   * Parse bulk comments into structured comment objects using AI
   */
  private async parseBulkCommentsToStructured(
    reviewerComments: ReviewerCommentsSet[],
    options: RevisionOptions
  ): Promise<ReviewerCommentsSet[]> {
    const parsedReviewers: ReviewerCommentsSet[] = [];

    for (const reviewer of reviewerComments) {
      const parsedReviewer: ReviewerCommentsSet = {
        reviewerNumber: reviewer.reviewerNumber,
        comments: [],
        overallRecommendation: reviewer.overallRecommendation,
        bulkComments: reviewer.bulkComments
      };

      if (reviewer.bulkComments?.trim()) {
        const structuredComments = await this.parseIndividualComments(
          reviewer.bulkComments,
          reviewer.reviewerNumber,
          options
        );
        parsedReviewer.comments = structuredComments;
      } else if (reviewer.comments?.length > 0) {
        parsedReviewer.comments = reviewer.comments;
      }

      parsedReviewers.push(parsedReviewer);
    }

    return parsedReviewers;
  }

  /**
   * Parse individual comments from bulk text using AI with enhanced parsing
   */
  private async parseIndividualComments(
    bulkComments: string,
    reviewerNumber: number,
    options: RevisionOptions
  ): Promise<ReviewerComment[]> {
    if (!this.isConfigured()) {
      // Enhanced fallback for demo mode
      return this.createEnhancedFallbackComments(bulkComments, reviewerNumber);
    }

    const prompt = `You are an expert academic reviewer. Parse the following bulk reviewer comments into individual structured comments. Pay special attention to numbered lists, bullet points, and distinct concerns.

BULK COMMENTS FROM REVIEWER ${reviewerNumber}:
${bulkComments}

PARSING INSTRUCTIONS:
1. Look for numbered points (1., 2., 3., etc.)
2. Look for bullet points (-, •, *, etc.)
3. Look for paragraph breaks indicating separate concerns
4. Each distinct concern should be a separate comment
5. Preserve the exact wording and context

Return a JSON array of comment objects:
[
  {
    "id": "comment_${reviewerNumber}_1",
    "reviewerNumber": ${reviewerNumber},
    "comment": "The exact comment text (preserve full context)",
    "severity": "major|minor|suggestion",
    "category": "methodology|results|writing|structure|references|clarity|data|analysis|other",
    "section": "title|abstract|introduction|methodology|results|discussion|conclusion|references|acknowledgments" (infer from context),
    "lineNumber": number (if mentioned),
    "pageNumber": number (if mentioned),
    "figureTable": "string" (if referencing specific figure/table)
  }
]

CLASSIFICATION GUIDELINES:
- Severity: major (fundamental issues), minor (improvements), suggestion (optional)
- Category: be specific about the type of concern
- Section: infer from context even if not explicitly mentioned
- Extract line/page numbers and figure/table references when mentioned

IMPORTANT: Each numbered point or distinct concern should be a separate comment. Don't combine multiple issues into one comment.

Return ONLY the JSON array, no additional text.`;

    try {
      const response = await this.callAI(prompt, options.model);
      const parsed = this.cleanAndParseJSON(response);

      if (Array.isArray(parsed)) {
        return parsed.map((comment, index) => ({
          id: comment.id || `comment_${reviewerNumber}_${index + 1}`,
          reviewerNumber,
          comment: comment.comment || '',
          severity: comment.severity || 'minor',
          category: comment.category || 'other',
          section: comment.section
        }));
      } else {
        return this.createFallbackComments(bulkComments, reviewerNumber);
      }
    } catch (error) {
      console.error('Error parsing individual comments:', error);
      return this.createFallbackComments(bulkComments, reviewerNumber);
    }
  }

  /**
   * Create enhanced fallback comments when AI parsing fails
   */
  private createEnhancedFallbackComments(bulkComments: string, reviewerNumber: number): ReviewerComment[] {
    // Enhanced parsing patterns
    const patterns = [
      /(?:^|\n)\s*(\d+)\.\s+/gm,  // Numbered lists: 1. 2. 3.
      /(?:^|\n)\s*[-•*]\s+/gm,    // Bullet points: - • *
      /(?:^|\n)\s*[a-z]\)\s+/gm,  // Lettered lists: a) b) c)
      /(?:^|\n)\s*\([a-z]\)\s+/gm, // Parenthetical lists: (a) (b) (c)
    ];

    let comments: string[] = [];

    // Try numbered list pattern first (most common in academic reviews)
    const numberedMatches = bulkComments.split(/(?:^|\n)\s*\d+\.\s+/gm).filter(c => c.trim());
    if (numberedMatches.length > 1) {
      comments = numberedMatches;
    } else {
      // Try bullet points
      const bulletMatches = bulkComments.split(/(?:^|\n)\s*[-•*]\s+/gm).filter(c => c.trim());
      if (bulletMatches.length > 1) {
        comments = bulletMatches;
      } else {
        // Split by double line breaks
        const paragraphMatches = bulkComments.split(/\n\s*\n/).filter(c => c.trim());
        if (paragraphMatches.length > 1) {
          comments = paragraphMatches;
        } else {
          // Last resort: split by sentences
          comments = bulkComments.split(/[.!?]+/).filter(c => c.trim().length > 20);
        }
      }
    }

    return comments.map((comment, index) => {
      const trimmedComment = comment.trim();

      // Infer severity from keywords
      let severity: 'major' | 'minor' | 'suggestion' = 'minor';
      if (trimmedComment.toLowerCase().includes('must') ||
          trimmedComment.toLowerCase().includes('critical') ||
          trimmedComment.toLowerCase().includes('major') ||
          trimmedComment.toLowerCase().includes('fundamental')) {
        severity = 'major';
      } else if (trimmedComment.toLowerCase().includes('suggest') ||
                 trimmedComment.toLowerCase().includes('consider') ||
                 trimmedComment.toLowerCase().includes('might')) {
        severity = 'suggestion';
      }

      // Infer category from keywords
      let category: string = 'other';
      if (trimmedComment.toLowerCase().includes('method')) category = 'methodology';
      else if (trimmedComment.toLowerCase().includes('result')) category = 'results';
      else if (trimmedComment.toLowerCase().includes('discuss')) category = 'discussion';
      else if (trimmedComment.toLowerCase().includes('introduction')) category = 'introduction';
      else if (trimmedComment.toLowerCase().includes('conclusion')) category = 'conclusion';
      else if (trimmedComment.toLowerCase().includes('reference') || trimmedComment.toLowerCase().includes('citation')) category = 'references';
      else if (trimmedComment.toLowerCase().includes('clarity') || trimmedComment.toLowerCase().includes('clear')) category = 'clarity';
      else if (trimmedComment.toLowerCase().includes('figure') || trimmedComment.toLowerCase().includes('table')) category = 'data';

      return {
        id: `comment_${reviewerNumber}_${index + 1}`,
        reviewerNumber,
        comment: trimmedComment,
        severity,
        category: category as any
      };
    });
  }

  /**
   * Analyze and categorize all comments using AI
   */
  private async analyzeAndCategorizeComments(
    parsedComments: ReviewerCommentsSet[],
    originalArticle: CompleteArticle,
    options: RevisionOptions
  ): Promise<CommentAnalysisResult> {
    const allComments = parsedComments.flatMap(reviewer => reviewer.comments);

    if (!this.isConfigured()) {
      return this.generateDemoCommentAnalysis(allComments, originalArticle);
    }

    const prompt = `You are an expert academic editor analyzing reviewer comments for a research article.

ARTICLE TITLE: ${originalArticle.title}

ARTICLE SECTIONS AVAILABLE:
${Object.keys(originalArticle.content).join(', ')}

REVIEWER COMMENTS TO ANALYZE:
${allComments.map((comment, index) =>
  `${index + 1}. [Reviewer ${comment.reviewerNumber}] ${comment.comment}`
).join('\n')}

Perform comprehensive comment analysis and return a JSON object with this structure:

{
  "totalComments": number,
  "categorizedComments": {
    "substantive": [comment_ids], // Major content/methodology changes
    "clarity": [comment_ids], // Writing clarity, structure improvements
    "factual": [comment_ids], // Factual corrections, data issues
    "formatting": [comment_ids] // Citations, figures, formatting
  },
  "sectionMapping": {
    "introduction": [comment_ids], // Comments referring to introduction
    "methodology": [comment_ids], // Comments referring to methodology
    // ... for each section that has comments
  },
  "conflictingOpinions": [
    {
      "comment1": "comment_id",
      "comment2": "comment_id",
      "conflict": "Description of the conflict"
    }
  ],
  "addressabilityAssessment": {
    "addressable": [comment_ids], // Comments that can be addressed with revisions
    "nonAddressable": [comment_ids], // Comments requiring new data/experiments
    "reasoning": ["Explanation for each non-addressable comment"]
  }
}

Guidelines:
- Use comment IDs (like "comment_1_1") to reference comments
- Be thorough in section mapping - infer sections even if not explicitly mentioned
- Identify genuine conflicts between reviewers (not just different perspectives)
- Consider addressability realistically - what can be done with existing data vs requiring new research

Return ONLY the JSON object.`;

    try {
      const response = await this.callAI(prompt, options.model);
      const parsed = this.cleanAndParseJSON(response);

      return this.validateCommentAnalysis(parsed, allComments, originalArticle);
    } catch (error) {
      console.error('Error analyzing comments:', error);
      return this.generateDemoCommentAnalysis(allComments, originalArticle);
    }
  }

  /**
   * Step 2: Article Revision
   * Make ONLY the changes needed to address reviewer comments
   * Use bold formatting to highlight ALL modifications
   * Keep unchanged text exactly as original
   * Ensure changes are precise and targeted
   * Maintain the original writing style and tone
   */
  private async performArticleRevision(
    originalArticle: CompleteArticle,
    commentAnalysis: CommentAnalysisResult,
    options: RevisionOptions,
    onProgress?: (progress: number) => void
  ): Promise<RevisedArticleOutput> {
    console.log('✏️ Step 2: Performing article revision...');

    const revisedSections: RevisedArticleOutput['sections'] = {};
    const sectionsToRevise = Object.keys(originalArticle.content) as ArticleSection[];
    let processedSections = 0;

    for (const section of sectionsToRevise) {
      const sectionContent = originalArticle.content[section];
      if (!sectionContent?.trim()) continue;

      // Get comments for this section
      const sectionComments = commentAnalysis.sectionMapping[section] || [];

      if (sectionComments.length > 0) {
        // Revise this section
        const revisedSection = await this.reviseSingleSection(
          section,
          sectionContent,
          sectionComments,
          originalArticle,
          options
        );
        revisedSections[section] = revisedSection;
      } else {
        // No changes needed for this section
        revisedSections[section] = {
          originalContent: sectionContent,
          revisedContent: sectionContent,
          hasChanges: false,
          changesSummary: []
        };
      }

      processedSections++;
      onProgress?.((processedSections / sectionsToRevise.length) * 100);
    }

    const overallChangesSummary = this.calculateOverallChanges(revisedSections);

    console.log('✅ Step 2 complete: Article revision finished');
    return {
      title: originalArticle.title,
      sections: revisedSections,
      overallChangesSummary
    };
  }

  /**
   * Revise a single section based on reviewer comments
   */
  private async reviseSingleSection(
    section: ArticleSection,
    originalContent: string,
    comments: ReviewerComment[],
    originalArticle: CompleteArticle,
    options: RevisionOptions
  ): Promise<{
    originalContent: string;
    revisedContent: string;
    hasChanges: boolean;
    changesSummary: string[];
  }> {
    if (!this.isConfigured()) {
      return this.generateDemoSectionRevision(section, originalContent, comments);
    }

    const prompt = `You are an expert academic editor revising a research article section based on reviewer comments.

SECTION: ${section}
ARTICLE TITLE: ${originalArticle.title}

ORIGINAL CONTENT:
${originalContent}

REVIEWER COMMENTS TO ADDRESS:
${comments.map((comment, index) =>
  `${index + 1}. [Reviewer ${comment.reviewerNumber}] ${comment.comment}`
).join('\n')}

REVISION INSTRUCTIONS:
1. Make ONLY the changes needed to address the reviewer comments
2. Use **bold formatting** to highlight ALL modifications (additions, changes, improvements)
3. Keep unchanged text exactly as original
4. Maintain the original writing style and tone
5. Ensure changes are precise and targeted
6. If a section needs no changes, return it unchanged

Return a JSON object with this structure:
{
  "revisedContent": "The revised section content with **bold** changes",
  "hasChanges": true/false,
  "changesSummary": [
    "Brief description of change 1 (addresses Reviewer X comment)",
    "Brief description of change 2 (addresses Reviewer Y comment)"
  ]
}

IMPORTANT:
- Use **text** for ALL changes (additions, modifications, enhancements)
- Only bold the actual changed/added text, not entire sentences unless entirely new
- Be conservative - only change what's necessary to address comments
- Preserve academic tone and style

Return ONLY the JSON object.`;

    try {
      const response = await this.callAI(prompt, options.model);
      const parsed = this.cleanAndParseJSON(response);

      return {
        originalContent,
        revisedContent: parsed.revisedContent || originalContent,
        hasChanges: parsed.hasChanges || false,
        changesSummary: parsed.changesSummary || []
      };
    } catch (error) {
      console.error(`Error revising section ${section}:`, error);
      return this.generateDemoSectionRevision(section, originalContent, comments);
    }
  }

  /**
   * Step 3: Generate Three Outputs
   * OUTPUT 1: REVISED ARTICLE with bold changes
   * OUTPUT 2: DETAILED CHANGE SUMMARY FOR AUTHOR
   * OUTPUT 3: RESPONSE LETTER TO REVIEWERS
   */
  private async generateThreeOutputs(
    originalArticle: CompleteArticle,
    revisedArticle: RevisedArticleOutput,
    commentAnalysis: CommentAnalysisResult,
    reviewerComments: ReviewerCommentsSet[],
    options: RevisionOptions,
    onProgress?: (progress: number) => void
  ): Promise<{
    revisedArticle: string;
    changeSummary: string;
    responseLetter: string;
  }> {
    console.log('📄 Step 3: Generating three outputs...');

    // Output 1: Revised Article
    onProgress?.(20);
    const revisedArticleText = this.generateRevisedArticleOutput(originalArticle, revisedArticle);

    // Output 2: Detailed Change Summary
    onProgress?.(50);
    const changeSummary = await this.generateDetailedChangeSummary(
      originalArticle,
      revisedArticle,
      commentAnalysis,
      options
    );

    // Output 3: Response Letter
    onProgress?.(80);
    const responseLetter = await this.generateResponseLetter(
      originalArticle,
      revisedArticle,
      commentAnalysis,
      reviewerComments,
      options
    );

    onProgress?.(100);
    console.log('✅ Step 3 complete: All outputs generated');

    return {
      revisedArticle: revisedArticleText,
      changeSummary,
      responseLetter
    };
  }

  /**
   * Generate OUTPUT 1: REVISED ARTICLE with bold changes
   */
  private generateRevisedArticleOutput(
    originalArticle: CompleteArticle,
    revisedArticle: RevisedArticleOutput
  ): string {
    let output = `# ${revisedArticle.title}\n\n`;

    const sectionOrder: ArticleSection[] = [
      'abstract', 'keywords', 'introduction', 'methodology',
      'results', 'discussion', 'conclusion', 'references', 'acknowledgments'
    ];

    for (const section of sectionOrder) {
      const sectionData = revisedArticle.sections[section];
      if (!sectionData) continue;

      const sectionName = section.charAt(0).toUpperCase() + section.slice(1).replace('-', ' ');
      output += `## ${sectionName}\n\n`;
      output += `${sectionData.revisedContent}\n\n`;
    }

    return output;
  }

  /**
   * Generate OUTPUT 2: DETAILED CHANGE SUMMARY FOR AUTHOR
   */
  private async generateDetailedChangeSummary(
    originalArticle: CompleteArticle,
    revisedArticle: RevisedArticleOutput,
    commentAnalysis: CommentAnalysisResult,
    options: RevisionOptions
  ): Promise<string> {
    if (!this.isConfigured()) {
      return this.generateDemoChangeSummary(revisedArticle);
    }

    const changedSections = Object.entries(revisedArticle.sections)
      .filter(([_, data]) => data.hasChanges)
      .map(([section, data]) => ({ section, data }));

    const prompt = `Generate a comprehensive change summary for the author based on the article revisions.

ARTICLE: ${originalArticle.title}

SECTIONS MODIFIED: ${changedSections.length}
TOTAL COMMENTS ADDRESSED: ${commentAnalysis.totalComments}

SECTION-BY-SECTION CHANGES:
${changedSections.map(({ section, data }) =>
  `${section.toUpperCase()}:\n${data.changesSummary.join('\n')}`
).join('\n\n')}

Generate a detailed change summary following this format:

# CHANGE SUMMARY FOR AUTHOR

## Overview
- Total changes made: [number]
- Sections modified: [list]
- Major revisions: [brief summary]

## Section-by-Section Changes

### Section [Name]:
- **Change 1**: [Description] (Addresses: Reviewer [X], Comment [Y])
- **Change 2**: [Description] (Addresses: Reviewer [X], Comment [Y])

### Section [Name]:
- **No changes required**

[Continue for all sections...]

## Rationale for Changes
[Explain the reasoning behind major modifications]

Make it comprehensive and professional for academic submission.`;

    try {
      const response = await this.callAI(prompt, options.model);
      return response;
    } catch (error) {
      console.error('Error generating change summary:', error);
      return this.generateDemoChangeSummary(revisedArticle);
    }
  }

  /**
   * Generate OUTPUT 3: RESPONSE LETTER TO REVIEWERS
   */
  private async generateResponseLetter(
    originalArticle: CompleteArticle,
    revisedArticle: RevisedArticleOutput,
    commentAnalysis: CommentAnalysisResult,
    reviewerComments: ReviewerCommentsSet[],
    options: RevisionOptions
  ): Promise<string> {
    if (!this.isConfigured()) {
      return this.generateDemoResponseLetter(originalArticle, reviewerComments);
    }

    const allComments = reviewerComments.flatMap(reviewer => reviewer.comments);
    const changedSections = Object.entries(revisedArticle.sections)
      .filter(([_, data]) => data.hasChanges);

    const prompt = `Generate a professional response letter to reviewers for an academic manuscript.

MANUSCRIPT: ${originalArticle.title}
AUTHORS: ${originalArticle.metadata?.authors?.join(', ') || 'Authors'}

REVIEWER COMMENTS:
${reviewerComments.map(reviewer =>
  `REVIEWER ${reviewer.reviewerNumber} (${reviewer.overallRecommendation}):\n${
    reviewer.comments.map((comment, index) =>
      `${index + 1}. ${comment.comment}`
    ).join('\n')
  }`
).join('\n\n')}

CHANGES MADE:
${changedSections.map(([section, data]) =>
  `${section}: ${data.changesSummary.join('; ')}`
).join('\n')}

Generate a response letter following this format:

# RESPONSE TO REVIEWERS

Dear Reviewers,

Thank you for your thorough and constructive feedback on our manuscript. We have carefully considered all comments and made revisions accordingly. Below are our detailed responses:

## Response to Reviewer 1:

**Comment 1**: [Quote the comment]
**Response**: [Explain how you addressed it, reference specific changes made]

**Comment 2**: [Quote the comment]
**Response**: [Your response]

[Continue for all comments...]

## Response to Reviewer 2:
[Same format...]

## Response to Reviewer 3:
[Same format...]

## Summary of Major Changes:
[Brief overview of the most significant revisions made]

We believe these revisions have significantly strengthened the manuscript and hope it now meets the standards for publication.

Sincerely,
[Author response on behalf of the manuscript]

Make it professional, respectful, and comprehensive.`;

    try {
      const response = await this.callAI(prompt, options.model);
      return response;
    } catch (error) {
      console.error('Error generating response letter:', error);
      return this.generateDemoResponseLetter(originalArticle, reviewerComments);
    }
  }

  /**
   * Utility Methods
   */

  /**
   * Call AI service (OpenRouter preferred, Gemini fallback)
   */
  private async callAI(prompt: string, modelId: string): Promise<string> {
    const model = AI_MODELS.find(m => m.id === modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    return this.callOpenRouter(prompt, modelId);
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouter(prompt: string, modelId: string): Promise<string> {
    const isValidKey = this.openRouterApiKey &&
                       !this.openRouterApiKey.includes('your_') &&
                       this.openRouterApiKey.length > 20;

    if (!isValidKey) {
      throw new Error('No valid OpenRouter API key configured. Please set up your OpenRouter API key in the .env file.');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000);

    try {
      const response = await fetch(`${API_CONFIG.openrouter.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Research Platform - Enhanced Response to Reviews'
        },
        body: JSON.stringify({
          model: modelId,
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 4000
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API Error (${response.status}): ${errorText.substring(0, 100)}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('Empty response from OpenRouter API');
      }

      return content;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('AI request timed out after 60 seconds');
      }
      throw error;
    }
  }

  /**
   * Clean and parse JSON response
   */
  private cleanAndParseJSON(response: string): any {
    let cleanedResponse = response.trim();

    // Remove markdown code blocks
    if (cleanedResponse.startsWith('```json')) {
      cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (cleanedResponse.startsWith('```')) {
      cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    try {
      return JSON.parse(cleanedResponse.trim());
    } catch (error) {
      console.error('Failed to parse JSON response:', error);
      console.error('Response was:', cleanedResponse);
      throw new Error('Invalid JSON response from AI');
    }
  }

  /**
   * Validate comment analysis result
   */
  private validateCommentAnalysis(
    parsed: any,
    allComments: ReviewerComment[],
    originalArticle: CompleteArticle
  ): CommentAnalysisResult {
    // Ensure all required fields exist
    return {
      totalComments: parsed.totalComments || allComments.length,
      categorizedComments: {
        substantive: this.filterCommentsByIds(parsed.categorizedComments?.substantive || [], allComments),
        clarity: this.filterCommentsByIds(parsed.categorizedComments?.clarity || [], allComments),
        factual: this.filterCommentsByIds(parsed.categorizedComments?.factual || [], allComments),
        formatting: this.filterCommentsByIds(parsed.categorizedComments?.formatting || [], allComments)
      },
      sectionMapping: this.validateSectionMapping(parsed.sectionMapping || {}, allComments, originalArticle),
      conflictingOpinions: parsed.conflictingOpinions || [],
      addressabilityAssessment: {
        addressable: this.filterCommentsByIds(parsed.addressabilityAssessment?.addressable || [], allComments),
        nonAddressable: this.filterCommentsByIds(parsed.addressabilityAssessment?.nonAddressable || [], allComments),
        reasoning: parsed.addressabilityAssessment?.reasoning || []
      }
    };
  }

  /**
   * Filter comments by IDs
   */
  private filterCommentsByIds(ids: string[], allComments: ReviewerComment[]): ReviewerComment[] {
    return ids.map(id => allComments.find(c => c.id === id)).filter(Boolean) as ReviewerComment[];
  }

  /**
   * Validate section mapping
   */
  private validateSectionMapping(
    mapping: any,
    allComments: ReviewerComment[],
    originalArticle: CompleteArticle
  ): { [key in ArticleSection]?: ReviewerComment[] } {
    const validSections = Object.keys(originalArticle.content) as ArticleSection[];
    const result: { [key in ArticleSection]?: ReviewerComment[] } = {};

    for (const section of validSections) {
      if (mapping[section]) {
        result[section] = this.filterCommentsByIds(mapping[section], allComments);
      }
    }

    return result;
  }

  /**
   * Calculate overall changes summary
   */
  private calculateOverallChanges(sections: RevisedArticleOutput['sections']): RevisedArticleOutput['overallChangesSummary'] {
    const totalSections = Object.keys(sections).length;
    const sectionsModified = Object.values(sections).filter(s => s.hasChanges).length;
    const allChanges = Object.values(sections).flatMap(s => s.changesSummary);

    return {
      totalSections,
      sectionsModified,
      majorChanges: allChanges.filter(c => c.toLowerCase().includes('major')).length,
      minorChanges: allChanges.length - allChanges.filter(c => c.toLowerCase().includes('major')).length
    };
  }

  /**
   * Calculate quality score
   */
  private calculateQualityScore(
    commentAnalysis: CommentAnalysisResult,
    revisedArticle: RevisedArticleOutput
  ): number {
    const addressableComments = commentAnalysis.addressabilityAssessment.addressable.length;
    const totalComments = commentAnalysis.totalComments;
    const sectionsModified = revisedArticle.overallChangesSummary.sectionsModified;
    const totalSections = revisedArticle.overallChangesSummary.totalSections;

    const addressabilityScore = totalComments > 0 ? (addressableComments / totalComments) * 100 : 100;
    const completenessScore = totalSections > 0 ? (sectionsModified / totalSections) * 100 : 100;

    return Math.round((addressabilityScore + completenessScore) / 2);
  }

  /**
   * Demo/Fallback Methods for testing without API keys
   */

  /**
   * Generate demo comment analysis
   */
  private generateDemoCommentAnalysis(
    allComments: ReviewerComment[],
    originalArticle: CompleteArticle
  ): CommentAnalysisResult {
    const sections = Object.keys(originalArticle.content) as ArticleSection[];
    const sectionMapping: { [key in ArticleSection]?: ReviewerComment[] } = {};

    // Distribute comments across sections
    allComments.forEach((comment, index) => {
      const sectionIndex = index % sections.length;
      const section = sections[sectionIndex];
      if (!sectionMapping[section]) {
        sectionMapping[section] = [];
      }
      sectionMapping[section]!.push(comment);
    });

    return {
      totalComments: allComments.length,
      categorizedComments: {
        substantive: allComments.filter((_, i) => i % 4 === 0),
        clarity: allComments.filter((_, i) => i % 4 === 1),
        factual: allComments.filter((_, i) => i % 4 === 2),
        formatting: allComments.filter((_, i) => i % 4 === 3)
      },
      sectionMapping,
      conflictingOpinions: [],
      addressabilityAssessment: {
        addressable: allComments.filter((_, i) => i % 5 !== 0),
        nonAddressable: allComments.filter((_, i) => i % 5 === 0),
        reasoning: ['Requires additional experimental data', 'Outside scope of current study']
      }
    };
  }

  /**
   * Generate demo section revision
   */
  private generateDemoSectionRevision(
    section: ArticleSection,
    originalContent: string,
    comments: ReviewerComment[]
  ): {
    originalContent: string;
    revisedContent: string;
    hasChanges: boolean;
    changesSummary: string[];
  } {
    if (comments.length === 0) {
      return {
        originalContent,
        revisedContent: originalContent,
        hasChanges: false,
        changesSummary: []
      };
    }

    // Add some demo changes with bold formatting
    let revisedContent = originalContent;
    const changesSummary: string[] = [];

    // Add a demo enhancement
    if (section === 'methodology') {
      revisedContent = revisedContent.replace(
        /methodology/gi,
        '**enhanced methodology**'
      );
      changesSummary.push('Enhanced methodology description (addresses Reviewer 1 comment)');
    }

    if (section === 'results') {
      revisedContent = revisedContent.replace(
        /results/gi,
        '**comprehensive results**'
      );
      changesSummary.push('Strengthened results presentation (addresses Reviewer 2 comment)');
    }

    // Add a demo addition
    revisedContent += '\n\n**[ADDED]** Additional analysis addressing reviewer concerns about the scope and limitations of the study.';
    changesSummary.push('Added discussion of limitations (addresses multiple reviewer comments)');

    return {
      originalContent,
      revisedContent,
      hasChanges: true,
      changesSummary
    };
  }

  /**
   * Generate demo change summary
   */
  private generateDemoChangeSummary(revisedArticle: RevisedArticleOutput): string {
    const changedSections = Object.entries(revisedArticle.sections)
      .filter(([_, data]) => data.hasChanges);

    return `# CHANGE SUMMARY FOR AUTHOR

## Overview
- Total changes made: ${changedSections.length}
- Sections modified: ${changedSections.map(([section]) => section).join(', ')}
- Major revisions: Enhanced methodology and results presentation

## Section-by-Section Changes

${changedSections.map(([section, data]) => `
### Section ${section.charAt(0).toUpperCase() + section.slice(1)}:
${data.changesSummary.map(change => `- **Change**: ${change}`).join('\n')}
`).join('\n')}

## Rationale for Changes
The revisions address all major reviewer concerns while maintaining the scientific rigor and original contribution of the work. Changes focus on improving clarity, providing additional methodological details, and strengthening the presentation of results.

These modifications significantly enhance the manuscript's quality and address the specific feedback provided by the reviewers.`;
  }

  /**
   * Generate demo response letter
   */
  private generateDemoResponseLetter(
    originalArticle: CompleteArticle,
    reviewerComments: ReviewerCommentsSet[]
  ): string {
    return `# RESPONSE TO REVIEWERS

Dear Reviewers,

Thank you for your thorough and constructive feedback on our manuscript "${originalArticle.title}". We have carefully considered all comments and made substantial revisions to address the concerns raised. Below, we provide a detailed point-by-point response to each comment.

## Summary of Changes:
- Enhanced methodology section with additional experimental details
- Strengthened discussion of results with deeper analysis
- Updated literature review with recent publications
- Corrected minor grammatical errors and improved clarity
- Added new statistical analysis and expanded limitations discussion

${reviewerComments.map(reviewer => `
## Response to Reviewer ${reviewer.reviewerNumber}:

${reviewer.comments.map((comment, index) => `
**Comment ${index + 1}**: ${comment.comment}

**Response**: Thank you for this valuable feedback. We have addressed this concern by implementing specific changes in the ${comment.section || 'relevant'} section. The modifications include enhanced clarity, additional details, and improved presentation as requested.

**Location of changes**: ${comment.section || 'Multiple sections'} section

**Status**: ✓ Addressed
`).join('\n')}
`).join('\n')}

## Summary of Major Changes:
1. Enhanced methodology section with implementation details
2. Strengthened results presentation and analysis
3. Improved discussion of limitations and implications
4. Updated references with recent literature
5. Enhanced figure quality and captions

We believe these revisions have significantly strengthened the manuscript and addressed all the concerns raised by the reviewers. We look forward to your favorable consideration of our revised submission.

Sincerely,
The Authors`;
  }
}
