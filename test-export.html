<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export Functionality</title>
    <script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
    <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
</head>
<body>
    <h1>Test Export Functionality</h1>
    <button onclick="testExport()">Test Export</button>
    <div id="output"></div>

    <script>
        // Test data
        const testPaper = {
            metadata: {
                title: 'Machine Learning for Climate Change Prediction and Environmental Monitoring',
                authors: ['Dr. <PERSON>', 'Dr. <PERSON>'],
                researchField: 'Computer Science',
                keywords: ['machine learning', 'climate change', 'environmental monitoring']
            },
            sections: [
                {
                    id: 'title-1',
                    title: 'Title',
                    content: 'Machine Learning for Climate Change Prediction and Environmental Monitoring',
                    citations: []
                },
                {
                    id: 'abstract-1',
                    title: 'Abstract',
                    content: 'Climate change poses an unprecedented global challenge, necessitating accurate predictive models and robust environmental monitoring strategies to inform effective mitigation and adaptation policies.\n\nTraditional climate models, while foundational, often grapple with computational intensity and the complex, non-linear interactions inherent in Earth\'s systems, leading to uncertainties in long-term projections.\n\nThis paper explores the transformative potential of machine learning (ML) techniques in augmenting and refining climate change prediction and environmental monitoring capabilities.',
                    citations: [
                        {
                            id: 'cite-1',
                            source: 'IPCC. (2021). Climate Change 2021: The Physical Science Basis. Cambridge University Press.',
                            url: 'https://www.ipcc.ch/report/ar6/wg1/'
                        }
                    ]
                },
                {
                    id: 'intro-1',
                    title: 'Introduction',
                    content: 'The urgency of addressing climate change has never been more apparent. As global temperatures continue to rise and extreme weather events become increasingly frequent, the scientific community faces the critical challenge of developing more accurate and efficient methods for climate prediction and environmental monitoring.\n\nMachine learning represents a paradigm shift in how we approach these complex environmental challenges. Unlike traditional statistical methods, ML algorithms can identify intricate patterns in vast datasets, adapt to new information, and provide insights that were previously unattainable through conventional approaches.\n\nThis research aims to demonstrate how machine learning can revolutionize our understanding and prediction of climate systems.',
                    citations: [
                        {
                            id: 'cite-2',
                            source: 'Reichstein, M., et al. (2019). Deep learning and process understanding for data-driven Earth system science. Nature, 566(7743), 195-204.',
                            url: 'https://www.nature.com/articles/s41586-019-0912-1'
                        }
                    ]
                }
            ]
        };

        // Combine paper sections function (simplified version of the fixed function)
        function combinePaperSections(sections, metadata) {
            console.log('combinePaperSections - Processing sections:', sections.length);
            
            let combinedContent = '';
            
            // Add title
            if (metadata?.title) {
                combinedContent += `# ${metadata.title}\n\n`;
            }
            
            // Process sections
            sections.forEach((section, index) => {
                if (!section.content || section.content.trim().length <= 5) {
                    console.log(`Skipping section ${section.title} - insufficient content`);
                    return;
                }
                
                // Add section heading
                if (section.title && section.title.toLowerCase() !== 'title') {
                    combinedContent += `## ${section.title}\n\n`;
                }
                
                // Add section content
                combinedContent += `${section.content.trim()}\n\n`;
                
                // Add citations if present
                if (section.citations && section.citations.length > 0) {
                    combinedContent += '### References\n\n';
                    section.citations.forEach((citation, citIndex) => {
                        combinedContent += `${citIndex + 1}. ${citation.source}\n`;
                    });
                    combinedContent += '\n';
                }
            });
            
            console.log('Combined content length:', combinedContent.length);
            console.log('Combined content preview:', combinedContent.substring(0, 200) + '...');
            
            return combinedContent;
        }

        // Markdown to DOCX function (simplified version of the fixed function)
        function markdownToDocx(title, content) {
            console.log('markdownToDocx - Processing content:', content.substring(0, 200) + '...');
            
            const documentChildren = [];

            // Add title with better formatting
            documentChildren.push(
                new docx.Paragraph({
                    children: [
                        new docx.TextRun({
                            text: title,
                            bold: true,
                            size: 32,
                            color: "2E3440"
                        }),
                    ],
                    spacing: { after: 400, before: 200 },
                    alignment: docx.AlignmentType.CENTER
                })
            );

            // Split content into paragraphs first (double line breaks indicate paragraph breaks)
            const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim());
            
            console.log('markdownToDocx - Found paragraphs:', paragraphs.length);

            // Process each paragraph
            paragraphs.forEach((paragraph, paragraphIndex) => {
                const lines = paragraph.trim().split('\n').filter(line => line.trim());
                
                // Process each line within the paragraph
                lines.forEach((line) => {
                    const trimmedLine = line.trim();
                    
                    if (!trimmedLine) return;

                    // Handle headings with proper hierarchy and spacing
                    if (trimmedLine.startsWith('### ')) {
                        documentChildren.push(
                            new docx.Paragraph({
                                children: [new docx.TextRun({
                                    text: trimmedLine.substring(4),
                                    bold: true,
                                    size: 24
                                })],
                                spacing: { before: 350, after: 180 }
                            })
                        );
                    } else if (trimmedLine.startsWith('## ')) {
                        documentChildren.push(
                            new docx.Paragraph({
                                children: [new docx.TextRun({
                                    text: trimmedLine.substring(3),
                                    bold: true,
                                    size: 28
                                })],
                                spacing: { before: 400, after: 200 }
                            })
                        );
                    } else if (trimmedLine.startsWith('# ')) {
                        documentChildren.push(
                            new docx.Paragraph({
                                children: [new docx.TextRun({
                                    text: trimmedLine.substring(2),
                                    bold: true,
                                    size: 32
                                })],
                                spacing: { before: 480, after: 240 }
                            })
                        );
                    }
                    // Regular text line
                    else {
                        documentChildren.push(
                            new docx.Paragraph({
                                children: [new docx.TextRun({ text: trimmedLine })],
                                spacing: { after: 160 },
                                alignment: docx.AlignmentType.JUSTIFY
                            })
                        );
                    }
                });

                // Add spacing between paragraphs (except for the last one)
                if (paragraphIndex < paragraphs.length - 1) {
                    documentChildren.push(
                        new docx.Paragraph({
                            children: [new docx.TextRun({ text: "" })],
                            spacing: { after: 200 }
                        })
                    );
                }
            });

            return new docx.Document({
                sections: [{
                    properties: {},
                    children: documentChildren
                }]
            });
        }

        async function testExport() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Testing export functionality...</p>';
            
            try {
                // Test the combine function
                const combinedContent = combinePaperSections(testPaper.sections, testPaper.metadata);
                output.innerHTML += `<p>✅ Combined content successfully (${combinedContent.length} characters)</p>`;
                
                // Test the Word export
                const doc = markdownToDocx(testPaper.metadata.title, combinedContent);
                output.innerHTML += '<p>✅ Created Word document successfully</p>';
                
                // Generate and download the file
                const buffer = await docx.Packer.toBlob(doc);
                const fileName = `${testPaper.metadata.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.docx`;
                
                saveAs(buffer, fileName);
                output.innerHTML += '<p>✅ Export completed successfully! File should be downloading.</p>';
                
                // Show content preview
                output.innerHTML += `<h3>Content Preview:</h3><pre style="background: #f5f5f5; padding: 10px; white-space: pre-wrap;">${combinedContent.substring(0, 500)}...</pre>`;
                
            } catch (error) {
                console.error('Export test failed:', error);
                output.innerHTML += `<p>❌ Export failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
