/**
 * RIS Parser Service
 * Parses RIS (.ris) files and extracts citation data
 */

import { 
  RawCitationData, 
  ProcessedCitationData, 
  CitationFileFormat, 
  ReferenceManagerSource,
  CitationDocumentType,
  RISEntry,
  CitationImportServiceResponse 
} from '../../types/citation-import.types';

export class RISParserService {
  /**
   * Parse RIS file content
   */
  async parseRIS(content: string): Promise<CitationImportServiceResponse<ProcessedCitationData[]>> {
    try {
      const entries = this.extractRISEntries(content);
      const citations = entries.map(entry => this.convertRISEntry(entry));
      
      return {
        success: true,
        data: citations,
        error: null,
        warnings: this.generateWarnings(citations)
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: `RIS parsing failed: ${error.message}`
      };
    }
  }

  /**
   * Extract individual RIS entries from file content
   */
  private extractRISEntries(content: string): RISEntry[] {
    const entries: RISEntry[] = [];
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    
    let currentEntry: RISEntry | null = null;
    
    for (const line of lines) {
      // Skip empty lines and comments
      if (!line || line.startsWith('#')) continue;
      
      // Parse RIS tag-value pair
      const match = line.match(/^([A-Z][A-Z0-9])\s*-\s*(.*)$/);
      if (!match) continue;
      
      const [, tag, value] = match;
      
      // Start of new entry
      if (tag === 'TY') {
        if (currentEntry) {
          entries.push(currentEntry);
        }
        currentEntry = {
          type: value.trim(),
          fields: {}
        };
        continue;
      }
      
      // End of entry
      if (tag === 'ER') {
        if (currentEntry) {
          entries.push(currentEntry);
          currentEntry = null;
        }
        continue;
      }
      
      // Add field to current entry
      if (currentEntry) {
        const cleanValue = value.trim();
        if (cleanValue) {
          if (currentEntry.fields[tag]) {
            // Handle multiple values for same tag
            if (Array.isArray(currentEntry.fields[tag])) {
              (currentEntry.fields[tag] as string[]).push(cleanValue);
            } else {
              currentEntry.fields[tag] = [currentEntry.fields[tag] as string, cleanValue];
            }
          } else {
            currentEntry.fields[tag] = cleanValue;
          }
        }
      }
    }
    
    // Add last entry if exists
    if (currentEntry) {
      entries.push(currentEntry);
    }
    
    return entries;
  }

  /**
   * Convert RIS entry to ProcessedCitationData
   */
  private convertRISEntry(entry: RISEntry): ProcessedCitationData {
    const fields = entry.fields;
    
    return {
      title: this.getFieldValue(fields.TI || fields.T1) || '',
      authors: this.parseAuthors(fields),
      publicationYear: this.parseYear(fields.PY || fields.Y1 || fields.DA),
      journal: this.getFieldValue(fields.JO || fields.JF || fields.T2) || undefined,
      volume: this.getFieldValue(fields.VL),
      issue: this.getFieldValue(fields.IS),
      pages: this.parsePages(fields.SP, fields.EP),
      doi: this.getFieldValue(fields.DO),
      abstract: this.getFieldValue(fields.AB || fields.N2),
      keywords: this.parseKeywords(fields.KW),
      documentType: this.mapRISType(entry.type),
      publisher: this.getFieldValue(fields.PB),
      isbn: this.getFieldValue(fields.SN),
      issn: this.getFieldValue(fields.SN),
      language: this.getFieldValue(fields.LA),
      url: this.getFieldValue(fields.UR || fields.L1 || fields.L2),
      tags: [],
      notes: this.getFieldValue(fields.N1),
      sourceFormat: 'ris' as CitationFileFormat,
      sourceManager: this.detectSourceManager(fields),
      processingConfidence: this.calculateConfidence(fields),
      processingErrors: [],
      processingWarnings: []
    };
  }

  /**
   * Get field value (handle both string and array)
   */
  private getFieldValue(field: string | string[] | undefined): string | undefined {
    if (!field) return undefined;
    if (Array.isArray(field)) {
      return field.join('; ');
    }
    return field;
  }

  /**
   * Parse authors from RIS fields
   */
  private parseAuthors(fields: Record<string, string | string[]>): string[] {
    const authors: string[] = [];
    
    // Primary authors (AU, A1)
    const primaryAuthors = fields.AU || fields.A1;
    if (primaryAuthors) {
      if (Array.isArray(primaryAuthors)) {
        authors.push(...primaryAuthors);
      } else {
        authors.push(primaryAuthors);
      }
    }
    
    // Secondary authors (A2, A3, A4)
    ['A2', 'A3', 'A4'].forEach(tag => {
      const secondaryAuthors = fields[tag];
      if (secondaryAuthors) {
        if (Array.isArray(secondaryAuthors)) {
          authors.push(...secondaryAuthors);
        } else {
          authors.push(secondaryAuthors);
        }
      }
    });
    
    // Clean and format author names
    return authors.map(author => {
      // Handle "Last, First" format
      if (author.includes(',')) {
        const parts = author.split(',').map(p => p.trim());
        if (parts.length >= 2) {
          return `${parts[1]} ${parts[0]}`.trim();
        }
      }
      return author.trim();
    }).filter(author => author.length > 0);
  }

  /**
   * Parse publication year
   */
  private parseYear(yearField: string | string[] | undefined): number | undefined {
    if (!yearField) return undefined;
    
    const yearStr = Array.isArray(yearField) ? yearField[0] : yearField;
    const match = yearStr.match(/(\d{4})/);
    return match ? parseInt(match[1]) : undefined;
  }

  /**
   * Parse page numbers
   */
  private parsePages(startPage?: string | string[], endPage?: string | string[]): string | undefined {
    const start = this.getFieldValue(startPage);
    const end = this.getFieldValue(endPage);
    
    if (start && end) {
      return `${start}-${end}`;
    } else if (start) {
      return start;
    }
    
    return undefined;
  }

  /**
   * Parse keywords
   */
  private parseKeywords(keywordField: string | string[] | undefined): string[] {
    if (!keywordField) return [];
    
    const keywords: string[] = [];
    
    if (Array.isArray(keywordField)) {
      keywords.push(...keywordField);
    } else {
      // Split by common delimiters
      keywords.push(...keywordField.split(/[,;|\n]/));
    }
    
    return keywords
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  }

  /**
   * Map RIS type to document type
   */
  private mapRISType(risType: string): CitationDocumentType {
    const typeMap: Record<string, CitationDocumentType> = {
      'JOUR': 'journal-article',
      'CONF': 'conference-paper',
      'CPAPER': 'conference-paper',
      'BOOK': 'book',
      'CHAP': 'book-chapter',
      'THES': 'thesis',
      'RPRT': 'report',
      'WEB': 'webpage',
      'ELEC': 'webpage',
      'PAT': 'patent',
      'UNPB': 'preprint',
      'INPR': 'preprint',
      'GEN': 'other',
      'MISC': 'other'
    };
    
    return typeMap[risType.toUpperCase()] || 'other';
  }

  /**
   * Detect source reference manager
   */
  private detectSourceManager(fields: Record<string, string | string[]>): ReferenceManagerSource {
    // Check for Mendeley-specific fields
    if (fields.DB && this.getFieldValue(fields.DB)?.toLowerCase().includes('mendeley')) {
      return 'mendeley';
    }
    
    // Check for Zotero-specific fields
    if (fields.DB && this.getFieldValue(fields.DB)?.toLowerCase().includes('zotero')) {
      return 'zotero';
    }
    
    // Check for EndNote-specific fields
    if (fields.DB && this.getFieldValue(fields.DB)?.toLowerCase().includes('endnote')) {
      return 'endnote';
    }
    
    // Check for RefWorks-specific fields
    if (fields.DB && this.getFieldValue(fields.DB)?.toLowerCase().includes('refworks')) {
      return 'refworks';
    }
    
    // Check file paths for clues
    const fileField = fields.L1 || fields.L2 || fields.UR;
    if (fileField) {
      const filePath = this.getFieldValue(fileField)?.toLowerCase() || '';
      if (filePath.includes('mendeley')) return 'mendeley';
      if (filePath.includes('zotero')) return 'zotero';
      if (filePath.includes('endnote')) return 'endnote';
    }
    
    return 'generic';
  }

  /**
   * Calculate processing confidence
   */
  private calculateConfidence(fields: Record<string, string | string[]>): number {
    let score = 0;
    const maxScore = 10;
    
    // Required fields
    if (fields.TI || fields.T1) score += 3;
    if (fields.AU || fields.A1) score += 2;
    if (fields.PY || fields.Y1 || fields.DA) score += 2;
    
    // Optional but important fields
    if (fields.JO || fields.JF || fields.T2) score += 1;
    if (fields.DO) score += 1;
    if (fields.AB || fields.N2) score += 1;
    
    return Math.min(score / maxScore, 1);
  }

  /**
   * Generate warnings for parsed citations
   */
  private generateWarnings(citations: ProcessedCitationData[]): string[] {
    const warnings: string[] = [];
    
    citations.forEach((citation, index) => {
      if (!citation.title) {
        warnings.push(`Citation ${index + 1}: Missing title`);
      }
      if (citation.authors.length === 0) {
        warnings.push(`Citation ${index + 1}: Missing authors`);
      }
      if (!citation.publicationYear) {
        warnings.push(`Citation ${index + 1}: Missing publication year`);
      }
    });
    
    return warnings;
  }
}

export const risParserService = new RISParserService();
